{% extends "layout.html" %}
{% block title %}注册 - SCUM物品代码网站{% endblock %}
{% block content %}
<div class="container auth-container">
    <div class="row justify-content-center">
        <div class="col-md-6 col-lg-5">
            <div class="auth-card">
                <div class="auth-header">
                    <h2>用户注册</h2>
                    <p>创建您的SCUM物品代码网站账号</p>
                </div>
                
                {% if error_message %}
                <div class="alert alert-danger" role="alert">
                    {{ error_message }}
                </div>
                {% endif %}
                
                <form method="post" action="{{ url_for('register') }}" class="auth-form">
                    <div class="form-group">
                        <label for="steamid">Steam ID <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="steamid" name="steamid" required>
                        <small class="form-text text-muted">请输入您的SCUM游戏Steam ID</small>
                    </div>
                    
                    <div class="form-group">
                        <label for="username">用户名 <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="username" name="username" required>
                        <small class="form-text text-muted">用户名将用于登录和显示</small>
                    </div>
                    
                    <div class="form-group">
                        <label for="password">密码 <span class="text-danger">*</span></label>
                        <input type="password" class="form-control" id="password" name="password" required>
                        <small class="form-text text-muted">密码长度至少6位</small>
                    </div>
                    
                    <div class="form-group">
                        <label for="confirm_password">确认密码 <span class="text-danger">*</span></label>
                        <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                    </div>
                    
                    <div class="form-group">
                        <div class="custom-control custom-checkbox">
                            <input type="checkbox" class="custom-control-input" id="terms" name="terms" required>
                            <label class="custom-control-label" for="terms">我已阅读并同意 <a href="#">服务条款</a> 和 <a href="#">隐私政策</a></label>
                        </div>
                    </div>
                    
                    <button type="submit" class="btn btn-primary btn-block">注册</button>
                </form>
                
                <div class="auth-footer">
                    <p>已有账号? <a href="{{ url_for('login') }}">登录</a></p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %} 