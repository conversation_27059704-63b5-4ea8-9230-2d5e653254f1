-- 添加VIP等级字段到users表
ALTER TABLE users ADD COLUMN vip_level INT DEFAULT 0 COMMENT 'VIP等级，0表示非VIP，1-9表示VIP等级';

-- 创建VIP等级配置表
CREATE TABLE vip_levels (
  level INT NOT NULL PRIMARY KEY,
  points_required INT NOT NULL COMMENT '升级所需积分',
  points_bonus_rate FLOAT NOT NULL COMMENT '积分奖励倍率',
  discount_rate FLOAT NOT NULL COMMENT '购物折扣率',
  checkin_bonus INT NOT NULL COMMENT '签到奖励积分'
);

-- 插入VIP等级配置数据
INSERT INTO vip_levels (level, points_required, points_bonus_rate, discount_rate, checkin_bonus)
VALUES 
(1, 200000, 1.2, 0.9, 1200),
(2, 400000, 1.4, 0.8, 1400),
(3, 600000, 1.6, 0.7, 1600),
(4, 800000, 1.8, 0.6, 1800),
(5, 1000000, 2.0, 0.5, 2000),
(6, 1200000, 2.2, 0.4, 2200),
(7, 1400000, 2.4, 0.3, 2400),
(8, 1600000, 2.6, 0.2, 2600),
(9, 1800000, 2.8, 0.1, 2800);

-- 更新现有VIP用户的等级
UPDATE users SET vip_level = 1 WHERE is_vip = 1 AND vip_level = 0;

-- 创建VIP购买记录表
CREATE TABLE vip_purchase_logs (
  id INT NOT NULL AUTO_INCREMENT PRIMARY KEY,
  steamid VARCHAR(255) NOT NULL,
  purchase_time DATETIME NOT NULL,
  vip_level INT NOT NULL,
  points_spent INT NOT NULL,
  FOREIGN KEY (steamid) REFERENCES users(steamid)
);
