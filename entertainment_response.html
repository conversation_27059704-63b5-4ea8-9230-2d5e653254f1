<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>娱乐 - SCUM物品代码网站</title>
    <link rel="stylesheet" href="/static/css/style.css">
    <link rel="stylesheet" href="{{ url_for('static', filename='vendor/fontawesome/all.min.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='vendor/bootstrap/bootstrap.min.css') }}">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="main-nav">
        <div class="container nav-container">
            <div class="logo">
                <a href="/">
                    <i class="fas fa-gamepad"></i>
                    <span>7788商城</span>
                </a>
            </div>
            <ul class="nav-menu">
                <li class="nav-item"><a href="/home" class="nav-link "><i class="fas fa-home"></i> 主页</a></li>
                <li class="nav-item"><a href="/items" class="nav-link "><i class="fas fa-cube"></i> 物品代码</a></li>
                <li class="nav-item"><a href="/sponsor" class="nav-link "><i class="fas fa-heart"></i> 赞助</a></li>
                <li class="nav-item"><a href="/entertainment" class="nav-link active"><i class="fas fa-dice"></i> 娱乐</a></li>
                <li class="nav-item"><a href="/profile" class="nav-link "><i class="fas fa-user"></i> 个人信息</a></li>
            </ul>
            <div class="search-box">
                <form action="/search" method="GET">
                    <input type="text" name="q" placeholder="搜索物品..." >
                    <button type="submit"><i class="fas fa-search"></i></button>
                </form>
            </div>
        </div>
    </nav>
    
    <!-- 主内容区 -->
    <main class="container">
        
<div class="container fun-container">
    <div class="fun-header">
        <div class="fun-header-content text-center">
            <h1>SCUM娱乐中心</h1>
            <p>放松一下，享受游戏的乐趣</p>
        </div>
    </div>

    <div class="row fun-features">
        <div class="col-md-4">
            <div class="fun-feature-card">
                <div class="feature-icon">
                    <i class="fas fa-dice"></i>
                </div>
                <h3>随机物品生成器</h3>
                <p>不知道要什么物品？让我们帮你随机选择吧！</p>
                <button class="btn btn-primary fun-button" id="random-item-btn">生成随机物品</button>
                <div class="random-item-result d-none mt-3">
                    <div class="result-item">
                        <img src="" alt="随机物品" class="random-item-img">
                        <h4 class="random-item-name"></h4>
                        <div class="random-item-code"></div>
                        <button class="btn btn-sm btn-copy mt-2">
                            <i class="fas fa-copy"></i> 复制代码
                        </button>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="fun-feature-card">
                <div class="feature-icon">
                    <i class="fas fa-trophy"></i>
                </div>
                <h3>SCUM知识问答</h3>
                <p>测试你对SCUM的了解程度，赢取积分兑换奖励！</p>
                <button class="btn btn-primary fun-button" id="quiz-btn">开始问答</button>
                <div class="quiz-stats mt-3">
                    <div class="quiz-record">最高分: <span>85</span> 分</div>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="fun-feature-card">
                <div class="feature-icon">
                    <i class="fas fa-mask"></i>
                </div>
                <h3>角色造型挑战</h3>
                <p>使用指定物品代码创建独特的角色外观，分享并投票！</p>
                <button class="btn btn-primary fun-button" id="character-btn">查看挑战</button>
            </div>
        </div>
    </div>

    <!-- 随机物品生成器模态框 -->
    <div class="modal fade" id="random-item-modal" tabindex="-1" role="dialog">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">随机物品生成器</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="options-container">
                        <h6>选择物品类别</h6>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="weapon-check" checked>
                            <label class="form-check-label" for="weapon-check">武器</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="food-check" checked>
                            <label class="form-check-label" for="food-check">食物</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="clothing-check" checked>
                            <label class="form-check-label" for="clothing-check">衣物</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="medicine-check" checked>
                            <label class="form-check-label" for="medicine-check">医疗</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="misc-check" checked>
                            <label class="form-check-label" for="misc-check">其他</label>
                        </div>
                    </div>
                    <div class="random-result-container text-center mt-4">
                        <button class="btn btn-success" id="generate-btn">生成物品</button>
                        <div class="generated-item-display mt-3 d-none">
                            <div class="item-image-container">
                                <img src="" alt="物品图片" class="generated-item-img">
                            </div>
                            <h4 class="generated-item-name mt-2"></h4>
                            <div class="generated-item-code mt-1"></div>
                            <button class="btn btn-sm btn-copy mt-2" data-code="">
                                <i class="fas fa-copy"></i> 复制代码
                            </button>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 问答游戏模态框 -->
    <div class="modal fade" id="quiz-modal" tabindex="-1" role="dialog">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">SCUM知识问答</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="quiz-container">
                        <div class="quiz-progress">
                            问题 <span id="current-question">1</span> / <span id="total-questions">10</span>
                            <div class="progress mt-2">
                                <div class="progress-bar" role="progressbar" style="width: 10%"></div>
                            </div>
                        </div>
                        <div class="quiz-question mt-3">
                            <h5 id="question-text">在SCUM中，玩家可以食用哪种野生蘑菇而不会中毒？</h5>
                            <div class="options-list mt-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="quiz-option" id="option-1" value="1">
                                    <label class="form-check-label" for="option-1">红色蘑菇</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="quiz-option" id="option-2" value="2">
                                    <label class="form-check-label" for="option-2">棕色蘑菇</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="quiz-option" id="option-3" value="3">
                                    <label class="form-check-label" for="option-3">白色蘑菇</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="quiz-option" id="option-4" value="4">
                                    <label class="form-check-label" for="option-4">所有野生蘑菇都有毒</label>
                                </div>
                            </div>
                        </div>
                        <div class="quiz-actions mt-4 text-center">
                            <button class="btn btn-primary" id="submit-answer">提交答案</button>
                        </div>
                        <div class="answer-feedback mt-3 d-none">
                            <div class="alert" id="feedback-alert">
                                <span id="feedback-text"></span>
                            </div>
                            <div class="correct-answer d-none">
                                <p>正确答案: <span id="correct-answer-text"></span></p>
                            </div>
                            <button class="btn btn-success btn-sm mt-2" id="next-question">下一题</button>
                        </div>
                    </div>
                    <div class="quiz-results d-none">
                        <div class="text-center">
                            <div class="result-icon">
                                <i class="fas fa-award fa-4x"></i>
                            </div>
                            <h4 class="mt-3">问答完成！</h4>
                            <p>您的得分: <span id="quiz-score">0</span> / <span id="quiz-total">10</span></p>
                            <div class="score-comment mt-2">太棒了！您是SCUM专家！</div>
                            <button class="btn btn-primary mt-3" id="restart-quiz">再来一次</button>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 角色造型挑战区 -->
    <div class="challenge-section mt-5">
        <div class="section-header text-center">
            <h2>本周角色造型挑战</h2>
            <p>主题: 末日幸存者</p>
        </div>
        <div class="challenge-description mt-4">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">挑战说明</h5>
                    <p class="card-text">使用以下物品代码创建一个末日幸存者角色，拍摄角色截图并上传。最受欢迎的造型将获得奖励！</p>
                    <div class="required-items mt-3">
                        <h6>必备物品:</h6>
                        <ul class="list-group list-group-flush">
                            <li class="list-group-item">
                                <span class="item-name">破旧的夹克</span>
                                <span class="item-code">#夹克代码123</span>
                                <button class="btn btn-sm btn-copy float-right" data-code="#夹克代码123">
                                    <i class="fas fa-copy"></i>
                                </button>
                            </li>
                            <li class="list-group-item">
                                <span class="item-name">军用背包</span>
                                <span class="item-code">#背包代码456</span>
                                <button class="btn btn-sm btn-copy float-right" data-code="#背包代码456">
                                    <i class="fas fa-copy"></i>
                                </button>
                            </li>
                            <li class="list-group-item">
                                <span class="item-name">皮制手套</span>
                                <span class="item-code">#手套代码789</span>
                                <button class="btn btn-sm btn-copy float-right" data-code="#手套代码789">
                                    <i class="fas fa-copy"></i>
                                </button>
                            </li>
                        </ul>
                    </div>
                </div>
                <div class="card-footer">
                    <div class="d-flex justify-content-between">
                        <span>截止日期: 2024年5月30日</span>
                        <button class="btn btn-primary" id="submit-challenge">提交作品</button>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="challenge-entries mt-5">
            <h3 class="text-center mb-4">参赛作品</h3>
            <div class="row">
                
                <div class="col-md-4">
                    <div class="entry-card">
                        <div class="entry-image">
                            <img src="static/images/character-placeholder.jpg" alt="角色造型">
                        </div>
                        <div class="entry-details p-3">
                            <h5>幸存者1</h5>
                            <p class="entry-description">这个角色使用了所有必备物品，并加入了独特的头盔和面具，突显末日氛围。</p>
                            <div class="entry-votes">
                                <span class="vote-count">15</span> 票
                                <button class="btn btn-sm btn-vote float-right">
                                    <i class="fas fa-thumbs-up"></i> 投票
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <div class="entry-card">
                        <div class="entry-image">
                            <img src="static/images/character-placeholder.jpg" alt="角色造型">
                        </div>
                        <div class="entry-details p-3">
                            <h5>幸存者2</h5>
                            <p class="entry-description">这个角色使用了所有必备物品，并加入了独特的头盔和面具，突显末日氛围。</p>
                            <div class="entry-votes">
                                <span class="vote-count">22</span> 票
                                <button class="btn btn-sm btn-vote float-right">
                                    <i class="fas fa-thumbs-up"></i> 投票
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <div class="entry-card">
                        <div class="entry-image">
                            <img src="static/images/character-placeholder.jpg" alt="角色造型">
                        </div>
                        <div class="entry-details p-3">
                            <h5>幸存者3</h5>
                            <p class="entry-description">这个角色使用了所有必备物品，并加入了独特的头盔和面具，突显末日氛围。</p>
                            <div class="entry-votes">
                                <span class="vote-count">29</span> 票
                                <button class="btn btn-sm btn-vote float-right">
                                    <i class="fas fa-thumbs-up"></i> 投票
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                
            </div>
            <div class="text-center mt-4">
                <button class="btn btn-outline-primary">查看更多作品</button>
            </div>
        </div>
    </div>

    <!-- 趣味SCUM统计 -->
    <div class="fun-stats mt-5">
        <div class="section-header text-center">
            <h2>SCUM趣味统计</h2>
            <p>游戏数据背后的故事</p>
        </div>
        <div class="row mt-4">
            <div class="col-md-6">
                <div class="stat-card">
                    <div class="card-header">最受欢迎物品</div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="popular-items-chart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="stat-card">
                    <div class="card-header">玩家生存时间分布</div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="survival-time-chart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

    </main>
    
    <!-- 复制成功提示 -->
    <div class="copy-tooltip" id="copyTooltip">已复制到剪贴板<br>请前往游戏内指令框输入</div>
    
    <!-- 页脚 -->
    <footer class="main-footer">
        <div class="container">
            <div class="footer-bottom">
                <p>&copy; 2025 出其东门 | 保留所有权利</p>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script src="{{ url_for('static', filename='vendor/jquery/jquery.min.js') }}"></script>
    <script src="{{ url_for('static', filename='vendor/bootstrap/bootstrap.bundle.min.js') }}"></script>
    <script>
    document.addEventListener('DOMContentLoaded', function() {
        // 复制功能
        const copyButtons = document.querySelectorAll('.copy-btn');
        const copyTooltip = document.getElementById('copyTooltip');
        
        if (copyButtons.length > 0 && copyTooltip) {
            copyButtons.forEach(button => {
                button.addEventListener('click', function(e) {
                    e.stopPropagation();
                    const textToCopy = this.getAttribute('data-name') || this.getAttribute('data-code');
                    
                    // 创建临时文本区域
                    const textArea = document.createElement('textarea');
                    textArea.value = textToCopy;
                    document.body.appendChild(textArea);
                    textArea.select();
                    
                    // 复制文本
                    document.execCommand('copy');
                    
                    // 移除临时元素
                    document.body.removeChild(textArea);
                    
                    // 显示提示
                    copyTooltip.style.opacity = '1';
                    copyTooltip.style.top = (this.getBoundingClientRect().top - 60) + 'px';
                    copyTooltip.style.left = (this.getBoundingClientRect().left + this.offsetWidth / 2) + 'px';
                    
                    // 3秒后隐藏提示
                    setTimeout(function() {
                        copyTooltip.style.opacity = '0';
                    }, 2000);
                });
            });
        }
    });
    </script>
    

<script src="{{ url_for('static', filename='vendor/chartjs/chart.min.js') }}"></script>
<script>
    // 这里将添加页面的JavaScript逻辑
    $(document).ready(function() {
        // 随机物品生成器按钮
        $('#random-item-btn').click(function() {
            $('#random-item-modal').modal('show');
        });

        // 问答游戏按钮
        $('#quiz-btn').click(function() {
            $('#quiz-modal').modal('show');
        });

        // 角色造型挑战按钮
        $('#character-btn').click(function() {
            $('html, body').animate({
                scrollTop: $('.challenge-section').offset().top
            }, 500);
        });

        // 简单的图表示例
        if ($('#popular-items-chart').length) {
            new Chart($('#popular-items-chart'), {
                type: 'pie',
                data: {
                    labels: ['武器', '食物', '衣物', '医疗用品', '其他'],
                    datasets: [{
                        data: [45, 20, 15, 12, 8],
                        backgroundColor: [
                            '#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0', '#9966FF'
                        ]
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });
        }

        if ($('#survival-time-chart').length) {
            new Chart($('#survival-time-chart'), {
                type: 'bar',
                data: {
                    labels: ['<1小时', '1-5小时', '5-10小时', '10-24小时', '>24小时'],
                    datasets: [{
                        label: '玩家数量',
                        data: [15, 30, 25, 20, 10],
                        backgroundColor: '#36A2EB'
                    }]
                },
                options: {
                    responsive: true,
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        }
    });
</script>

</body>
</html> 