#!/usr/bin/env python3
"""
为 battlepass_rewards 表添加 image_path 字段
"""

import pymysql

def add_image_path_column():
    conn = pymysql.connect(
        host='localhost',
        user='root',
        password='123456',
        database='scum',
        charset='utf8mb4',
        cursorclass=pymysql.cursors.DictCursor
    )
    
    try:
        with conn.cursor() as cursor:
            # 检查字段是否已存在
            cursor.execute("SHOW COLUMNS FROM battlepass_rewards LIKE 'image_path'")
            result = cursor.fetchone()
            
            if not result:
                # 添加 image_path 字段
                sql = """ALTER TABLE battlepass_rewards 
                        ADD COLUMN image_path VARCHAR(500) DEFAULT '' 
                        AFTER reward_type"""
                cursor.execute(sql)
                conn.commit()
                print("✅ 成功添加 image_path 字段到 battlepass_rewards 表")
            else:
                print("ℹ️  image_path 字段已存在")
                
    except Exception as e:
        print(f"❌ 添加字段失败: {e}")
    finally:
        conn.close()

if __name__ == "__main__":
    add_image_path_column()
