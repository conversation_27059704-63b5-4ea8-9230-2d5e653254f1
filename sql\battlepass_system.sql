-- 通行证系统数据库表结构

-- 1. 通行证等级配置表
CREATE TABLE battlepass_levels (
    level INT NOT NULL PRIMARY KEY,
    exp_required INT NOT NULL COMMENT '升级到此等级所需总经验',
    points_reward INT NOT NULL DEFAULT 0 COMMENT '升级奖励积分',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='通行证等级配置表';

-- 2. 通行证物品奖励表
CREATE TABLE battlepass_rewards (
    id INT NOT NULL AUTO_INCREMENT PRIMARY KEY,
    level INT NOT NULL COMMENT '等级',
    item_id VARCHAR(255) DEFAULT NULL COMMENT '物品ID',
    item_name VARCHAR(255) NOT NULL COMMENT '物品名称',
    quantity INT NOT NULL DEFAULT 1 COMMENT '数量',
    reward_type ENUM('points', 'item') NOT NULL DEFAULT 'item' COMMENT '奖励类型：积分或物品',
    is_active TINYINT(1) NOT NULL DEFAULT 1 COMMENT '是否激活',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_level (level)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='通行证物品奖励表';

-- 3. 用户通行证进度表
CREATE TABLE user_battlepass (
    steamid VARCHAR(255) NOT NULL PRIMARY KEY,
    current_level INT NOT NULL DEFAULT 1 COMMENT '当前等级',
    current_exp INT NOT NULL DEFAULT 0 COMMENT '当前经验',
    total_exp INT NOT NULL DEFAULT 0 COMMENT '总经验',
    last_quest_date DATE DEFAULT NULL COMMENT '最后完成任务日期',
    daily_quest_count INT NOT NULL DEFAULT 0 COMMENT '今日完成任务次数',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (steamid) REFERENCES users(steamid) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户通行证进度表';

-- 4. 任务完成记录表
CREATE TABLE quest_completion_logs (
    id INT NOT NULL AUTO_INCREMENT PRIMARY KEY,
    steamid VARCHAR(255) NOT NULL,
    quest_name VARCHAR(255) NOT NULL COMMENT '任务名称',
    exp_gained INT NOT NULL COMMENT '获得经验',
    completed_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_steamid_date (steamid, completed_at),
    FOREIGN KEY (steamid) REFERENCES users(steamid) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='任务完成记录表';

-- 5. 通行证奖励领取记录表
CREATE TABLE battlepass_reward_claims (
    id INT NOT NULL AUTO_INCREMENT PRIMARY KEY,
    steamid VARCHAR(255) NOT NULL,
    level INT NOT NULL COMMENT '等级',
    reward_id INT NOT NULL COMMENT '奖励ID',
    item_name VARCHAR(255) NOT NULL COMMENT '物品名称',
    quantity INT NOT NULL DEFAULT 1 COMMENT '数量',
    claimed_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_steamid (steamid),
    FOREIGN KEY (steamid) REFERENCES users(steamid) ON DELETE CASCADE,
    FOREIGN KEY (reward_id) REFERENCES battlepass_rewards(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='通行证奖励领取记录表';

-- 6. 系统配置表（如果不存在）
CREATE TABLE IF NOT EXISTS system_config (
    config_key VARCHAR(50) PRIMARY KEY,
    config_value TEXT NOT NULL,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统配置表';

-- 插入通行证系统配置
INSERT INTO system_config (config_key, config_value) VALUES 
('battlepass_max_level', '50'),
('battlepass_daily_quest_limit', '20'),
('battlepass_quest_exp', '1000'),
('battlepass_enabled', '1')
ON DUPLICATE KEY UPDATE config_value = VALUES(config_value);

-- 插入默认通行证等级配置（1-50级）
INSERT INTO battlepass_levels (level, exp_required, points_reward) VALUES
(1, 0, 0),
(2, 2000, 1000),
(3, 5000, 1000),
(4, 9000, 1000),
(5, 14000, 2000),
(6, 20000, 1000),
(7, 27000, 1000),
(8, 35000, 1000),
(9, 44000, 1000),
(10, 54000, 3000),
(11, 65000, 1000),
(12, 77000, 1000),
(13, 90000, 1000),
(14, 104000, 1000),
(15, 119000, 4000),
(16, 135000, 1000),
(17, 152000, 1000),
(18, 170000, 1000),
(19, 189000, 1000),
(20, 209000, 5000),
(21, 230000, 1000),
(22, 252000, 1000),
(23, 275000, 1000),
(24, 299000, 1000),
(25, 324000, 6000),
(26, 350000, 1000),
(27, 377000, 1000),
(28, 405000, 1000),
(29, 434000, 1000),
(30, 464000, 7000),
(31, 495000, 1000),
(32, 527000, 1000),
(33, 560000, 1000),
(34, 594000, 1000),
(35, 629000, 8000),
(36, 665000, 1000),
(37, 702000, 1000),
(38, 740000, 1000),
(39, 779000, 1000),
(40, 819000, 10000),
(41, 860000, 1000),
(42, 902000, 1000),
(43, 945000, 1000),
(44, 989000, 1000),
(45, 1034000, 12000),
(46, 1080000, 1000),
(47, 1127000, 1000),
(48, 1175000, 1000),
(49, 1224000, 1000),
(50, 1274000, 15000);

-- 插入默认通行证奖励配置（示例）
INSERT INTO battlepass_rewards (level, item_id, item_name, quantity, reward_type) VALUES
(2, 'weapon_ak47', 'AK47突击步枪', 1, 'item'),
(3, 'ammo_762', '7.62mm弹药', 100, 'item'),
(4, 'medkit', '医疗包', 5, 'item'),
(5, 'armor_vest', '防弹衣', 1, 'item'),
(6, 'food_mre', '军用口粮', 10, 'item'),
(7, 'tool_lockpick', '撬锁工具', 3, 'item'),
(8, 'weapon_sniper', '狙击步枪', 1, 'item'),
(9, 'ammo_556', '5.56mm弹药', 200, 'item'),
(10, 'vehicle_parts', '载具零件', 5, 'item'),
(15, 'weapon_rpg', '火箭筒', 1, 'item'),
(20, 'rare_materials', '稀有材料', 10, 'item'),
(25, 'legendary_weapon', '传奇武器', 1, 'item'),
(30, 'epic_armor', '史诗护甲', 1, 'item'),
(35, 'master_toolkit', '大师工具包', 1, 'item'),
(40, 'ultimate_weapon', '终极武器', 1, 'item'),
(45, 'legendary_vehicle', '传奇载具', 1, 'item'),
(50, 'champion_reward', '冠军奖励包', 1, 'item');
