import logging
import json
from datetime import datetime, date
from flask import Blueprint, render_template, request, jsonify, session, redirect, url_for, flash

# 创建蓝图
battlepass_bp = Blueprint('battlepass', __name__)

# 导入必要的函数（这些函数将在app.py中导入时被设置）
def get_db_connection():
    """This function will be imported from app at runtime"""
    pass

def get_user_by_steamid(steamid, use_cache=True):
    """This function will be imported from app at runtime"""
    pass

def clear_user_cache(steamid):
    """This function will be imported from app at runtime"""
    pass

# 设置日志
logger = logging.getLogger("battlepass")

def get_battlepass_config():
    """获取通行证系统配置"""
    conn = get_db_connection()
    config = {
        'max_level': 50,
        'daily_quest_limit': 20,
        'quest_exp': 1000,
        'enabled': True
    }
    
    if conn:
        try:
            with conn.cursor() as cursor:
                sql = """SELECT config_key, config_value FROM system_config 
                        WHERE config_key LIKE 'battlepass_%'"""
                cursor.execute(sql)
                results = cursor.fetchall()
                
                for item in results:
                    key = item['config_key'].replace('battlepass_', '')
                    if key == 'enabled':
                        config[key] = item['config_value'] == '1'
                    else:
                        config[key] = int(item['config_value'])
        except Exception as e:
            logger.error(f"获取通行证配置失败: {e}")
        finally:
            conn.close()
    
    return config

def get_user_battlepass(steamid):
    """获取用户通行证进度"""
    conn = get_db_connection()
    if not conn:
        return None
    
    try:
        with conn.cursor() as cursor:
            sql = """SELECT * FROM user_battlepass WHERE steamid = %s"""
            cursor.execute(sql, (steamid,))
            result = cursor.fetchone()
            
            if not result:
                # 创建新的通行证记录
                sql = """INSERT INTO user_battlepass (steamid, current_level, current_exp, total_exp) 
                        VALUES (%s, 1, 0, 0)"""
                cursor.execute(sql, (steamid,))
                conn.commit()
                
                # 重新查询
                sql = """SELECT * FROM user_battlepass WHERE steamid = %s"""
                cursor.execute(sql, (steamid,))
                result = cursor.fetchone()
            
            return result
    except Exception as e:
        logger.error(f"获取用户通行证进度失败: {e}")
        return None
    finally:
        conn.close()

def get_battlepass_levels():
    """获取所有通行证等级配置"""
    conn = get_db_connection()
    levels = []
    
    if conn:
        try:
            with conn.cursor() as cursor:
                sql = """SELECT * FROM battlepass_levels ORDER BY level ASC"""
                cursor.execute(sql)
                levels = cursor.fetchall()
        except Exception as e:
            logger.error(f"获取通行证等级配置失败: {e}")
        finally:
            conn.close()
    
    return levels

def get_battlepass_rewards(level=None):
    """获取通行证奖励配置"""
    conn = get_db_connection()
    rewards = []
    
    if conn:
        try:
            with conn.cursor() as cursor:
                if level:
                    sql = """SELECT * FROM battlepass_rewards WHERE level = %s AND is_active = 1"""
                    cursor.execute(sql, (level,))
                else:
                    sql = """SELECT * FROM battlepass_rewards WHERE is_active = 1 ORDER BY level ASC"""
                    cursor.execute(sql)
                rewards = cursor.fetchall()
        except Exception as e:
            logger.error(f"获取通行证奖励配置失败: {e}")
        finally:
            conn.close()
    
    return rewards

def calculate_level_from_exp(total_exp, levels):
    """根据总经验计算等级"""
    current_level = 1
    for level_config in levels:
        if total_exp >= level_config['exp_required']:
            current_level = level_config['level']
        else:
            break
    return current_level

def complete_quest(steamid, quest_name):
    """完成任务，获得经验"""
    config = get_battlepass_config()
    
    if not config['enabled']:
        return False, "通行证系统未启用", None
    
    conn = get_db_connection()
    if not conn:
        return False, "数据库连接失败", None
    
    try:
        # 检查用户是否存在
        user = get_user_by_steamid(steamid)
        if not user:
            return False, "用户不存在", None
        
        # 获取用户通行证进度
        battlepass = get_user_battlepass(steamid)
        if not battlepass:
            return False, "获取通行证进度失败", None
        
        # 检查今日任务完成次数
        today = date.today()
        last_quest_date = battlepass.get('last_quest_date')
        daily_count = battlepass.get('daily_quest_count', 0)
        
        # 如果是新的一天，重置计数
        if last_quest_date != today:
            daily_count = 0
        
        # 检查是否超过每日限制
        if daily_count >= config['daily_quest_limit']:
            return False, f"今日任务完成次数已达上限({config['daily_quest_limit']}次)", None
        
        # 获取等级配置
        levels = get_battlepass_levels()
        if not levels:
            return False, "获取等级配置失败", None
        
        # 开始事务
        conn.begin()
        
        # 计算经验奖励
        exp_gained = config['quest_exp']
        new_total_exp = battlepass['total_exp'] + exp_gained
        new_daily_count = daily_count + 1
        
        # 计算新等级
        old_level = battlepass['current_level']
        new_level = calculate_level_from_exp(new_total_exp, levels)
        
        # 更新用户通行证进度
        with conn.cursor() as cursor:
            sql = """UPDATE user_battlepass 
                    SET current_level = %s, total_exp = %s, 
                        last_quest_date = %s, daily_quest_count = %s,
                        updated_at = NOW()
                    WHERE steamid = %s"""
            cursor.execute(sql, (new_level, new_total_exp, today, new_daily_count, steamid))
        
        # 记录任务完成日志
        with conn.cursor() as cursor:
            sql = """INSERT INTO quest_completion_logs (steamid, quest_name, exp_gained) 
                    VALUES (%s, %s, %s)"""
            cursor.execute(sql, (steamid, quest_name, exp_gained))
        
        # 检查是否升级，发放奖励
        rewards_given = []
        total_points_reward = 0
        
        if new_level > old_level:
            # 升级了，发放所有中间等级的奖励
            for level in range(old_level + 1, new_level + 1):
                # 发放积分奖励
                level_config = next((l for l in levels if l['level'] == level), None)
                if level_config and level_config['points_reward'] > 0:
                    total_points_reward += level_config['points_reward']
                
                # 发放物品奖励
                level_rewards = get_battlepass_rewards(level)
                for reward in level_rewards:
                    # 添加到用户仓库
                    with conn.cursor() as cursor:
                        sql = """INSERT INTO user_inventory 
                                (steamid, item_name, item_id, quantity, purchase_date) 
                                VALUES (%s, %s, %s, %s, NOW())"""
                        cursor.execute(sql, (steamid, reward['item_name'], 
                                           reward['item_id'], reward['quantity']))
                    
                    # 记录奖励领取
                    with conn.cursor() as cursor:
                        sql = """INSERT INTO battlepass_reward_claims 
                                (steamid, level, reward_id, item_name, quantity) 
                                VALUES (%s, %s, %s, %s, %s)"""
                        cursor.execute(sql, (steamid, level, reward['id'], 
                                           reward['item_name'], reward['quantity']))
                    
                    rewards_given.append({
                        'level': level,
                        'item_name': reward['item_name'],
                        'quantity': reward['quantity'],
                        'reward_type': reward['reward_type']
                    })
        
        # 发放积分奖励
        if total_points_reward > 0:
            with conn.cursor() as cursor:
                sql = """UPDATE users SET points = points + %s WHERE steamid = %s"""
                cursor.execute(sql, (total_points_reward, steamid))
        
        # 提交事务
        conn.commit()
        
        # 清除用户缓存
        clear_user_cache(steamid)
        
        # 获取更新后的用户信息
        updated_user = get_user_by_steamid(steamid, use_cache=False)
        updated_battlepass = get_user_battlepass(steamid)
        
        result = {
            'exp_gained': exp_gained,
            'total_exp': new_total_exp,
            'old_level': old_level,
            'new_level': new_level,
            'level_up': new_level > old_level,
            'points_reward': total_points_reward,
            'rewards_given': rewards_given,
            'daily_quest_count': new_daily_count,
            'daily_quest_limit': config['daily_quest_limit'],
            'user_points': updated_user['points'] if updated_user else 0
        }
        
        return True, "任务完成成功", result
        
    except Exception as e:
        logger.error(f"完成任务失败: {e}")
        if conn:
            conn.rollback()
        return False, f"完成任务失败: {str(e)}", None
    finally:
        if conn:
            conn.close()

# API路由
@battlepass_bp.route('/api/quest/complete', methods=['POST'])
def api_complete_quest():
    """API接口：完成任务"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({'success': False, 'message': '请求数据格式错误'}), 400

        steam_id = data.get('steam_id')
        quest_name = data.get('quest_name')

        if not steam_id or not quest_name:
            return jsonify({'success': False, 'message': '缺少必要参数'}), 400

        # 完成任务
        success, message, result = complete_quest(steam_id, quest_name)

        if success:
            return jsonify({
                'success': True,
                'message': message,
                'data': result
            })
        else:
            return jsonify({'success': False, 'message': message}), 400

    except Exception as e:
        logger.error(f"API完成任务失败: {e}")
        return jsonify({'success': False, 'message': '服务器内部错误'}), 500

@battlepass_bp.route('/battlepass')
def battlepass_page():
    """通行证页面"""
    if 'steamid' not in session:
        return redirect(url_for('login'))

    steamid = session.get('steamid')

    # 获取用户通行证进度
    battlepass = get_user_battlepass(steamid)
    if not battlepass:
        flash('获取通行证信息失败')
        return redirect(url_for('home'))

    # 获取等级配置
    levels = get_battlepass_levels()

    # 获取奖励配置
    rewards = get_battlepass_rewards()

    # 获取系统配置
    config = get_battlepass_config()

    # 计算当前等级进度
    current_level = battlepass['current_level']
    total_exp = battlepass['total_exp']

    # 找到当前等级和下一等级的经验需求
    current_level_config = next((l for l in levels if l['level'] == current_level), None)
    next_level_config = next((l for l in levels if l['level'] == current_level + 1), None)

    current_level_exp = current_level_config['exp_required'] if current_level_config else 0
    next_level_exp = next_level_config['exp_required'] if next_level_config else total_exp

    # 计算当前等级进度百分比
    if next_level_exp > current_level_exp:
        level_progress = ((total_exp - current_level_exp) / (next_level_exp - current_level_exp)) * 100
        level_progress = min(100, max(0, level_progress))
    else:
        level_progress = 100

    # 获取已领取的奖励
    claimed_rewards = get_claimed_rewards(steamid)

    # 组织奖励数据
    rewards_by_level = {}
    for reward in rewards:
        level = reward['level']
        if level not in rewards_by_level:
            rewards_by_level[level] = []
        rewards_by_level[level].append(reward)

    return render_template('battlepass.html',
                          active_page='battlepass',
                          battlepass=battlepass,
                          levels=levels,
                          rewards_by_level=rewards_by_level,
                          config=config,
                          current_level_exp=current_level_exp,
                          next_level_exp=next_level_exp,
                          level_progress=level_progress,
                          claimed_rewards=claimed_rewards)

def get_claimed_rewards(steamid):
    """获取用户已领取的奖励"""
    conn = get_db_connection()
    claimed = []

    if conn:
        try:
            with conn.cursor() as cursor:
                sql = """SELECT level, reward_id, item_name, quantity, claimed_at
                        FROM battlepass_reward_claims
                        WHERE steamid = %s
                        ORDER BY level ASC, claimed_at DESC"""
                cursor.execute(sql, (steamid,))
                claimed = cursor.fetchall()
        except Exception as e:
            logger.error(f"获取已领取奖励失败: {e}")
        finally:
            conn.close()

    return claimed

# 管理员功能
@battlepass_bp.route('/admin/battlepass')
def admin_battlepass():
    """通行证管理页面"""
    if 'steamid' not in session:
        return redirect(url_for('login'))

    # 检查管理员权限
    user = get_user_by_steamid(session.get('steamid'))
    if not user or not user.get('is_admin'):
        flash('权限不足')
        return redirect(url_for('home'))

    # 获取配置
    config = get_battlepass_config()
    levels = get_battlepass_levels()
    rewards = get_battlepass_rewards()

    # 获取商城物品数据用于奖励配置
    try:
        from app import get_all_data
        all_data = get_all_data()
        shop_items = []

        if all_data and 'packages' in all_data:
            for package in all_data['packages']:
                shop_items.append({
                    'id': package.get('packageName', ''),
                    'name': package.get('packageName', ''),
                    'category': package.get('category', ''),
                    'points': package.get('points', 0)
                })
    except:
        shop_items = []

    return render_template('admin_battlepass.html',
                          active_page='admin',
                          config=config,
                          levels=levels,
                          rewards=rewards,
                          shop_items=shop_items)

@battlepass_bp.route('/admin/battlepass/config', methods=['POST'])
def admin_update_config():
    """更新通行证配置"""
    if 'steamid' not in session:
        return jsonify({'success': False, 'message': '请先登录'}), 401

    # 检查管理员权限
    user = get_user_by_steamid(session.get('steamid'))
    if not user or not user.get('is_admin'):
        return jsonify({'success': False, 'message': '权限不足'}), 403

    try:
        data = request.get_json()

        max_level = int(data.get('max_level', 50))
        daily_quest_limit = int(data.get('daily_quest_limit', 20))
        quest_exp = int(data.get('quest_exp', 1000))
        enabled = bool(data.get('enabled', True))

        # 更新配置
        conn = get_db_connection()
        if not conn:
            return jsonify({'success': False, 'message': '数据库连接失败'}), 500

        try:
            with conn.cursor() as cursor:
                configs = [
                    ('battlepass_max_level', str(max_level)),
                    ('battlepass_daily_quest_limit', str(daily_quest_limit)),
                    ('battlepass_quest_exp', str(quest_exp)),
                    ('battlepass_enabled', '1' if enabled else '0')
                ]

                for config_key, config_value in configs:
                    sql = """INSERT INTO system_config (config_key, config_value)
                            VALUES (%s, %s)
                            ON DUPLICATE KEY UPDATE config_value = VALUES(config_value)"""
                    cursor.execute(sql, (config_key, config_value))

            conn.commit()
            return jsonify({'success': True, 'message': '配置更新成功'})

        except Exception as e:
            conn.rollback()
            logger.error(f"更新通行证配置失败: {e}")
            return jsonify({'success': False, 'message': f'更新失败: {str(e)}'}), 500
        finally:
            conn.close()

    except Exception as e:
        logger.error(f"处理配置更新请求失败: {e}")
        return jsonify({'success': False, 'message': '请求处理失败'}), 500

@battlepass_bp.route('/admin/battlepass/reward', methods=['POST'])
def admin_add_reward():
    """添加奖励配置"""
    if 'steamid' not in session:
        return jsonify({'success': False, 'message': '请先登录'}), 401

    # 检查管理员权限
    user = get_user_by_steamid(session.get('steamid'))
    if not user or not user.get('is_admin'):
        return jsonify({'success': False, 'message': '权限不足'}), 403

    try:
        data = request.get_json()

        level = int(data.get('level'))
        item_id = data.get('item_id', '')
        item_name = data.get('item_name')
        quantity = int(data.get('quantity', 1))
        reward_type = data.get('reward_type', 'item')

        if not item_name:
            return jsonify({'success': False, 'message': '物品名称不能为空'}), 400

        conn = get_db_connection()
        if not conn:
            return jsonify({'success': False, 'message': '数据库连接失败'}), 500

        try:
            with conn.cursor() as cursor:
                sql = """INSERT INTO battlepass_rewards
                        (level, item_id, item_name, quantity, reward_type)
                        VALUES (%s, %s, %s, %s, %s)"""
                cursor.execute(sql, (level, item_id, item_name, quantity, reward_type))

            conn.commit()
            return jsonify({'success': True, 'message': '奖励添加成功'})

        except Exception as e:
            conn.rollback()
            logger.error(f"添加奖励失败: {e}")
            return jsonify({'success': False, 'message': f'添加失败: {str(e)}'}), 500
        finally:
            conn.close()

    except Exception as e:
        logger.error(f"处理添加奖励请求失败: {e}")
        return jsonify({'success': False, 'message': '请求处理失败'}), 500

@battlepass_bp.route('/admin/battlepass/reward/<int:reward_id>', methods=['DELETE'])
def admin_delete_reward(reward_id):
    """删除奖励配置"""
    if 'steamid' not in session:
        return jsonify({'success': False, 'message': '请先登录'}), 401

    # 检查管理员权限
    user = get_user_by_steamid(session.get('steamid'))
    if not user or not user.get('is_admin'):
        return jsonify({'success': False, 'message': '权限不足'}), 403

    try:
        conn = get_db_connection()
        if not conn:
            return jsonify({'success': False, 'message': '数据库连接失败'}), 500

        try:
            with conn.cursor() as cursor:
                sql = "DELETE FROM battlepass_rewards WHERE id = %s"
                cursor.execute(sql, (reward_id,))

            conn.commit()
            return jsonify({'success': True, 'message': '奖励删除成功'})

        except Exception as e:
            conn.rollback()
            logger.error(f"删除奖励失败: {e}")
            return jsonify({'success': False, 'message': f'删除失败: {str(e)}'}), 500
        finally:
            conn.close()

    except Exception as e:
        logger.error(f"处理删除奖励请求失败: {e}")
        return jsonify({'success': False, 'message': '请求处理失败'}), 500

# 管理员功能
@battlepass_bp.route('/admin/battlepass')
def admin_battlepass():
    """通行证管理页面"""
    if 'steamid' not in session:
        return redirect(url_for('login'))

    # 检查管理员权限
    user = get_user_by_steamid(session.get('steamid'))
    if not user or not user.get('is_admin'):
        flash('权限不足')
        return redirect(url_for('home'))

    # 获取配置
    config = get_battlepass_config()
    levels = get_battlepass_levels()
    rewards = get_battlepass_rewards()

    # 获取商城物品数据用于奖励配置
    try:
        from app import get_all_data
        all_data = get_all_data()
        shop_items = []

        if all_data and 'packages' in all_data:
            for package in all_data['packages']:
                shop_items.append({
                    'id': package.get('packageName', ''),
                    'name': package.get('packageName', ''),
                    'category': package.get('category', ''),
                    'points': package.get('points', 0)
                })
    except:
        shop_items = []

    return render_template('admin_battlepass.html',
                          active_page='admin',
                          config=config,
                          levels=levels,
                          rewards=rewards,
                          shop_items=shop_items)

@battlepass_bp.route('/admin/battlepass/config', methods=['POST'])
def admin_update_config():
    """更新通行证配置"""
    if 'steamid' not in session:
        return jsonify({'success': False, 'message': '请先登录'}), 401

    # 检查管理员权限
    user = get_user_by_steamid(session.get('steamid'))
    if not user or not user.get('is_admin'):
        return jsonify({'success': False, 'message': '权限不足'}), 403

    try:
        data = request.get_json()

        max_level = int(data.get('max_level', 50))
        daily_quest_limit = int(data.get('daily_quest_limit', 20))
        quest_exp = int(data.get('quest_exp', 1000))
        enabled = bool(data.get('enabled', True))

        # 更新配置
        conn = get_db_connection()
        if not conn:
            return jsonify({'success': False, 'message': '数据库连接失败'}), 500

        try:
            with conn.cursor() as cursor:
                configs = [
                    ('battlepass_max_level', str(max_level)),
                    ('battlepass_daily_quest_limit', str(daily_quest_limit)),
                    ('battlepass_quest_exp', str(quest_exp)),
                    ('battlepass_enabled', '1' if enabled else '0')
                ]

                for config_key, config_value in configs:
                    sql = """INSERT INTO system_config (config_key, config_value)
                            VALUES (%s, %s)
                            ON DUPLICATE KEY UPDATE config_value = VALUES(config_value)"""
                    cursor.execute(sql, (config_key, config_value))

            conn.commit()
            return jsonify({'success': True, 'message': '配置更新成功'})

        except Exception as e:
            conn.rollback()
            logger.error(f"更新通行证配置失败: {e}")
            return jsonify({'success': False, 'message': f'更新失败: {str(e)}'}), 500
        finally:
            conn.close()

    except Exception as e:
        logger.error(f"处理配置更新请求失败: {e}")
        return jsonify({'success': False, 'message': '请求处理失败'}), 500

@battlepass_bp.route('/admin/battlepass/level', methods=['POST'])
def admin_update_level():
    """更新等级配置"""
    if 'steamid' not in session:
        return jsonify({'success': False, 'message': '请先登录'}), 401

    # 检查管理员权限
    user = get_user_by_steamid(session.get('steamid'))
    if not user or not user.get('is_admin'):
        return jsonify({'success': False, 'message': '权限不足'}), 403

    try:
        data = request.get_json()

        level = int(data.get('level'))
        exp_required = int(data.get('exp_required'))
        points_reward = int(data.get('points_reward', 0))

        conn = get_db_connection()
        if not conn:
            return jsonify({'success': False, 'message': '数据库连接失败'}), 500

        try:
            with conn.cursor() as cursor:
                sql = """INSERT INTO battlepass_levels (level, exp_required, points_reward)
                        VALUES (%s, %s, %s)
                        ON DUPLICATE KEY UPDATE
                        exp_required = VALUES(exp_required),
                        points_reward = VALUES(points_reward)"""
                cursor.execute(sql, (level, exp_required, points_reward))

            conn.commit()
            return jsonify({'success': True, 'message': '等级配置更新成功'})

        except Exception as e:
            conn.rollback()
            logger.error(f"更新等级配置失败: {e}")
            return jsonify({'success': False, 'message': f'更新失败: {str(e)}'}), 500
        finally:
            conn.close()

    except Exception as e:
        logger.error(f"处理等级配置更新请求失败: {e}")
        return jsonify({'success': False, 'message': '请求处理失败'}), 500

@battlepass_bp.route('/admin/battlepass/reward', methods=['POST'])
def admin_add_reward():
    """添加奖励配置"""
    if 'steamid' not in session:
        return jsonify({'success': False, 'message': '请先登录'}), 401

    # 检查管理员权限
    user = get_user_by_steamid(session.get('steamid'))
    if not user or not user.get('is_admin'):
        return jsonify({'success': False, 'message': '权限不足'}), 403

    try:
        data = request.get_json()

        level = int(data.get('level'))
        item_id = data.get('item_id', '')
        item_name = data.get('item_name')
        quantity = int(data.get('quantity', 1))
        reward_type = data.get('reward_type', 'item')

        if not item_name:
            return jsonify({'success': False, 'message': '物品名称不能为空'}), 400

        conn = get_db_connection()
        if not conn:
            return jsonify({'success': False, 'message': '数据库连接失败'}), 500

        try:
            with conn.cursor() as cursor:
                sql = """INSERT INTO battlepass_rewards
                        (level, item_id, item_name, quantity, reward_type)
                        VALUES (%s, %s, %s, %s, %s)"""
                cursor.execute(sql, (level, item_id, item_name, quantity, reward_type))

            conn.commit()
            return jsonify({'success': True, 'message': '奖励添加成功'})

        except Exception as e:
            conn.rollback()
            logger.error(f"添加奖励失败: {e}")
            return jsonify({'success': False, 'message': f'添加失败: {str(e)}'}), 500
        finally:
            conn.close()

    except Exception as e:
        logger.error(f"处理添加奖励请求失败: {e}")
        return jsonify({'success': False, 'message': '请求处理失败'}), 500

@battlepass_bp.route('/admin/battlepass/reward/<int:reward_id>', methods=['DELETE'])
def admin_delete_reward(reward_id):
    """删除奖励配置"""
    if 'steamid' not in session:
        return jsonify({'success': False, 'message': '请先登录'}), 401

    # 检查管理员权限
    user = get_user_by_steamid(session.get('steamid'))
    if not user or not user.get('is_admin'):
        return jsonify({'success': False, 'message': '权限不足'}), 403

    try:
        conn = get_db_connection()
        if not conn:
            return jsonify({'success': False, 'message': '数据库连接失败'}), 500

        try:
            with conn.cursor() as cursor:
                sql = "DELETE FROM battlepass_rewards WHERE id = %s"
                cursor.execute(sql, (reward_id,))

            conn.commit()
            return jsonify({'success': True, 'message': '奖励删除成功'})

        except Exception as e:
            conn.rollback()
            logger.error(f"删除奖励失败: {e}")
            return jsonify({'success': False, 'message': f'删除失败: {str(e)}'}), 500
        finally:
            conn.close()

    except Exception as e:
        logger.error(f"处理删除奖励请求失败: {e}")
        return jsonify({'success': False, 'message': '请求处理失败'}), 500

# API路由
@battlepass_bp.route('/api/quest/complete', methods=['POST'])
def api_complete_quest():
    """API接口：完成任务"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({'success': False, 'message': '请求数据格式错误'}), 400

        steam_id = data.get('steam_id')
        quest_name = data.get('quest_name')

        if not steam_id or not quest_name:
            return jsonify({'success': False, 'message': '缺少必要参数'}), 400

        # 完成任务
        success, message, result = complete_quest(steam_id, quest_name)

        if success:
            return jsonify({
                'success': True,
                'message': message,
                'data': result
            })
        else:
            return jsonify({'success': False, 'message': message}), 400

    except Exception as e:
        logger.error(f"API完成任务失败: {e}")
        return jsonify({'success': False, 'message': '服务器内部错误'}), 500

@battlepass_bp.route('/battlepass')
def battlepass_page():
    """通行证页面"""
    if 'steamid' not in session:
        return redirect(url_for('login'))

    steamid = session.get('steamid')

    # 获取用户通行证进度
    battlepass = get_user_battlepass(steamid)
    if not battlepass:
        flash('获取通行证信息失败')
        return redirect(url_for('home'))

    # 获取等级配置
    levels = get_battlepass_levels()

    # 获取奖励配置
    rewards = get_battlepass_rewards()

    # 获取系统配置
    config = get_battlepass_config()

    # 计算当前等级进度
    current_level = battlepass['current_level']
    total_exp = battlepass['total_exp']

    # 找到当前等级和下一等级的经验需求
    current_level_config = next((l for l in levels if l['level'] == current_level), None)
    next_level_config = next((l for l in levels if l['level'] == current_level + 1), None)

    current_level_exp = current_level_config['exp_required'] if current_level_config else 0
    next_level_exp = next_level_config['exp_required'] if next_level_config else total_exp

    # 计算当前等级进度百分比
    if next_level_exp > current_level_exp:
        level_progress = ((total_exp - current_level_exp) / (next_level_exp - current_level_exp)) * 100
        level_progress = min(100, max(0, level_progress))
    else:
        level_progress = 100

    # 获取已领取的奖励
    claimed_rewards = get_claimed_rewards(steamid)

    # 组织奖励数据
    rewards_by_level = {}
    for reward in rewards:
        level = reward['level']
        if level not in rewards_by_level:
            rewards_by_level[level] = []
        rewards_by_level[level].append(reward)

    return render_template('battlepass.html',
                          active_page='battlepass',
                          battlepass=battlepass,
                          levels=levels,
                          rewards_by_level=rewards_by_level,
                          config=config,
                          current_level_exp=current_level_exp,
                          next_level_exp=next_level_exp,
                          level_progress=level_progress,
                          claimed_rewards=claimed_rewards)

def get_claimed_rewards(steamid):
    """获取用户已领取的奖励"""
    conn = get_db_connection()
    claimed = []

    if conn:
        try:
            with conn.cursor() as cursor:
                sql = """SELECT level, reward_id, item_name, quantity, claimed_at
                        FROM battlepass_reward_claims
                        WHERE steamid = %s
                        ORDER BY level ASC, claimed_at DESC"""
                cursor.execute(sql, (steamid,))
                claimed = cursor.fetchall()
        except Exception as e:
            logger.error(f"获取已领取奖励失败: {e}")
        finally:
            conn.close()

    return claimed

@battlepass_bp.route('/api/battlepass/info')
def api_battlepass_info():
    """API接口：获取通行证信息"""
    if 'steamid' not in session:
        return jsonify({'success': False, 'message': '请先登录'}), 401

    steamid = session.get('steamid')

    try:
        # 获取用户通行证进度
        battlepass = get_user_battlepass(steamid)
        if not battlepass:
            return jsonify({'success': False, 'message': '获取通行证信息失败'}), 400

        # 获取等级配置
        levels = get_battlepass_levels()

        # 获取奖励配置
        rewards = get_battlepass_rewards()

        # 获取系统配置
        config = get_battlepass_config()

        # 获取已领取的奖励
        claimed_rewards = get_claimed_rewards(steamid)

        return jsonify({
            'success': True,
            'data': {
                'battlepass': battlepass,
                'levels': levels,
                'rewards': rewards,
                'config': config,
                'claimed_rewards': claimed_rewards
            }
        })

    except Exception as e:
        logger.error(f"获取通行证信息失败: {e}")
        return jsonify({'success': False, 'message': '服务器内部错误'}), 500
