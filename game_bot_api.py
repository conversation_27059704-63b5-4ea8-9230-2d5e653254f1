#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
游戏机器人API接口
提供给游戏机器人调用的API接口，包括签到、查看库存和发货功能
"""

import os
import json
import logging
import pymysql
import decimal
from flask import Blueprint, request, jsonify, Response
from datetime import datetime, timedelta

# 创建蓝图
game_bot_api = Blueprint('game_bot_api', __name__)

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("game_bot_api.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("game_bot_api")

# 自定义JSON编码器，处理Decimal类型
class CustomJSONEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, decimal.Decimal):
            return float(obj)
        return super(CustomJSONEncoder, self).default(obj)

# 自定义响应函数，使用自定义JSON编码器
def custom_jsonify(data, status_code=200):
    return Response(
        json.dumps(data, cls=CustomJSONEncoder),
        status=status_code,
        mimetype='application/json'
    )

# 这些函数将在app.py中被注入
def get_db_connection():
    """This function will be imported from app at runtime"""
    pass

def get_user_by_steamid(steamid, use_cache=True):
    """This function will be imported from app at runtime"""
    pass

def clear_user_cache(steamid):
    """This function will be imported from app at runtime"""
    pass

def get_all_data():
    """This function will be imported from app at runtime"""
    pass

def send_ship_message(message):
    """This function will be imported from app at runtime"""
    pass

# 辅助函数
def validate_steamid(steamid):
    """验证steamid是否有效"""
    if not steamid:
        return False

    # 简单验证steamid格式
    if not isinstance(steamid, str):
        return False

    # 检查是否为数字
    if not steamid.isdigit():
        return False

    # 检查长度
    if len(steamid) < 10:
        return False

    return True

@game_bot_api.route('/api/bot/daily_checkin', methods=['POST'])
def daily_checkin():
    """
    每日签到接口

    参数:
        steamid: 用户的Steam ID

    返回:
        {
            "success": true/false,
            "message": "签到成功/失败原因",
            "data": {
                "points": 签到获得的积分,
                "total_points": 签到后的总积分,
                "streak_days": 连续签到天数
            }
        }
    """
    # 获取请求数据
    data = request.json
    if not data:
        return custom_jsonify({
            'success': False,
            'message': '无效的请求数据'
        }, 400)

    # 获取steamid
    steamid = data.get('steamid')
    if not validate_steamid(steamid):
        return custom_jsonify({
            'success': False,
            'message': '无效的steamid'
        }, 400)

    # 获取用户信息
    user = get_user_by_steamid(steamid)
    if not user:
        return custom_jsonify({
            'success': False,
            'message': '用户不存在'
        }, 404)

    # 检查今天是否已经签到
    conn = get_db_connection()
    if not conn:
        return custom_jsonify({
            'success': False,
            'message': '数据库连接失败'
        }, 500)

    try:
        # 检查上次签到时间
        with conn.cursor() as cursor:
            sql = """SELECT last_sign_time, checkin_streak FROM users WHERE steamid = %s"""
            cursor.execute(sql, (steamid,))
            result = cursor.fetchone()

            today = datetime.now().date()
            checkin_streak = result.get('checkin_streak', 0) if result else 0

            if result and result.get('last_sign_time'):
                last_sign_time = result.get('last_sign_time')
                last_sign_date = last_sign_time.date() if isinstance(last_sign_time, datetime) else datetime.strptime(str(last_sign_time), '%Y-%m-%d %H:%M:%S').date()

                if today == last_sign_date:
                    return custom_jsonify({
                        'success': False,
                        'message': '今天已经签到过了，请明天再来'
                    }, 400)

                # 检查是否是连续签到（昨天有签到）
                yesterday = today - timedelta(days=1)
                is_consecutive = (last_sign_date == yesterday)

                if is_consecutive:
                    # 连续签到，连续天数+1
                    checkin_streak += 1
                else:
                    # 不是连续签到，重置连续天数为1
                    checkin_streak = 1
            else:
                # 首次签到，连续天数设为1
                checkin_streak = 1

        # 更新签到状态和积分
        base_reward = 1000  # 默认签到奖励
        # 先获取用户信息
        vip_level = user.get('vip_level', 0) if isinstance(user, dict) else 0

        # 计算签到奖励
        sign_points = base_reward
        if vip_level > 0:
            sign_points = int(base_reward * (1 + vip_level * 0.2))  # VIP用户每级额外增加20%

        bonus_points = 0    # 额外奖励积分
        messages = []

        # 如果连续签到达到10天，额外奖励1000积分
        if checkin_streak > 0 and checkin_streak % 10 == 0:
            bonus_points = 1000
            if vip_level > 0:
                bonus_points = int(bonus_points * (1 + vip_level * 0.2))  # VIP用户额外奖励也有加成
            messages.append(f'恭喜您连续签到{checkin_streak}天，获得额外奖励{bonus_points}积分！')

        total_points = sign_points + bonus_points

        # 显示VIP签到奖励提示
        if vip_level > 0:
            messages.append(f'VIP{vip_level}特权：签到奖励提升至{sign_points}积分！')

        now = datetime.now()

        with conn.cursor() as cursor:
            # 更新用户积分、签到时间和连续签到天数
            sql = """UPDATE users SET points = points + %s, last_sign_time = %s, checkin_streak = %s,
                    checkin_days = checkin_days + 1 WHERE steamid = %s"""
            cursor.execute(sql, (total_points, now, checkin_streak, steamid))

            # 添加签到日志
            sql = """INSERT INTO gift_logs (steamid, gift_type, points, received_at) VALUES (%s, %s, %s, %s)"""
            cursor.execute(sql, (steamid, 0, total_points, now))  # 0表示签到奖励

        conn.commit()

        # 清除用户缓存，确保获取最新数据
        clear_user_cache(steamid)

        # 获取更新后的用户信息
        updated_user = get_user_by_steamid(steamid, use_cache=False)
        updated_points = updated_user.get('points', 0) if updated_user else 0

        success_message = f'签到成功，获得{sign_points}积分！{bonus_points > 0 and "额外奖励"+str(bonus_points)+"积分！" or ""}'
        messages.append(success_message)

        return custom_jsonify({
            'success': True,
            'message': '<br>'.join(messages),
            'data': {
                'points': int(total_points),
                'total_points': updated_points,
                'checkin_streak': checkin_streak
            }
        })

    except Exception as e:
        logger.error(f"签到失败: {e}")
        if conn:
            conn.rollback()
        return custom_jsonify({
            'success': False,
            'message': f'签到失败: {str(e)}'
        }, 500)

    finally:
        if conn:
            conn.close()

@game_bot_api.route('/api/bot/inventory', methods=['POST'])
def get_inventory():
    """
    查看库存接口

    参数:
        steamid: 用户的Steam ID

    返回:
        {
            "success": true/false,
            "message": "成功/失败原因",
            "data": {
                "items": [
                    {
                        "name": "物品名称",
                        "quantity": 未使用的物品数量
                    },
                    ...
                ],
                "total_items": 物品总数
            }
        }
    """
    # 获取请求数据
    data = request.json
    if not data:
        return custom_jsonify({
            'success': False,
            'message': '无效的请求数据'
        }, 400)

    # 获取steamid
    steamid = data.get('steamid')
    if not validate_steamid(steamid):
        return custom_jsonify({
            'success': False,
            'message': '无效的steamid'
        }, 400)

    # 获取用户信息
    user = get_user_by_steamid(steamid)
    if not user:
        return custom_jsonify({
            'success': False,
            'message': '用户不存在'
        }, 404)

    # 获取用户库存
    conn = get_db_connection()
    if not conn:
        return custom_jsonify({
            'success': False,
            'message': '数据库连接失败'
        }, 500)

    try:
        with conn.cursor() as cursor:
            # 只查询未使用的物品，并按物品名称分组
            sql = """SELECT item_name, SUM(quantity) as total_quantity
                    FROM user_inventory
                    WHERE steamid = %s AND used = 0
                    GROUP BY item_name
                    ORDER BY item_name"""
            cursor.execute(sql, (steamid,))

            items = cursor.fetchall()

            # 构建简化的物品列表，只包含名称和数量
            inventory_items = []
            for item in items:
                # 将Decimal类型转换为int或float
                quantity = item['total_quantity']
                if isinstance(quantity, decimal.Decimal):
                    quantity = int(quantity) if quantity % 1 == 0 else float(quantity)

                inventory_items.append({
                    'name': item['item_name'],
                    'quantity': quantity
                })

            return custom_jsonify({
                'success': True,
                'message': '获取库存成功',
                'data': {
                    'items': inventory_items,
                    'total_items': len(inventory_items)
                }
            })

    except Exception as e:
        logger.error(f"获取库存失败: {e}")
        return custom_jsonify({
            'success': False,
            'message': f'获取库存失败: {str(e)}'
        }, 500)

    finally:
        conn.close()

@game_bot_api.route('/api/bot/ship_item_ws', methods=['POST'])
def ship_item_ws():
    """
    发货接口

    参数:
        steamid: 用户的Steam ID
        item_name: 物品名称
        quantity: 发货数量 (可选，默认为1)

    返回:
        {
            "success": true/false,
            "message": "发货成功/失败原因",
            "data": {
                "shipped_quantity": 实际发货数量,
                "remaining_quantity": 剩余数量
            }
        }
    """
    # 获取请求数据
    data = request.json
    if not data:
        return custom_jsonify({
            'success': False,
            'message': '无效的请求数据'
        }, 400)

    # 获取参数
    steamid = data.get('steamid')
    item_name = data.get('item_name')
    quantity = int(data.get('quantity', 1))

    # 验证参数
    if not validate_steamid(steamid):
        return custom_jsonify({
            'success': False,
            'message': '无效的steamid'
        }, 400)

    if not item_name:
        return custom_jsonify({
            'success': False,
            'message': '物品名称不能为空'
        }, 400)

    if quantity <= 0:
        return custom_jsonify({
            'success': False,
            'message': '发货数量必须大于0'
        }, 400)

    # 获取用户信息
    user = get_user_by_steamid(steamid)
    if not user:
        return custom_jsonify({
            'success': False,
            'message': '用户不存在'
        }, 404)

    # 检查用户库存中是否有足够的物品
    conn = get_db_connection()
    if not conn:
        return custom_jsonify({
            'success': False,
            'message': '数据库连接失败'
        }, 500)

    try:
        with conn.cursor() as cursor:
            # 获取用户库存中未使用的指定物品
            sql = """SELECT id, quantity
                    FROM user_inventory
                    WHERE steamid = %s AND item_name = %s AND used = 0
                    ORDER BY purchase_date ASC"""
            cursor.execute(sql, (steamid, item_name))
            available_items = cursor.fetchall()

            if not available_items:
                return custom_jsonify({
                    'success': False,
                    'message': f'库存中没有未使用的物品: {item_name}'
                }, 404)

            # 计算可用数量
            total_available = sum(item['quantity'] for item in available_items)

            if total_available < quantity:
                return custom_jsonify({
                    'success': False,
                    'message': f'库存中只有 {total_available} 个 {item_name}，不足 {quantity} 个'
                }, 400)

            # 开始发货
            remaining = quantity
            shipped_items = []
            item_ids_to_mark = []

            # 开始事务
            conn.begin()

            for item in available_items:
                if remaining <= 0:
                    break

                item_id = item['id']
                item_quantity = item['quantity']

                if item_quantity <= remaining:
                    # 如果当前物品数量小于等于剩余需要发货的数量，全部标记为已使用
                    item_ids_to_mark.append(item_id)

                    shipped_items.append({
                        'id': item_id,
                        'quantity': item_quantity
                    })

                    remaining -= item_quantity
                else:
                    # 如果当前物品数量大于剩余需要发货的数量，拆分物品
                    # 1. 更新当前物品数量
                    sql = "UPDATE user_inventory SET quantity = quantity - %s WHERE id = %s"
                    cursor.execute(sql, (remaining, item_id))

                    # 2. 创建一个新的已使用物品记录
                    sql = """INSERT INTO user_inventory
                            (steamid, item_name, item_id, quantity, purchase_date, used)
                            SELECT steamid, item_name, item_id, %s, purchase_date, 1
                            FROM user_inventory WHERE id = %s"""
                    cursor.execute(sql, (remaining, item_id))

                    shipped_items.append({
                        'id': item_id,
                        'quantity': remaining
                    })

                    remaining = 0

            # 标记物品为已使用
            if item_ids_to_mark:
                placeholders = ', '.join(['%s'] * len(item_ids_to_mark))
                sql = f"UPDATE user_inventory SET used = 1 WHERE id IN ({placeholders})"
                cursor.execute(sql, item_ids_to_mark)

            # 提交事务
            conn.commit()

            # 计算实际发货数量和剩余数量
            shipped_quantity = quantity - remaining
            remaining_quantity = total_available - shipped_quantity

            # 清除用户缓存
            clear_user_cache(steamid)

            # 发送WebSocket消息，根据数量发送多个请求
            try:
                # 对每个物品发送一个WebSocket消息
                for i in range(shipped_quantity):
                    message = {
                        'steamid': steamid,
                        'item_name': item_name,
                        'quantity': 1  # 每次只发送一个物品
                    }
                    send_ship_message(message)
                    logger.info(f"已发送WebSocket消息 ({i+1}/{shipped_quantity}): {message}")
            except Exception as e:
                logger.error(f"发送WebSocket消息失败: {e}")

            return custom_jsonify({
                'success': True,
                'message': '发货成功',
                'data': {
                    'shipped_quantity': shipped_quantity,
                    'remaining_quantity': remaining_quantity,
                    'shipped_items': shipped_items
                }
            })

    except Exception as e:
        logger.error(f"发货失败: {e}")
        if conn:
            conn.rollback()
        return custom_jsonify({
            'success': False,
            'message': f'发货失败: {str(e)}'
        }, 500)

    finally:
        if conn:
            conn.close()

@game_bot_api.route('/api/bot/recycle', methods=['POST'])
def recycle_item():
    """
    回收物品接口

    参数:
        steamid: 用户的Steam ID
        item_name: 回收物品名称
        quantity: 回收数量 (可选，默认为1)
        points: 获得的积分

    返回:
        {
            "success": true/false,
            "message": "回收成功/失败原因",
            "data": {
                "recycled_quantity": 回收数量,
                "points_earned": 获得的积分,
                "total_points": 回收后的总积分
            }
        }
    """
    # 获取请求数据
    data = request.json
    if not data:
        return custom_jsonify({
            'success': False,
            'message': '无效的请求数据'
        }, 400)

    # 获取参数
    steamid = data.get('steamid')
    item_name = data.get('item_name')
    quantity = int(data.get('quantity', 1))
    points = int(data.get('points', 0))

    # 验证参数
    if not validate_steamid(steamid):
        return custom_jsonify({
            'success': False,
            'message': '无效的steamid'
        }, 400)

    if not item_name:
        return custom_jsonify({
            'success': False,
            'message': '物品名称不能为空'
        }, 400)

    if quantity <= 0:
        return custom_jsonify({
            'success': False,
            'message': '回收数量必须大于0'
        }, 400)

    if points < 0:
        return custom_jsonify({
            'success': False,
            'message': '积分不能为负数'
        }, 400)

    # 获取用户信息
    user = get_user_by_steamid(steamid)
    if not user:
        return custom_jsonify({
            'success': False,
            'message': '用户不存在'
        }, 404)

    # 连接数据库
    conn = get_db_connection()
    if not conn:
        return custom_jsonify({
            'success': False,
            'message': '数据库连接失败'
        }, 500)

    try:
        # 开始事务
        conn.begin()

        with conn.cursor() as cursor:
            # 1. 记录回收日志
            sql = """INSERT INTO recycle_logs
                    (steamid, item_name, quantity, points_earned, recycle_time)
                    VALUES (%s, %s, %s, %s, %s)"""
            now = datetime.now()
            cursor.execute(sql, (steamid, item_name, quantity, points, now))

            # 2. 更新用户积分
            sql = "UPDATE users SET points = points + %s WHERE steamid = %s"
            cursor.execute(sql, (points, steamid))

            # 3. 更新回收统计
            # 先检查是否已有该物品的统计记录
            sql = "SELECT total_recycled FROM recycle_stats WHERE item_name = %s"
            cursor.execute(sql, (item_name,))
            stat = cursor.fetchone()

            if stat:
                # 如果已有记录，更新数量
                sql = "UPDATE recycle_stats SET total_recycled = total_recycled + %s WHERE item_name = %s"
                cursor.execute(sql, (quantity, item_name))
            else:
                # 如果没有记录，创建新记录
                sql = "INSERT INTO recycle_stats (item_name, total_recycled) VALUES (%s, %s)"
                cursor.execute(sql, (item_name, quantity))

        # 提交事务
        conn.commit()

        # 清除用户缓存
        clear_user_cache(steamid)

        # 获取更新后的用户信息
        updated_user = get_user_by_steamid(steamid, use_cache=False)
        updated_points = updated_user.get('points', 0) if updated_user else 0

        return custom_jsonify({
            'success': True,
            'message': f'回收成功，获得{points}积分！',
            'data': {
                'recycled_quantity': quantity,
                'points_earned': points,
                'total_points': updated_points
            }
        })

    except Exception as e:
        logger.error(f"回收失败: {e}")
        if conn:
            conn.rollback()
        return custom_jsonify({
            'success': False,
            'message': f'回收失败: {str(e)}'
        }, 500)

    finally:
        if conn:
            conn.close()

@game_bot_api.route('/api/bot/record_kill', methods=['POST'])
def record_kill():
    """
    记录击杀信息接口

    参数:
        steamid: 击杀者的Steam ID
        victimid: 被击杀者的Steam ID
        weapon: 使用的武器
        distance: 击杀距离

    返回:
        {
            "success": true/false,
            "message": "记录成功/失败原因",
            "data": {
                "killer": {
                    "steamid": 击杀者steamid,
                    "kills": 击杀数,
                    "deaths": 死亡数,
                    "kd": KD值
                },
                "victim": {
                    "steamid": 被击杀者steamid,
                    "kills": 击杀数,
                    "deaths": 死亡数,
                    "kd": KD值
                }
            }
        }
    """
    # 获取请求数据
    data = request.json
    if not data:
        return custom_jsonify({
            'success': False,
            'message': '无效的请求数据'
        }, 400)

    # 获取参数
    steamid = data.get('steamid')  # 击杀者
    victimid = data.get('victimid')  # 被击杀者
    weapon = data.get('weapon', '')  # 武器
    distance = float(data.get('distance', 0))  # 距离

    # 验证参数
    if not validate_steamid(steamid):
        return custom_jsonify({
            'success': False,
            'message': '击杀者steamid无效'
        }, 400)

    if not validate_steamid(victimid):
        return custom_jsonify({
            'success': False,
            'message': '被击杀者steamid无效'
        }, 400)

    # 检查击杀者和被击杀者是否存在
    killer = get_user_by_steamid(steamid)
    victim = get_user_by_steamid(victimid)

    if not killer:
        return custom_jsonify({
            'success': False,
            'message': '击杀者不存在'
        }, 404)

    if not victim:
        return custom_jsonify({
            'success': False,
            'message': '被击杀者不存在'
        }, 404)

    # 连接数据库
    conn = get_db_connection()
    if not conn:
        return custom_jsonify({
            'success': False,
            'message': '数据库连接失败'
        }, 500)

    try:
        # 开始事务
        conn.begin()

        # 当前时间戳
        log_time = int(datetime.now().timestamp())

        with conn.cursor() as cursor:
            # 1. 记录击杀日志
            sql = """INSERT INTO kill_logs
                    (steamid, victimid, log_time, weapon, distance)
                    VALUES (%s, %s, %s, %s, %s)"""
            cursor.execute(sql, (steamid, victimid, log_time, weapon, distance))

            # 2. 更新击杀者的击杀数
            sql = "UPDATE users SET kills = kills + 1 WHERE steamid = %s"
            cursor.execute(sql, (steamid,))

            # 3. 更新被击杀者的死亡数
            sql = "UPDATE users SET deaths = deaths + 1 WHERE steamid = %s"
            cursor.execute(sql, (victimid,))

            # 4. 更新击杀者的KD值
            sql = """UPDATE users SET kd = CASE
                    WHEN deaths = 0 THEN kills
                    ELSE kills / deaths
                    END
                    WHERE steamid = %s"""
            cursor.execute(sql, (steamid,))

            # 5. 更新被击杀者的KD值
            sql = """UPDATE users SET kd = CASE
                    WHEN deaths = 0 THEN kills
                    ELSE kills / deaths
                    END
                    WHERE steamid = %s"""
            cursor.execute(sql, (victimid,))

        # 提交事务
        conn.commit()

        # 清除用户缓存
        clear_user_cache(steamid)
        clear_user_cache(victimid)

        # 获取更新后的用户信息
        updated_killer = get_user_by_steamid(steamid, use_cache=False)
        updated_victim = get_user_by_steamid(victimid, use_cache=False)

        # 构建响应数据
        killer_data = {
            'steamid': steamid,
            'kills': updated_killer.get('kills', 0),
            'deaths': updated_killer.get('deaths', 0),
            'kd': updated_killer.get('kd', 0)
        }

        victim_data = {
            'steamid': victimid,
            'kills': updated_victim.get('kills', 0),
            'deaths': updated_victim.get('deaths', 0),
            'kd': updated_victim.get('kd', 0)
        }

        return custom_jsonify({
            'success': True,
            'message': '击杀记录添加成功',
            'data': {
                'killer': killer_data,
                'victim': victim_data
            }
        })

    except Exception as e:
        logger.error(f"记录击杀信息失败: {e}")
        if conn:
            conn.rollback()
        return custom_jsonify({
            'success': False,
            'message': f'记录击杀信息失败: {str(e)}'
        }, 500)

    finally:
        if conn:
            conn.close()