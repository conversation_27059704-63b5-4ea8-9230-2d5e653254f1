import logging
import random
import json
from datetime import datetime
from decimal import Decimal
from flask import Blueprint, request, jsonify, current_app

# 创建蓝图
api_lottery_bp = Blueprint('api_lottery', __name__)

# 设置日志
logger = logging.getLogger("api_lottery")

# 自定义JSON编码器，处理Decimal类型
class DecimalJSONEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, Decimal):
            return float(obj)
        return super(DecimalJSONEncoder, self).default(obj)

# 这些函数将在app.py中被注入
def get_db_connection():
    """This function will be imported from app at runtime"""
    pass

def get_user_by_steamid(steamid, use_cache=True):
    """This function will be imported from app at runtime"""
    pass

def clear_user_cache(steamid):
    """This function will be imported from app at runtime"""
    pass

# 默认抽奖配置
DEFAULT_LOTTERY_CONFIG = {
    'single_cost': 1000,  # 单抽花费
    'ten_cost': 9500,     # 十连抽花费（有优惠）
    'hundred_cost': 90000 # 百连抽花费（有优惠）
}

def get_lottery_config():
    """获取抽奖配置"""
    conn = get_db_connection()
    config = DEFAULT_LOTTERY_CONFIG.copy()

    if conn:
        try:
            with conn.cursor() as cursor:
                sql = "SELECT config_key, config_value FROM system_config WHERE config_key LIKE 'lottery_%'"
                cursor.execute(sql)
                results = cursor.fetchall()

                for item in results:
                    key = item['config_key'].replace('lottery_', '')
                    if key in config:
                        config[key] = int(item['config_value'])
        except Exception as e:
            logger.error(f"获取抽奖配置失败: {e}")
        finally:
            conn.close()

    return config

def external_draw_lottery(steamid, times):
    """外部API抽奖逻辑"""
    # 获取抽奖配置
    config = get_lottery_config()
    single_cost = config['single_cost']
    total_cost = single_cost * times

    conn = get_db_connection()
    if not conn:
        return False, "数据库连接失败", None

    try:
        # 获取用户信息
        user = get_user_by_steamid(steamid)
        if not user:
            return False, "获取用户信息失败", None

        # 检查用户积分是否足够
        if user['points'] < total_cost:
            return False, "积分不足，无法抽奖", None

        # 获取所有激活的奖品
        with conn.cursor() as cursor:
            sql = """
            SELECT id, item_id, item_name, probability, image_path
            FROM lottery_prizes
            WHERE is_active = 1
            """
            cursor.execute(sql)
            prizes = cursor.fetchall()

            # 将Decimal类型转换为float
            for prize in prizes:
                if 'probability' in prize and isinstance(prize['probability'], Decimal):
                    prize['probability'] = float(prize['probability'])

        if not prizes:
            return False, "当前没有可抽取的奖品", None

        # 计算总概率
        total_probability = sum(float(prize['probability']) for prize in prizes)

        # 开始事务
        conn.begin()

        # 1. 扣除用户积分
        with conn.cursor() as cursor:
            sql = "UPDATE users SET points = points - %s WHERE steamid = %s"
            cursor.execute(sql, (total_cost, steamid))

        # 2. 进行多次抽奖
        results = []
        now = datetime.now()

        for _ in range(times):
            # 随机抽取奖品
            random_num = random.uniform(0, total_probability)
            current_sum = 0
            selected_prize = None

            for prize in prizes:
                current_sum += float(prize['probability'])
                if random_num <= current_sum:
                    selected_prize = prize
                    break

            # 如果没有选中奖品（理论上不应该发生），选择第一个奖品
            if not selected_prize and prizes:
                selected_prize = prizes[0]

            # 添加到结果列表
            results.append(selected_prize)

            # 添加到用户仓库
            with conn.cursor() as cursor:
                sql = """
                INSERT INTO user_inventory
                (steamid, item_name, item_id, quantity, purchase_date)
                VALUES (%s, %s, %s, %s, %s)
                """
                cursor.execute(sql, (steamid, selected_prize['item_name'], selected_prize['item_id'], 1, now))

            # 记录抽奖日志
            with conn.cursor() as cursor:
                sql = """
                INSERT INTO lottery_logs
                (steamid, item_id, item_name, points_cost, draw_time, draw_type)
                VALUES (%s, %s, %s, %s, %s, %s)
                """
                cursor.execute(sql, (
                    steamid,
                    selected_prize['item_id'],
                    selected_prize['item_name'],
                    single_cost,
                    now,
                    'external'
                ))

        # 提交事务
        conn.commit()

        # 清除用户缓存
        clear_user_cache(steamid)

        # 获取更新后的用户积分
        updated_user = get_user_by_steamid(steamid, use_cache=False)
        updated_points = updated_user['points'] if updated_user else 0

        # 统计结果 - 按物品名称合并相同物品
        prize_counts = {}
        for result in results:
            item_name = result['item_name']
            if item_name in prize_counts:
                prize_counts[item_name]['count'] += 1
            else:
                prize_counts[item_name] = {
                    'item_id': result['item_id'],
                    'item_name': item_name,
                    'count': 1
                }

        # 转换为列表格式
        summary = list(prize_counts.values())

        return True, "抽奖成功", {
            'items': summary,
            'total_cost': total_cost,
            'points_before': user['points'],
            'points_after': updated_points
        }

    except Exception as e:
        logger.error(f"外部API抽奖失败: {e}")
        if conn:
            conn.rollback()
        return False, f"抽奖失败: {str(e)}", None
    finally:
        if conn:
            conn.close()

@api_lottery_bp.route('/api/external/lottery/draw', methods=['POST'])
def external_lottery_draw():
    """外部API抽奖接口"""
    data = request.json
    if not data:
        return jsonify({'success': False, 'message': '无效的请求数据'})

    steamid = data.get('steamid')
    times = data.get('times', 1)

    # 验证参数
    if not steamid:
        return jsonify({'success': False, 'message': '缺少steamid参数'})

    # 确保times是整数且在合理范围内
    try:
        times = int(times)
        if times <= 0:
            return jsonify({'success': False, 'message': '抽奖次数必须大于0'})
        if times > 100:  # 限制最大抽奖次数
            return jsonify({'success': False, 'message': '单次请求抽奖次数不能超过100'})
    except (ValueError, TypeError):
        return jsonify({'success': False, 'message': '抽奖次数必须是有效的整数'})

    # 执行抽奖
    success, message, result = external_draw_lottery(steamid, times)

    # 返回结果
    if success:
        return jsonify({
            'success': True,
            'message': message,
            'data': result
        })
    else:
        return jsonify({
            'success': False,
            'message': message
        })
