{% extends "base.html" %}

{% block title %}抽奖中心 - 7788商城{% endblock %}

{% block head_extra %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/lottery.css') }}">
{% endblock %}

{% block content %}
<div class="lottery-container">
    <div class="lottery-header">
        <h1><i class="fas fa-gift"></i> 抽奖中心</h1>
        <p>试试你的运气，抽取稀有物品</p>
    </div>

    <!-- 奖品展示区 -->
    <div class="prize-showcase">
        <div class="section-header">
            <h2><i class="fas fa-trophy"></i> 奖池物品</h2>
            <p>当前可抽取的物品</p>
            <div style="margin-top: 10px;">
                <button id="expandPrizePool" class="btn btn-sm btn-outline-success" style="padding: 5px 15px; font-size: 14px;">
                    <i class="fas fa-expand-alt"></i> 展开全部奖品
                </button>
            </div>
        </div>

        <!-- 奖品滚动容器 -->
        <div id="prizeContainer" class="prize-container" style="position: relative; overflow: hidden; height: 180px; margin-bottom: 20px;">
            <!-- 滚动展示区 -->
            <div id="prizeScroller" class="prize-scroller" style="display: flex; position: absolute; left: 0;">
                {% set sorted_prizes = prizes|selectattr('is_active', 'equalto', True)|sort(attribute='probability') %}
                {% for prize in sorted_prizes %}
                <div class="prize-card" style="min-width: 150px; margin: 0 10px;">
                    <div class="prize-image">
                        <img src="{{ prize.image_path or '/static/uploads/default-item.jpg' }}" alt="{{ prize.item_name }}" style="width: 100%; height: 120px; object-fit: contain;">
                    </div>
                    <div class="prize-info" style="text-align: center; padding: 8px 0;">
                        <h3 style="font-size: 16px; margin: 0; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;">{{ prize.item_name }}</h3>
                        <!-- 概率信息已隐藏 -->
                    </div>
                </div>
                {% endfor %}
                {% if sorted_prizes|length == 0 %}
                <div class="no-prizes" style="width: 100%; text-align: center; padding: 50px 0;">
                    <p>当前没有可抽取的奖品，请稍后再试</p>
                </div>
                {% endif %}

                <!-- 复制一份奖品，用于无缝滚动 -->
                {% for prize in sorted_prizes %}
                <div class="prize-card" style="min-width: 150px; margin: 0 10px;">
                    <div class="prize-image">
                        <img src="{{ prize.image_path or '/static/uploads/default-item.jpg' }}" alt="{{ prize.item_name }}" style="width: 100%; height: 120px; object-fit: contain;">
                    </div>
                    <div class="prize-info" style="text-align: center; padding: 8px 0;">
                        <h3 style="font-size: 16px; margin: 0; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;">{{ prize.item_name }}</h3>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>

        <!-- 添加CSS动画样式 -->
        <style>
            @keyframes scrollPrizes {
                0% { transform: translateX(0); }
                100% { transform: translateX(-50%); }
            }

            #prizeScroller {
                animation: scrollPrizes 30s linear infinite;
                width: auto;
            }

            #prizeScroller:hover {
                animation-play-state: paused;
            }

            /* 当展开奖品池时暂停动画 */
            .paused-animation {
                animation-play-state: paused !important;
            }
        </style>

        <!-- 展开后的奖品网格 -->
        <div id="expandedPrizeGrid" class="prize-grid" style="display: none; grid-template-columns: repeat(auto-fill, minmax(180px, 1fr)); gap: 15px;">
            {% set sorted_prizes = prizes|selectattr('is_active', 'equalto', True)|sort(attribute='probability') %}
            {% for prize in sorted_prizes %}
            <div class="prize-card">
                <div class="prize-image">
                    <img src="{{ prize.image_path or '/static/uploads/default-item.jpg' }}" alt="{{ prize.item_name }}">
                </div>
                <div class="prize-info">
                    <h3>{{ prize.item_name }}</h3>
                    <!-- 概率信息已隐藏 -->
                </div>
            </div>
            {% endfor %}
            {% if sorted_prizes|length == 0 %}
            <div class="no-prizes">
                <p>当前没有可抽取的奖品，请稍后再试</p>
            </div>
            {% endif %}
        </div>
    </div>

    <!-- 抽奖区域 -->
    <div class="lottery-section">
        <div class="section-header">
            <h2><i class="fas fa-random"></i> 开始抽奖</h2>
            <p>选择抽奖方式，赢取心仪物品</p>
        </div>

        <div class="lottery-options">
            <div class="lottery-option">
                <div class="option-header">
                    <h3>单次抽奖</h3>
                    <div class="option-cost">
                        <span>花费: {{ config.single_cost }} 积分</span>
                    </div>
                </div>
                <div class="option-action">
                    <button class="lottery-btn" id="single-draw-btn" data-type="single" data-cost="{{ config.single_cost }}">
                        <i class="fas fa-gift"></i> 抽一次
                    </button>
                </div>
            </div>

            <div class="lottery-option">
                <div class="option-header">
                    <h3>十连抽</h3>
                    <div class="option-cost">
                        <span>花费: {{ config.ten_cost }} 积分</span>
                        <span class="discount">省 {{ config.single_cost * 10 - config.ten_cost }} 积分</span>
                    </div>
                </div>
                <div class="option-action">
                    <button class="lottery-btn" id="ten-draw-btn" data-type="ten" data-cost="{{ config.ten_cost }}">
                        <i class="fas fa-gifts"></i> 抽十次
                    </button>
                </div>
            </div>

            <div class="lottery-option">
                <div class="option-header">
                    <h3>百连抽</h3>
                    <div class="option-cost">
                        <span>花费: {{ config.hundred_cost }} 积分</span>
                        <span class="discount">省 {{ config.single_cost * 100 - config.hundred_cost }} 积分</span>
                    </div>
                </div>
                <div class="option-action">
                    <button class="lottery-btn" id="hundred-draw-btn" data-type="hundred" data-cost="{{ config.hundred_cost }}">
                        <i class="fas fa-box-open"></i> 抽一百次
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 抽奖历史 -->
    <div class="lottery-history">
        <div class="section-header">
            <h2><i class="fas fa-history"></i> 抽奖记录</h2>
            <p>查看抽奖记录和中奖情况</p>
        </div>

        <!-- 抽奖记录切换标签 -->
        <div class="history-tabs" style="display: flex; margin-bottom: 20px; border-bottom: 1px solid #333;">
            <button class="history-tab active" data-tab="user-history" style="flex: 1; padding: 12px; background: none; border: none; border-bottom: 3px solid #4CAF50; color: white; cursor: pointer; font-size: 16px;">
                <i class="fas fa-user"></i> 我的抽奖记录
            </button>
            <button class="history-tab" data-tab="global-history" style="flex: 1; padding: 12px; background: none; border: none; border-bottom: 3px solid transparent; color: #aaa; cursor: pointer; font-size: 16px;">
                <i class="fas fa-globe"></i> 全服抽奖记录
            </button>
        </div>

        <!-- 我的抽奖记录 -->
        <div id="user-history" class="history-tab-content" style="display: block;">
            <div class="history-table-container">
                <table class="history-table">
                    <thead>
                        <tr>
                            <th>时间</th>
                            <th>物品</th>
                            <th>花费</th>
                            <th>抽奖类型</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for item in user_history %}
                        <tr>
                            <td>{{ item.draw_time.strftime('%Y-%m-%d %H:%M:%S') }}</td>
                            <td>{{ item.item_name }}</td>
                            <td>{{ item.points_cost }} 积分</td>
                            <td>
                                {% if item.draw_type == 'single' %}
                                单抽
                                {% elif item.draw_type == 'ten' %}
                                十连抽
                                {% elif item.draw_type == 'hundred' %}
                                百连抽
                                {% else %}
                                {{ item.draw_type }}
                                {% endif %}
                            </td>
                        </tr>
                        {% else %}
                        <tr>
                            <td colspan="4" class="no-records">您还没有抽奖记录</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>

        <!-- 全服抽奖记录 -->
        <div id="global-history" class="history-tab-content" style="display: none;">
            <div class="history-table-container">
                <table class="history-table">
                    <thead>
                        <tr>
                            <th>时间</th>
                            <th>玩家</th>
                            <th>物品</th>
                            <th>花费</th>
                            <th>抽奖类型</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for item in global_history %}
                        <tr>
                            <td>{{ item.draw_time.strftime('%Y-%m-%d %H:%M:%S') }}</td>
                            <td>{{ item.username or '未知玩家' }}</td>
                            <td>{{ item.item_name }}</td>
                            <td>{{ item.points_cost }} 积分</td>
                            <td>
                                {% if item.draw_type == 'single' %}
                                单抽
                                {% elif item.draw_type == 'ten' %}
                                十连抽
                                {% elif item.draw_type == 'hundred' %}
                                百连抽
                                {% else %}
                                {{ item.draw_type }}
                                {% endif %}
                            </td>
                        </tr>
                        {% else %}
                        <tr>
                            <td colspan="5" class="no-records">暂无抽奖记录</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- 抽奖结果模态框 -->
    <div class="lottery-result-modal" id="lotteryResultModal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 id="lotteryResultTitle">抽奖结果</h2>
                <span class="modal-close">&times;</span>
            </div>
            <div class="modal-body">
                <div class="result-animation" id="resultAnimation">
                    <!-- 动画区域 -->
                </div>
                <div class="result-message" id="lotteryResultMessage">
                    <!-- 结果消息将通过JavaScript动态添加 -->
                </div>
            </div>
            <div class="modal-footer">
                <button class="draw-again-btn" id="drawAgainBtn">再抽一次</button>
                <button class="close-btn" id="closeResultBtn">关闭</button>
            </div>
        </div>
    </div>

    <!-- 确认抽奖对话框 -->
    <div class="confirm-dialog" id="confirmDrawDialog">
        <div class="confirm-content">
            <div class="confirm-header">
                <h3>确认抽奖</h3>
            </div>
            <div class="confirm-body">
                <p id="confirmMessage">确定要进行抽奖吗？</p>
            </div>
            <div class="confirm-footer">
                <button id="cancelDrawBtn">取消</button>
                <button id="confirmDrawBtn">确定</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // 获取DOM元素
        const singleDrawBtn = document.getElementById('single-draw-btn');
        const tenDrawBtn = document.getElementById('ten-draw-btn');
        const hundredDrawBtn = document.getElementById('hundred-draw-btn');
        const lotteryResultModal = document.getElementById('lotteryResultModal');
        const modalClose = document.querySelector('.modal-close');
        const closeResultBtn = document.getElementById('closeResultBtn');
        const drawAgainBtn = document.getElementById('drawAgainBtn');
        const resultAnimation = document.getElementById('resultAnimation');
        const lotteryResultMessage = document.getElementById('lotteryResultMessage');
        const lotteryResultTitle = document.getElementById('lotteryResultTitle');

        // 当前抽奖类型和花费
        let currentType = '';
        let currentCost = 0;

        // 绑定抽奖按钮点击事件
        singleDrawBtn.addEventListener('click', function() {
            currentType = this.getAttribute('data-type');
            currentCost = parseInt(this.getAttribute('data-cost'));
            confirmDraw(currentType, currentCost);
        });

        tenDrawBtn.addEventListener('click', function() {
            currentType = this.getAttribute('data-type');
            currentCost = parseInt(this.getAttribute('data-cost'));
            confirmDraw(currentType, currentCost);
        });

        hundredDrawBtn.addEventListener('click', function() {
            currentType = this.getAttribute('data-type');
            currentCost = parseInt(this.getAttribute('data-cost'));
            confirmDraw(currentType, currentCost);
        });

        // 关闭模态框
        modalClose.addEventListener('click', closeLotteryResult);
        closeResultBtn.addEventListener('click', closeLotteryResult);

        // 再抽一次
        drawAgainBtn.addEventListener('click', function() {
            closeLotteryResult();
            setTimeout(function() {
                confirmDraw(currentType, currentCost);
            }, 300);
        });

        // 确认抽奖
        let currentDrawType = '';
        const confirmDrawDialog = document.getElementById('confirmDrawDialog');
        const confirmMessage = document.getElementById('confirmMessage');
        const confirmDrawBtn = document.getElementById('confirmDrawBtn');
        const cancelDrawBtn = document.getElementById('cancelDrawBtn');

        function confirmDraw(type, cost) {
            currentDrawType = type;
            let message = '';
            if (type === 'single') {
                message = `确定要花费 <strong>${cost}</strong> 积分进行<strong>单次抽奖</strong>吗？`;
            } else if (type === 'ten') {
                message = `确定要花费 <strong>${cost}</strong> 积分进行<strong>十连抽</strong>吗？`;
            } else if (type === 'hundred') {
                message = `确定要花费 <strong>${cost}</strong> 积分进行<strong>百连抽</strong>吗？`;
            }

            confirmMessage.innerHTML = message;
            confirmDrawDialog.style.display = 'flex';
        }

        // 确认抽奖按钮事件
        confirmDrawBtn.addEventListener('click', function() {
            confirmDrawDialog.style.display = 'none';
            performDraw(currentDrawType);
        });

        // 取消抽奖按钮事件
        cancelDrawBtn.addEventListener('click', function() {
            confirmDrawDialog.style.display = 'none';
        });

        // 执行抽奖
        function performDraw(type) {
            fetch('/api/lottery/draw', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: JSON.stringify({
                    type: type
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showLotteryResult(type, data.result);

                    // 更新用户积分显示
                    const pointsDisplay = document.querySelector('.nav-points span');
                    if (pointsDisplay) {
                        pointsDisplay.textContent = data.result.points_after;
                    }
                } else {
                    alert(data.message);
                }
            })
            .catch(error => {
                console.error('抽奖请求失败:', error);
                alert('网络错误，请稍后重试');
            });
        }

        // 显示抽奖结果
        function showLotteryResult(type, result) {
            // 设置标题
            if (type === 'single') {
                lotteryResultTitle.textContent = '单次抽奖结果';
            } else if (type === 'ten') {
                lotteryResultTitle.textContent = '十连抽结果';
            } else if (type === 'hundred') {
                lotteryResultTitle.textContent = '百连抽结果';
            }

            // 清空之前的内容
            resultAnimation.innerHTML = '';
            lotteryResultMessage.innerHTML = '';

            // 单次抽奖结果
            if (type === 'single') {
                const prize = result.prize;

                // 创建奖品展示
                const prizeElement = document.createElement('div');
                prizeElement.className = 'single-prize';
                prizeElement.innerHTML = `
                    <div class="prize-image">
                        <img src="${prize.image_path || '/static/uploads/default-item.jpg'}"
                             alt="${prize.item_name}">
                    </div>
                    <div class="prize-name">${prize.item_name}</div>
                `;
                resultAnimation.appendChild(prizeElement);

                // 创建结果消息
                const messageElement = document.createElement('div');
                messageElement.className = 'result-detail';
                messageElement.innerHTML = `
                    <p>恭喜您获得了 <strong>${prize.item_name}</strong>！</p>
                    <p>花费: ${result.cost} 积分</p>
                    <p>剩余积分: ${result.points_after} 积分</p>
                `;
                lotteryResultMessage.appendChild(messageElement);
            }
            // 批量抽奖结果
            else {
                // 创建摘要视图
                const summaryElement = document.createElement('div');
                summaryElement.className = 'batch-summary';

                let summaryHTML = '<div class="summary-items">';

                // 对于十连抽，显示所有物品
                if (type === 'ten' && result.results) {
                    for (const item of result.results) {
                        summaryHTML += `
                            <div class="summary-item">
                                <img src="${item.image_path || '/static/uploads/default-item.jpg'}"
                                     alt="${item.item_name}">
                            </div>
                        `;
                    }
                }
                // 对于百连抽，显示统计信息
                else if (type === 'hundred' && result.summary) {
                    for (const item of result.summary) {
                        summaryHTML += `
                            <div class="summary-group">
                                <div class="group-image">
                                    <img src="${item.prize.image_path || '/static/uploads/default-item.jpg'}"
                                         alt="${item.prize.item_name}">
                                </div>
                                <div class="group-info">
                                    <div class="group-name">${item.prize.item_name}</div>
                                    <div class="group-count">x${item.count}</div>
                                </div>
                            </div>
                        `;
                    }
                }

                summaryHTML += '</div>';
                summaryElement.innerHTML = summaryHTML;
                resultAnimation.appendChild(summaryElement);

                // 创建结果消息
                const messageElement = document.createElement('div');
                messageElement.className = 'result-detail';
                messageElement.innerHTML = `
                    <p>抽奖完成！</p>
                    <p>花费: ${result.total_cost} 积分</p>
                    <p>剩余积分: ${result.points_after} 积分</p>
                `;
                lotteryResultMessage.appendChild(messageElement);
            }

            // 显示模态框
            lotteryResultModal.style.display = 'flex';
        }

        // 关闭抽奖结果模态框
        function closeLotteryResult() {
            lotteryResultModal.style.display = 'none';
        }

        // 再抽一次按钮事件
        document.getElementById('drawAgainBtn').addEventListener('click', function() {
            // 先关闭结果模态框
            lotteryResultModal.style.display = 'none';

            // 等待一下再执行抽奖，避免界面闪烁
            setTimeout(function() {
                // 根据当前抽奖类型再次确认
                if (currentDrawType === 'single') {
                    confirmDraw('single', {{ config.single_cost }});
                } else if (currentDrawType === 'ten') {
                    confirmDraw('ten', {{ config.ten_cost }});
                } else if (currentDrawType === 'hundred') {
                    confirmDraw('hundred', {{ config.hundred_cost }});
                }
            }, 300);
        });

        // 关闭按钮事件
        document.getElementById('closeResultBtn').addEventListener('click', function() {
            closeLotteryResult();

            // 刷新页面以更新抽奖历史
            setTimeout(function() {
                window.location.reload();
            }, 300);
        });

        // 抽奖记录标签切换
        const historyTabs = document.querySelectorAll('.history-tab');
        const historyContents = document.querySelectorAll('.history-tab-content');

        historyTabs.forEach(tab => {
            tab.addEventListener('click', function() {
                // 移除所有标签的活动状态
                historyTabs.forEach(t => {
                    t.classList.remove('active');
                    t.style.borderBottomColor = 'transparent';
                    t.style.color = '#aaa';
                });

                // 隐藏所有内容
                historyContents.forEach(content => {
                    content.style.display = 'none';
                });

                // 激活当前标签
                this.classList.add('active');
                this.style.borderBottomColor = '#4CAF50';
                this.style.color = 'white';

                // 显示对应内容
                const tabId = this.getAttribute('data-tab');
                document.getElementById(tabId).style.display = 'block';
            });
        });

        // 奖品池展开/收起功能
        const prizeContainer = document.getElementById('prizeContainer');
        const prizeScroller = document.getElementById('prizeScroller');
        const expandPrizePool = document.getElementById('expandPrizePool');
        const expandedPrizeGrid = document.getElementById('expandedPrizeGrid');
        let isExpanded = false;

        // 展开/收起奖品池
        expandPrizePool.addEventListener('click', function() {
            isExpanded = !isExpanded;

            if (isExpanded) {
                // 展开奖品池
                prizeContainer.style.display = 'none';
                expandedPrizeGrid.style.display = 'grid';
                this.innerHTML = '<i class="fas fa-compress-alt"></i> 收起奖品池';
                // 暂停动画
                prizeScroller.classList.add('paused-animation');
            } else {
                // 收起奖品池
                expandedPrizeGrid.style.display = 'none';
                prizeContainer.style.display = 'block';
                this.innerHTML = '<i class="fas fa-expand-alt"></i> 展开全部奖品';
                // 恢复动画
                prizeScroller.classList.remove('paused-animation');
            }
        });
    });
</script>
{% endblock %}
