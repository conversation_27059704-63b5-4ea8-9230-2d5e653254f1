{% extends "base.html" %}
{% block title %}管理员控制台 - SCUM物品代码网站{% endblock %}

{% block head_extra %}
<style>
    .admin-nav {
        display: flex;
        margin-bottom: 30px;
        border-bottom: 1px solid #444;
    }

    .admin-nav-item {
        padding: 12px 20px;
        color: #adb5bd;
        text-decoration: none;
        border-bottom: 3px solid transparent;
        transition: all 0.3s;
    }

    .admin-nav-item:hover {
        color: #f8f9fa;
        background-color: #333;
    }

    .admin-nav-item.active {
        color: #f8f9fa;
        border-bottom-color: #007bff;
    }

    .admin-container {
        padding: 30px 0;
    }

    .admin-header {
        margin-bottom: 30px;
        text-align: center;
    }

    .admin-header h1 {
        font-size: 2.5rem;
        margin-bottom: 10px;
        color: #f8f9fa;
    }

    .admin-header p {
        color: #adb5bd;
        font-size: 1.1rem;
    }

    .admin-panel {
        background-color: #2a2a2a;
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 30px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }

    .admin-panel-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
        padding-bottom: 15px;
        border-bottom: 1px solid #444;
    }

    .admin-panel-title {
        font-size: 1.5rem;
        color: #f8f9fa;
        margin: 0;
    }

    .admin-search-form {
        display: flex;
        margin-bottom: 20px;
    }

    .admin-search-input {
        flex: 1;
        padding: 10px 15px;
        border: 1px solid #444;
        background-color: #333;
        color: #fff;
        border-radius: 4px 0 0 4px;
    }

    .admin-search-btn {
        padding: 10px 20px;
        background-color: #007bff;
        color: white;
        border: none;
        border-radius: 0 4px 4px 0;
        cursor: pointer;
        transition: background-color 0.3s;
    }

    .admin-search-btn:hover {
        background-color: #0069d9;
    }

    .admin-table {
        width: 100%;
        border-collapse: collapse;
        margin-bottom: 20px;
    }

    .admin-table th,
    .admin-table td {
        padding: 12px 15px;
        text-align: left;
        border-bottom: 1px solid #444;
    }

    .admin-table th {
        background-color: #333;
        color: #f8f9fa;
        font-weight: 600;
    }

    .admin-table tr:hover {
        background-color: #333;
    }

    .admin-table .user-actions {
        display: flex;
        gap: 10px;
    }

    .admin-table .btn-edit {
        padding: 6px 12px;
        background-color: #28a745;
        color: white;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        transition: background-color 0.3s;
    }

    .admin-table .btn-edit:hover {
        background-color: #218838;
    }

    .admin-pagination {
        display: flex;
        justify-content: center;
        margin-top: 20px;
    }

    .admin-pagination button {
        padding: 8px 15px;
        margin: 0 5px;
        background-color: #333;
        color: #fff;
        border: 1px solid #444;
        border-radius: 4px;
        cursor: pointer;
        transition: background-color 0.3s;
    }

    .admin-pagination button:hover {
        background-color: #444;
    }

    .admin-pagination button.active {
        background-color: #007bff;
        border-color: #007bff;
    }

    .admin-pagination button:disabled {
        background-color: #222;
        color: #666;
        cursor: not-allowed;
    }

    .admin-modal {
        display: none;
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.7);
        z-index: 1000;
        overflow: auto;
    }

    .admin-modal-content {
        background-color: #2a2a2a;
        margin: 50px auto;
        padding: 20px;
        width: 80%;
        max-width: 600px;
        border-radius: 8px;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
        position: relative;
    }

    .admin-modal-close {
        position: absolute;
        top: 15px;
        right: 20px;
        font-size: 24px;
        color: #aaa;
        cursor: pointer;
        transition: color 0.3s;
    }

    .admin-modal-close:hover {
        color: #fff;
    }

    .admin-modal-header {
        margin-bottom: 20px;
        padding-bottom: 15px;
        border-bottom: 1px solid #444;
    }

    .admin-modal-title {
        font-size: 1.5rem;
        color: #f8f9fa;
        margin: 0;
    }

    .admin-form-group {
        margin-bottom: 20px;
    }

    .admin-form-group label {
        display: block;
        margin-bottom: 8px;
        color: #f8f9fa;
    }

    .admin-form-control {
        width: 100%;
        padding: 10px 15px;
        border: 1px solid #444;
        background-color: #333;
        color: #fff;
        border-radius: 4px;
    }

    .admin-form-check {
        display: flex;
        align-items: center;
        margin-bottom: 15px;
    }

    .admin-form-check input[type="checkbox"] {
        margin-right: 10px;
    }

    .admin-form-actions {
        display: flex;
        justify-content: flex-end;
        gap: 10px;
        margin-top: 20px;
    }

    .admin-btn {
        padding: 10px 20px;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        transition: background-color 0.3s;
    }

    .admin-btn-primary {
        background-color: #007bff;
        color: white;
    }

    .admin-btn-primary:hover {
        background-color: #0069d9;
    }

    .admin-btn-secondary {
        background-color: #6c757d;
        color: white;
    }

    .admin-btn-secondary:hover {
        background-color: #5a6268;
    }

    .admin-badge {
        display: inline-block;
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 0.8rem;
        font-weight: 600;
    }

    .admin-badge-admin {
        background-color: #dc3545;
        color: white;
    }

    .admin-badge-vip {
        background-color: #ffc107;
        color: #212529;
    }

    .admin-badge-user {
        background-color: #6c757d;
        color: white;
    }

    .admin-alert {
        padding: 15px;
        margin-bottom: 20px;
        border-radius: 4px;
    }

    .admin-alert-success {
        background-color: rgba(40, 167, 69, 0.2);
        border: 1px solid #28a745;
        color: #28a745;
    }

    .admin-alert-danger {
        background-color: rgba(220, 53, 69, 0.2);
        border: 1px solid #dc3545;
        color: #dc3545;
    }

    .btn-delete-inventory {
        padding: 6px 12px;
        background-color: #dc3545;
        color: white;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        transition: all 0.3s;
        display: inline-flex;
        align-items: center;
        gap: 5px;
    }

    .btn-delete-inventory:hover {
        background-color: #c82333;
        transform: translateY(-1px);
    }

    .btn-delete-inventory:active {
        transform: translateY(0);
    }

    .btn-delete-inventory i {
        font-size: 0.9em;
    }

    .user-actions {
        display: flex;
        gap: 8px;
        align-items: center;
    }
</style>
{% endblock %}

{% block content %}
<div class="container admin-container">
    <div class="admin-header">
        <h1><i class="fas fa-shield-alt"></i> 管理员控制台</h1>
        <p>管理用户、积分和VIP等级</p>
    </div>

    <div class="admin-nav">
        <a href="/admin" class="admin-nav-item active">用户管理</a>
        <a href="/admin/lottery" class="admin-nav-item">抽奖管理</a>
    </div>

    <div class="admin-panel">
        <div class="admin-panel-header">
            <h2 class="admin-panel-title">用户管理</h2>
        </div>

        <div class="admin-search-form">
            <input type="text" id="userSearchInput" class="admin-search-input" placeholder="搜索用户名或Steam ID...">
            <button id="userSearchBtn" class="admin-search-btn"><i class="fas fa-search"></i> 搜索</button>
        </div>

        <div id="userTableContainer">
            <table class="admin-table" id="userTable">
                <thead>
                    <tr>
                        <th>Steam ID</th>
                        <th>用户名</th>
                        <th>积分</th>
                        <th>VIP状态</th>
                        <th>管理员</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody id="userTableBody">
                    <!-- 用户数据将通过JavaScript动态加载 -->
                </tbody>
            </table>

            <div class="admin-pagination" id="userPagination">
                <!-- 分页控件将通过JavaScript动态生成 -->
            </div>
        </div>
    </div>
</div>

<!-- 编辑用户模态框 -->
<div id="editUserModal" class="admin-modal">
    <div class="admin-modal-content">
        <span class="admin-modal-close">&times;</span>
        <div class="admin-modal-header">
            <h3 class="admin-modal-title">编辑用户信息</h3>
        </div>

        <form id="editUserForm">
            <input type="hidden" id="editUserSteamId">

            <div class="admin-form-group">
                <label for="editUsername">用户名</label>
                <input type="text" id="editUsername" class="admin-form-control" disabled>
            </div>

            <div class="admin-form-group">
                <label for="editPoints">积分</label>
                <input type="number" id="editPoints" class="admin-form-control" min="0">
            </div>

            <div class="admin-form-check">
                <input type="checkbox" id="editIsVip">
                <label for="editIsVip">VIP用户</label>
            </div>

            <div class="admin-form-group">
                <label for="editVipLevel">VIP等级</label>
                <select id="editVipLevel" class="admin-form-control">
                    <option value="0">非VIP</option>
                    {% for level in vip_levels %}
                    <option value="{{ level.level }}">VIP{{ level.level }} - {{ level.points_required }}积分</option>
                    {% endfor %}
                </select>
            </div>

            <div class="admin-form-check">
                <input type="checkbox" id="editIsAdmin">
                <label for="editIsAdmin">管理员权限</label>
            </div>

            <div class="admin-form-actions">
                <button type="button" class="admin-btn admin-btn-secondary" id="cancelEditBtn">取消</button>
                <button type="submit" class="admin-btn admin-btn-primary">保存更改</button>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // 全局变量
        let currentPage = 1;
        let totalPages = 1;
        let searchTerm = '';

        // 初始加载用户列表
        loadUsers();

        // 搜索按钮点击事件
        document.getElementById('userSearchBtn').addEventListener('click', function() {
            searchTerm = document.getElementById('userSearchInput').value.trim();
            currentPage = 1;
            loadUsers();
        });

        // 搜索输入框回车事件
        document.getElementById('userSearchInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                searchTerm = document.getElementById('userSearchInput').value.trim();
                currentPage = 1;
                loadUsers();
            }
        });

        // 加载用户列表
        function loadUsers() {
            const url = `/api/admin/search_users?page=${currentPage}&search=${encodeURIComponent(searchTerm)}`;

            fetch(url)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        renderUserTable(data.users);
                        renderPagination(data.pagination);
                    } else {
                        showAlert('加载用户列表失败: ' + data.message, 'danger');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    showAlert('加载用户列表失败，请稍后再试', 'danger');
                });
        }

        // 渲染用户表格
        function renderUserTable(users) {
            const tableBody = document.getElementById('userTableBody');
            tableBody.innerHTML = '';

            if (users.length === 0) {
                const row = document.createElement('tr');
                row.innerHTML = '<td colspan="6" style="text-align: center;">没有找到用户</td>';
                tableBody.appendChild(row);
                return;
            }

            users.forEach(user => {
                const row = document.createElement('tr');

                // VIP状态显示
                let vipStatus = '';
                if (user.is_vip) {
                    vipStatus = `<span class="admin-badge admin-badge-vip">VIP${user.vip_level}</span>`;
                } else {
                    vipStatus = '<span class="admin-badge admin-badge-user">普通用户</span>';
                }

                // 管理员状态显示
                let adminStatus = '';
                if (user.is_admin) {
                    adminStatus = '<span class="admin-badge admin-badge-admin">是</span>';
                } else {
                    adminStatus = '<span>否</span>';
                }

                row.innerHTML = `
                    <td>${user.steamid}</td>
                    <td>${user.username || '未设置'}</td>
                    <td>${user.points}</td>
                    <td>${vipStatus}</td>
                    <td>${adminStatus}</td>
                    <td>
                        <div class="user-actions">
                            <button class="btn-edit" data-steamid="${user.steamid}">编辑</button>
                            <button class="btn-delete-inventory" data-steamid="${user.steamid}" data-username="${user.username || '未设置'}">
                                <i class="fas fa-trash"></i> 删除仓库
                            </button>
                        </div>
                    </td>
                `;

                tableBody.appendChild(row);
            });

            // 添加编辑按钮点击事件
            document.querySelectorAll('.btn-edit').forEach(button => {
                button.addEventListener('click', function() {
                    const steamid = this.getAttribute('data-steamid');
                    openEditModal(steamid);
                });
            });
        }

        // 渲染分页控件
        function renderPagination(pagination) {
            const paginationContainer = document.getElementById('userPagination');
            paginationContainer.innerHTML = '';

            totalPages = pagination.total_pages;

            // 如果只有一页，不显示分页
            if (totalPages <= 1) {
                return;
            }

            // 上一页按钮
            const prevButton = document.createElement('button');
            prevButton.innerHTML = '<i class="fas fa-chevron-left"></i> 上一页';
            prevButton.disabled = currentPage === 1;
            prevButton.addEventListener('click', function() {
                if (currentPage > 1) {
                    currentPage--;
                    loadUsers();
                }
            });
            paginationContainer.appendChild(prevButton);

            // 页码按钮
            let startPage = Math.max(1, currentPage - 2);
            let endPage = Math.min(totalPages, startPage + 4);

            if (endPage - startPage < 4) {
                startPage = Math.max(1, endPage - 4);
            }

            for (let i = startPage; i <= endPage; i++) {
                const pageButton = document.createElement('button');
                pageButton.textContent = i;
                pageButton.classList.toggle('active', i === currentPage);
                pageButton.addEventListener('click', function() {
                    currentPage = i;
                    loadUsers();
                });
                paginationContainer.appendChild(pageButton);
            }

            // 下一页按钮
            const nextButton = document.createElement('button');
            nextButton.innerHTML = '下一页 <i class="fas fa-chevron-right"></i>';
            nextButton.disabled = currentPage === totalPages;
            nextButton.addEventListener('click', function() {
                if (currentPage < totalPages) {
                    currentPage++;
                    loadUsers();
                }
            });
            paginationContainer.appendChild(nextButton);
        }

        // 打开编辑用户模态框
        function openEditModal(steamid) {
            fetch(`/api/admin/user/${steamid}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const user = data.user;

                        // 填充表单
                        document.getElementById('editUserSteamId').value = user.steamid;
                        document.getElementById('editUsername').value = user.username || '';
                        document.getElementById('editPoints').value = user.points || 0;
                        document.getElementById('editIsVip').checked = user.is_vip === 1;
                        document.getElementById('editVipLevel').value = user.vip_level || 0;
                        document.getElementById('editIsAdmin').checked = user.is_admin === 1;

                        // 显示模态框
                        document.getElementById('editUserModal').style.display = 'block';
                    } else {
                        showAlert('获取用户信息失败: ' + data.message, 'danger');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    showAlert('获取用户信息失败，请稍后再试', 'danger');
                });
        }

        // 关闭模态框
        document.querySelector('.admin-modal-close').addEventListener('click', function() {
            document.getElementById('editUserModal').style.display = 'none';
        });

        document.getElementById('cancelEditBtn').addEventListener('click', function() {
            document.getElementById('editUserModal').style.display = 'none';
        });

        // 点击模态框外部关闭
        window.addEventListener('click', function(event) {
            const modal = document.getElementById('editUserModal');
            if (event.target === modal) {
                modal.style.display = 'none';
            }
        });

        // 提交编辑表单
        document.getElementById('editUserForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const steamid = document.getElementById('editUserSteamId').value;
            const points = parseInt(document.getElementById('editPoints').value);
            const isVip = document.getElementById('editIsVip').checked;
            const vipLevel = parseInt(document.getElementById('editVipLevel').value);
            const isAdmin = document.getElementById('editIsAdmin').checked;

            // 构建请求数据
            const data = {
                points: points,
                is_vip: isVip,
                vip_level: vipLevel,
                is_admin: isAdmin
            };

            // 发送更新请求
            fetch(`/api/admin/user/${steamid}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(data)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showAlert('用户信息更新成功', 'success');
                    document.getElementById('editUserModal').style.display = 'none';
                    loadUsers(); // 重新加载用户列表
                } else {
                    showAlert('更新用户信息失败: ' + data.message, 'danger');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showAlert('更新用户信息失败，请稍后再试', 'danger');
            });
        });

        // 显示提示信息
        function showAlert(message, type) {
            // 创建提示元素
            const alertDiv = document.createElement('div');
            alertDiv.className = `admin-alert admin-alert-${type}`;
            alertDiv.textContent = message;

            // 添加到页面
            const container = document.querySelector('.admin-container');
            container.insertBefore(alertDiv, container.firstChild);

            // 3秒后自动移除
            setTimeout(() => {
                alertDiv.remove();
            }, 3000);
        }

        // VIP复选框与VIP等级联动
        document.getElementById('editIsVip').addEventListener('change', function() {
            const vipLevelSelect = document.getElementById('editVipLevel');

            if (this.checked) {
                if (vipLevelSelect.value === '0') {
                    vipLevelSelect.value = '1';
                }
            } else {
                vipLevelSelect.value = '0';
            }
        });

        // VIP等级与VIP复选框联动
        document.getElementById('editVipLevel').addEventListener('change', function() {
            const isVipCheckbox = document.getElementById('editIsVip');

            if (this.value === '0') {
                isVipCheckbox.checked = false;
            } else {
                isVipCheckbox.checked = true;
            }
        });

        // 删除用户仓库
        function deleteUserInventory(steamid, username) {
            customConfirm(`确定要删除用户 ${username} (${steamid}) 的所有仓库物品吗？此操作不可撤销。`, (confirmed) => {
                if (confirmed) {
                    fetch('/delete_user_inventory', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-Requested-With': 'XMLHttpRequest'
                        },
                        body: JSON.stringify({
                            steamid: steamid
                        })
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            showAlert(data.message, 'success');
                            // 重新加载用户列表
                            loadUsers();
                        } else {
                            showAlert(data.message, 'danger');
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        showAlert('操作失败，请稍后再试', 'danger');
                    });
                }
            });
        }

        // 绑定删除仓库按钮事件
        document.addEventListener('click', function(e) {
            if (e.target.closest('.btn-delete-inventory')) {
                const button = e.target.closest('.btn-delete-inventory');
                const steamid = button.getAttribute('data-steamid');
                const username = button.getAttribute('data-username');
                deleteUserInventory(steamid, username);
            }
        });
    });
</script>
{% endblock %}
