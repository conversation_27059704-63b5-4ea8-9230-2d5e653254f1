# 系统服务配置说明
# 这个文件演示如何将应用设置为系统服务，将此文件保存为 /etc/systemd/system/scum-shop.service

[Unit]
Description=7788商城服务
After=network.target

[Service]
Type=forking
User=your_username  # 修改为实际运行服务的用户名
Group=your_group    # 修改为实际运行服务的用户组
WorkingDirectory=/path/to/application  # 修改为应用程序的实际路径
ExecStart=/path/to/application/daemon.sh start
ExecStop=/path/to/application/daemon.sh stop
ExecReload=/path/to/application/daemon.sh restart
Restart=on-failure
RestartSec=10

[Install]
WantedBy=multi-user.target

# 使用方法：
# 1. 修改上面的用户名、用户组和路径
# 2. 将此文件复制到 /etc/systemd/system/scum-shop.service
# 3. 执行以下命令启用并启动服务：
#    sudo systemctl daemon-reload
#    sudo systemctl enable scum-shop.service
#    sudo systemctl start scum-shop.service
#
# 查看服务状态：
#    sudo systemctl status scum-shop.service
#
# 查看日志：
#    sudo journalctl -u scum-shop.service 