{% extends "base.html" %}
{% block title %}设置密码 - SCUM物品代码网站{% endblock %}

{% block head_extra %}
<style>
    /* 设置密码页面样式 */
    .password-page {
        display: flex;
        justify-content: center;
        align-items: center;
        min-height: calc(100vh - 200px);
        padding: 40px 0;
    }
    
    .password-container {
        width: 100%;
        max-width: 480px;
        margin: 0 auto;
    }
    
    .password-card {
        background: linear-gradient(145deg, #2a2a2a, #333333);
        border-radius: 16px;
        overflow: hidden;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        border: 1px solid rgba(255, 255, 255, 0.1);
        animation: fadeIn 0.5s ease-out forwards;
    }
    
    .password-header {
        background: linear-gradient(135deg, #00a8ff, #0077cc);
        padding: 30px;
        text-align: center;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }
    
    .password-header h1 {
        color: #ffffff;
        font-size: 28px;
        margin: 0 0 10px 0;
        font-weight: 600;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        display: flex;
        align-items: center;
        justify-content: center;
    }
    
    .password-header h1 i {
        margin-right: 12px;
        font-size: 32px;
    }
    
    .password-header p {
        color: #ffffff;
        margin: 0;
        font-size: 16px;
        opacity: 0.9;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
    }
    
    .password-body {
        padding: 30px;
        background-color: #2a2a2a;
    }
    
    .password-form .form-group {
        margin-bottom: 25px;
    }
    
    .password-form label {
        display: block;
        margin-bottom: 10px;
        color: #e0e0e0;
        font-weight: 500;
        font-size: 16px;
    }
    
    .password-form .input-wrapper {
        position: relative;
    }
    
    .password-form .input-icon {
        position: absolute;
        left: 16px;
        top: 50%;
        transform: translateY(-50%);
        color: #00a8ff;
        font-size: 18px;
    }
    
    .password-form .form-control {
        width: 100%;
        height: 54px;
        background-color: #333;
        border: 1px solid rgba(255, 255, 255, 0.1);
        border-radius: 8px;
        color: #ffffff;
        font-size: 16px;
        padding: 0 16px 0 50px;
        transition: all 0.3s ease;
    }
    
    .password-form .form-control:focus {
        background-color: #3a3a3a;
        border-color: #00a8ff;
        box-shadow: 0 0 0 3px rgba(0, 168, 255, 0.25);
        outline: none;
    }
    
    .password-form .form-text {
        color: #cccccc;
        font-size: 14px;
        margin-top: 8px;
        display: block;
    }
    
    .password-form .btn-submit {
        width: 100%;
        height: 54px;
        background: linear-gradient(135deg, #00a8ff, #0077cc);
        border: none;
        border-radius: 8px;
        color: #ffffff;
        font-size: 18px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-top: 10px;
    }
    
    .password-form .btn-submit:hover {
        background: linear-gradient(135deg, #0095e0, #0066b3);
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);
    }
    
    .password-form .btn-submit i {
        margin-right: 10px;
        font-size: 18px;
    }
    
    .password-footer {
        text-align: center;
        padding: 20px 30px;
        background-color: #222;
        border-top: 1px solid rgba(255, 255, 255, 0.05);
    }
    
    .password-footer p {
        color: #aaa;
        margin: 0;
        font-size: 14px;
    }
    
    .alert-custom {
        background-color: rgba(220, 53, 69, 0.2);
        color: #ff6b6b;
        border: 1px solid rgba(220, 53, 69, 0.3);
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 25px;
        display: flex;
        align-items: center;
    }
    
    .alert-custom i {
        margin-right: 10px;
        font-size: 20px;
        flex-shrink: 0;
    }
    
    /* 动画效果 */
    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(20px); }
        to { opacity: 1; transform: translateY(0); }
    }
</style>
{% endblock %}

{% block content %}
<div class="password-page">
    <div class="password-container">
        <div class="password-card">
            <div class="password-header">
                <h1><i class="fas fa-key"></i> 设置密码</h1>
                <p>请为您的账号设置一个安全的密码</p>
            </div>
            
            <div class="password-body">
                {% with messages = get_flashed_messages() %}
                    {% if messages %}
                        {% for message in messages %}
                        <div class="alert-custom">
                            <i class="fas fa-exclamation-circle"></i>
                            <span>{{ message }}</span>
                        </div>
                        {% endfor %}
                    {% endif %}
                {% endwith %}
                
                {% if error %}
                <div class="alert-custom">
                    <i class="fas fa-exclamation-circle"></i>
                    <span>{{ error }}</span>
                </div>
                {% endif %}
                
                <form action="{{ url_for('set_password') }}" method="post" id="passwordForm" class="password-form">
                    <div class="form-group">
                        <label for="new_password">新密码</label>
                        <div class="input-wrapper">
                            <i class="fas fa-lock input-icon"></i>
                            <input type="password" class="form-control" id="new_password" name="new_password" 
                                   placeholder="输入新密码" required>
                        </div>
                        <small class="form-text">至少6位字符，建议使用字母和数字的组合</small>
                    </div>
                    
                    <div class="form-group">
                        <label for="confirm_password">确认密码</label>
                        <div class="input-wrapper">
                            <i class="fas fa-check-circle input-icon"></i>
                            <input type="password" class="form-control" id="confirm_password" name="confirm_password" 
                                   placeholder="再次输入新密码" required>
                        </div>
                    </div>
                    
                    <button type="submit" class="btn-submit">
                        <i class="fas fa-save"></i>设置密码并登录
                    </button>
                </form>
            </div>
            
            <div class="password-footer">
                <p>请牢记您的密码，将用于下次登录</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // 检查密码匹配
    const newPassword = document.getElementById('new_password');
    const confirmPassword = document.getElementById('confirm_password');
    const form = document.getElementById('passwordForm');

    form.addEventListener('submit', function(e) {
        if (newPassword.value !== confirmPassword.value) {
            e.preventDefault();
            alert('两次输入的密码不一致，请重新输入');
        }

        if (newPassword.value.length < 6) {
            e.preventDefault();
            alert('密码长度至少为6位');
        }
    });
});
</script>
{% endblock %}
