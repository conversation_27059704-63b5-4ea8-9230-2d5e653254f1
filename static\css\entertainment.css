/* 娱乐中心页面样式 */

.dice-buttons {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.dice-buttons .play-btn {
    width: 100%;
    white-space: nowrap;
}

.dice-buttons .play-btn:nth-child(2) {
    background-color: #5e35b1;
}

.dice-buttons .play-btn:nth-child(3) {
    background-color: #d32f2f;
}

.dice-result-table-container {
    max-height: 300px;
    overflow-y: auto;
    margin-top: 15px;
    margin-bottom: 15px;
    border-radius: 8px;
    background-color: rgba(0, 0, 0, 0.2);
}

.dice-result-table {
    width: 100%;
    border-collapse: collapse;
    background-color: rgba(0, 0, 0, 0.2);
    overflow: hidden;
}

.dice-result-table th,
.dice-result-table td {
    padding: 8px 12px;
    text-align: center;
    border-bottom: 1px solid #444;
}

.dice-result-table th {
    background-color: rgba(0, 0, 0, 0.3);
    color: var(--text-primary);
    font-weight: 600;
}

.dice-result-table tr:last-child td {
    border-bottom: none;
}

.dice-result-table .win {
    color: #4caf50;
}

.dice-result-table .lose {
    color: #f44336;
}

.dice-result-summary {
    margin-top: 20px;
    padding: 15px;
    background-color: rgba(0, 0, 0, 0.2);
    border-radius: 8px;
    border-left: 3px solid var(--primary-color);
}

.dice-result-summary p {
    margin: 5px 0;
}

.dice-result-summary .total-win {
    color: #4caf50;
    font-weight: 600;
}

.dice-result-summary .total-lose {
    color: #f44336;
    font-weight: 600;
}

.dice-result-summary .net-result {
    font-size: 1.1rem;
    font-weight: 700;
    margin-top: 10px;
}

.dice-result-summary .net-result.positive {
    color: #4caf50;
}

.dice-result-summary .net-result.negative {
    color: #f44336;
}
.entertainment-container {
    margin-top: 30px;
    margin-bottom: 40px;
}

.entertainment-header {
    text-align: center;
    margin-bottom: 40px;
}

.entertainment-header h1 {
    font-size: 2.2rem;
    font-weight: 700;
    margin-bottom: 16px;
    color: var(--text-primary);
}

.entertainment-header p {
    font-size: 1.1rem;
    color: var(--text-secondary);
    max-width: 700px;
    margin: 0 auto;
}

/* 排行榜部分 */
.leaderboard-section {
    background-color: var(--card-bg);
    border-radius: var(--border-radius);
    padding: 30px;
    margin-bottom: 40px;
    border: 1px solid #333;
    box-shadow: var(--box-shadow);
}

.section-header {
    margin-bottom: 25px;
    text-align: center;
}

.section-header h2 {
    font-size: 1.6rem;
    font-weight: 600;
    margin-bottom: 10px;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    justify-content: center;
}

.section-header h2 i {
    margin-right: 10px;
    color: var(--primary-color);
}

.section-header p {
    color: var(--text-secondary);
    font-size: 1rem;
}

.leaderboard-table {
    overflow-x: auto;
}

.leaderboard-table table {
    width: 100%;
    border-collapse: collapse;
}

.leaderboard-table th, .leaderboard-table td {
    padding: 15px;
    text-align: left;
    border-bottom: 1px solid #333;
}

.leaderboard-table th {
    background-color: rgba(0, 0, 0, 0.2);
    color: var(--text-primary);
    font-weight: 600;
}

.leaderboard-table tr:hover {
    background-color: rgba(255, 255, 255, 0.05);
}

.leaderboard-table tr.current-user {
    background-color: rgba(0, 168, 255, 0.1);
}

.user-badges {
    display: flex;
    flex-wrap: wrap;
    gap: 5px;
}

.vip-badge, .admin-badge, .user-badge {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.8rem;
    font-weight: 600;
    display: inline-flex;
    align-items: center;
}

.vip-badge {
    background-color: #ffc107;
    color: #000;
}

.admin-badge {
    background-color: #f44336;
    color: #fff;
}

.user-badge {
    background-color: #607d8b;
    color: #fff;
}

.vip-badge i, .admin-badge i, .user-badge i {
    margin-right: 4px;
}

/* 每日签到部分 */
.daily-checkin-section {
    background-color: var(--card-bg);
    border-radius: var(--border-radius);
    padding: 30px;
    margin-bottom: 40px;
    border: 1px solid #333;
    box-shadow: var(--box-shadow);
}

.checkin-card {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: rgba(0, 0, 0, 0.2);
    border-radius: 10px;
    padding: 25px;
    border: 1px solid #444;
}

.checkin-info {
    flex: 1;
}

.checkin-status {
    margin-bottom: 15px;
}

.status-signed, .status-unsigned {
    display: flex;
    align-items: center;
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 5px;
}

.status-signed {
    color: #4caf50;
}

.status-unsigned {
    color: #ff9800;
}

.status-signed i, .status-unsigned i {
    margin-right: 10px;
    font-size: 1.4rem;
}

.next-checkin, .checkin-reward {
    color: var(--text-secondary);
    font-size: 0.9rem;
    margin-top: 5px;
}

.reward-points {
    color: #ff9800;
    font-weight: 600;
}

.checkin-streak {
    margin-top: 15px;
    display: flex;
    gap: 20px;
}

.checkin-streak p {
    color: var(--text-secondary);
    font-size: 0.95rem;
}

.streak-days, .total-days {
    color: var(--primary-color);
    font-weight: 600;
}

.checkin-action {
    margin-left: 20px;
}

.checkin-btn {
    display: inline-block;
    padding: 12px 25px;
    background-color: #4caf50;
    color: white;
    border-radius: 30px;
    font-weight: 600;
    font-size: 1rem;
    text-align: center;
    transition: var(--transition);
    border: none;
    cursor: pointer;
    box-shadow: 0 4px 10px rgba(76, 175, 80, 0.3);
}

.checkin-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 15px rgba(76, 175, 80, 0.4);
    text-decoration: none;
    color: white;
}

.checkin-btn.signed {
    background-color: #9e9e9e;
    cursor: not-allowed;
    box-shadow: none;
}

.checkin-btn.signed:hover {
    transform: none;
    box-shadow: none;
}

.checkin-btn i {
    margin-right: 5px;
}

/* 游戏部分 */
.games-section {
    background-color: var(--card-bg);
    border-radius: var(--border-radius);
    padding: 30px;
    border: 1px solid #333;
    box-shadow: var(--box-shadow);
}

.games-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
}

/* 抽奖中心样式 */
.prize-container {
    background-color: rgba(0, 0, 0, 0.2);
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 20px;
    border: 1px solid #333;
}

.prize-scroller {
    display: flex;
    gap: 15px;
}

.prize-card {
    background-color: rgba(0, 0, 0, 0.3);
    border-radius: 8px;
    overflow: hidden;
    transition: all 0.3s ease;
    border: 1px solid #444;
}

.prize-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
    border-color: #4CAF50;
}

.prize-image {
    height: 150px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: rgba(0, 0, 0, 0.2);
    overflow: hidden;
}

.prize-image img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
}

.prize-info {
    padding: 15px;
}

.prize-info h3 {
    margin: 0 0 10px 0;
    font-size: 18px;
    color: white;
}

#expandPrizePool {
    background-color: transparent;
    color: #4CAF50;
    border: 1px solid #4CAF50;
    transition: all 0.3s ease;
}

#expandPrizePool:hover {
    background-color: #4CAF50;
    color: white;
}

/* 抽奖结果模态框样式 */
.lottery-result-modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
    z-index: 1000;
    justify-content: center;
    align-items: center;
}

.modal-content {
    background-color: #222;
    border-radius: 10px;
    width: 90%;
    max-width: 600px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 5px 25px rgba(0, 0, 0, 0.5);
    border: 1px solid #444;
    animation: modalFadeIn 0.3s;
}

@keyframes modalFadeIn {
    from { opacity: 0; transform: translateY(-20px); }
    to { opacity: 1; transform: translateY(0); }
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    border-bottom: 1px solid #333;
}

.modal-header h2 {
    margin: 0;
    color: #4CAF50;
    font-size: 1.5rem;
}

.modal-close {
    font-size: 1.8rem;
    cursor: pointer;
    color: #aaa;
    transition: color 0.2s;
}

.modal-close:hover {
    color: white;
}

.modal-body {
    padding: 20px;
}

.modal-footer {
    padding: 15px 20px;
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    border-top: 1px solid #333;
}

.draw-again-btn {
    padding: 10px 20px;
    background-color: #4CAF50;
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-weight: 600;
    transition: all 0.2s;
}

.draw-again-btn:hover {
    background-color: #3d8b40;
    transform: translateY(-2px);
    box-shadow: 0 3px 8px rgba(76, 175, 80, 0.3);
}

.close-btn {
    padding: 10px 20px;
    background-color: #555;
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    transition: all 0.2s;
}

.close-btn:hover {
    background-color: #666;
    transform: translateY(-2px);
}

/* 确认对话框样式 */
.confirm-dialog {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    z-index: 1000;
    justify-content: center;
    align-items: center;
}

.confirm-content {
    background-color: #222;
    border-radius: 10px;
    padding: 20px;
    width: 90%;
    max-width: 400px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.5);
    border: 1px solid #444;
    animation: confirmFadeIn 0.3s;
}

@keyframes confirmFadeIn {
    from { opacity: 0; transform: scale(0.9); }
    to { opacity: 1; transform: scale(1); }
}

.confirm-header {
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #333;
}

.confirm-header h3 {
    margin: 0;
    color: #4CAF50;
}

.confirm-body {
    margin-bottom: 20px;
}

.confirm-body p {
    font-size: 16px;
    line-height: 1.5;
}

.confirm-footer {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

#cancelDrawBtn {
    padding: 8px 15px;
    background-color: #555;
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    transition: all 0.2s;
}

#cancelDrawBtn:hover {
    background-color: #666;
    transform: translateY(-2px);
}

#confirmDrawBtn {
    padding: 8px 15px;
    background-color: #4CAF50;
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    transition: all 0.2s;
}

#confirmDrawBtn:hover {
    background-color: #3d8b40;
    transform: translateY(-2px);
    box-shadow: 0 3px 8px rgba(76, 175, 80, 0.3);
}

.game-card {
    background-color: rgba(0, 0, 0, 0.2);
    border-radius: 10px;
    padding: 20px;
    display: flex;
    align-items: center;
    border: 1px solid #444;
    transition: var(--transition);
}

.game-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);
    border-color: var(--primary-color);
}

.game-icon {
    width: 60px;
    height: 60px;
    background-color: #673ab7;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    box-shadow: 0 5px 15px rgba(103, 58, 183, 0.3);
}

.game-icon i {
    font-size: 24px;
    color: white;
}

.game-info {
    flex: 1;
}

.game-info h3 {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 5px;
    color: var(--text-primary);
}

.game-info p {
    color: var(--text-secondary);
    font-size: 0.9rem;
    margin-bottom: 10px;
}

.game-cost {
    font-size: 0.85rem;
    color: #ff9800;
    font-weight: 500;
}

.game-action {
    margin-left: 15px;
}

.play-btn {
    background-color: #673ab7;
    color: white;
    border: none;
    border-radius: 20px;
    padding: 8px 15px;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
    box-shadow: 0 3px 8px rgba(103, 58, 183, 0.3);
}

.play-btn:hover {
    background-color: #5e35b1;
    transform: translateY(-2px);
    box-shadow: 0 5px 12px rgba(103, 58, 183, 0.4);
}

.play-btn i {
    margin-right: 5px;
}

/* 游戏结果模态框 */
.game-result-modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    z-index: 1000;
    justify-content: center;
    align-items: center;
}

.modal-content {
    background-color: #222;
    border-radius: 15px;
    width: 400px;
    max-width: 90%;
    max-height: 80vh;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
    animation: modalFadeIn 0.3s;
    border: 1px solid #444;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

@keyframes modalFadeIn {
    from { opacity: 0; transform: translateY(-30px); }
    to { opacity: 1; transform: translateY(0); }
}

.modal-header {
    padding: 20px;
    border-bottom: 1px solid #444;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: rgba(0, 0, 0, 0.2);
}

.modal-header h2 {
    font-size: 1.4rem;
    font-weight: 600;
    color: var(--primary-color);
    margin: 0;
}

.modal-close {
    font-size: 1.5rem;
    cursor: pointer;
    color: #999;
    transition: color 0.2s;
}

.modal-close:hover {
    color: #fff;
}

.modal-body {
    padding: 30px;
    overflow-y: auto;
    max-height: calc(80vh - 140px); /* 减去header和footer的高度 */
}

.result-animation {
    text-align: center;
    margin-bottom: 20px;
}

.dice {
    width: 100px;
    height: 100px;
    background-color: white;
    border-radius: 15px;
    margin: 0 auto;
    position: relative;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
    animation: diceRoll 1s;
}

@keyframes diceRoll {
    0% { transform: rotateZ(0deg); }
    25% { transform: rotateZ(90deg); }
    50% { transform: rotateZ(180deg); }
    75% { transform: rotateZ(270deg); }
    100% { transform: rotateZ(360deg); }
}

.dice-dot {
    width: 20px;
    height: 20px;
    background-color: #333;
    border-radius: 50%;
    position: absolute;
}

.dice-1 .dice-dot:nth-child(1) {
    top: 40px;
    left: 40px;
}

.dice-2 .dice-dot:nth-child(1) {
    top: 20px;
    left: 20px;
}

.dice-2 .dice-dot:nth-child(2) {
    bottom: 20px;
    right: 20px;
}

.dice-3 .dice-dot:nth-child(1) {
    top: 20px;
    left: 20px;
}

.dice-3 .dice-dot:nth-child(2) {
    top: 40px;
    left: 40px;
}

.dice-3 .dice-dot:nth-child(3) {
    bottom: 20px;
    right: 20px;
}

.dice-4 .dice-dot:nth-child(1) {
    top: 20px;
    left: 20px;
}

.dice-4 .dice-dot:nth-child(2) {
    top: 20px;
    right: 20px;
}

.dice-4 .dice-dot:nth-child(3) {
    bottom: 20px;
    left: 20px;
}

.dice-4 .dice-dot:nth-child(4) {
    bottom: 20px;
    right: 20px;
}

.dice-5 .dice-dot:nth-child(1) {
    top: 20px;
    left: 20px;
}

.dice-5 .dice-dot:nth-child(2) {
    top: 20px;
    right: 20px;
}

.dice-5 .dice-dot:nth-child(3) {
    top: 40px;
    left: 40px;
}

.dice-5 .dice-dot:nth-child(4) {
    bottom: 20px;
    left: 20px;
}

.dice-5 .dice-dot:nth-child(5) {
    bottom: 20px;
    right: 20px;
}

.dice-6 .dice-dot:nth-child(1) {
    top: 20px;
    left: 20px;
}

.dice-6 .dice-dot:nth-child(2) {
    top: 20px;
    right: 20px;
}

.dice-6 .dice-dot:nth-child(3) {
    top: 40px;
    left: 20px;
}

.dice-6 .dice-dot:nth-child(4) {
    top: 40px;
    right: 20px;
}

.dice-6 .dice-dot:nth-child(5) {
    bottom: 20px;
    left: 20px;
}

.dice-6 .dice-dot:nth-child(6) {
    bottom: 20px;
    right: 20px;
}

.result-message {
    text-align: center;
}

.result-success, .result-failure {
    padding: 15px;
    border-radius: 10px;
    text-align: center;
}

.result-success {
    background-color: rgba(76, 175, 80, 0.1);
    border: 1px solid rgba(76, 175, 80, 0.3);
}

.result-failure {
    background-color: rgba(244, 67, 54, 0.1);
    border: 1px solid rgba(244, 67, 54, 0.3);
}

.result-success i, .result-failure i {
    font-size: 2rem;
    margin-bottom: 10px;
}

.result-success i {
    color: #4caf50;
}

.result-failure i {
    color: #f44336;
}

.points-change {
    font-size: 1.2rem;
    font-weight: 600;
    margin: 10px 0;
}

.result-success .points-change {
    color: #4caf50;
}

.result-failure .points-change {
    color: #f44336;
}

.current-points {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.modal-footer {
    padding: 15px 20px;
    border-top: 1px solid #444;
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    background-color: rgba(0, 0, 0, 0.1);
    position: sticky;
    bottom: 0;
    z-index: 10;
}

.play-again-btn, .close-btn {
    padding: 8px 16px;
    border-radius: 4px;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
    border: none;
}

.play-again-btn {
    background-color: #673ab7;
    color: white;
}

.play-again-btn:hover {
    background-color: #5e35b1;
    transform: translateY(-2px);
    box-shadow: 0 3px 8px rgba(103, 58, 183, 0.3);
}

.close-btn {
    background-color: #555;
    color: white;
}

.close-btn:hover {
    background-color: #666;
    transform: translateY(-2px);
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.2);
}

@media (max-width: 768px) {
    .checkin-card {
        flex-direction: column;
        text-align: center;
    }

    .checkin-status, .status-signed, .status-unsigned {
        justify-content: center;
    }

    .checkin-streak {
        justify-content: center;
    }

    .checkin-action {
        margin-left: 0;
        margin-top: 20px;
    }

    .games-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 576px) {
    .leaderboard-table th, .leaderboard-table td {
        padding: 8px 5px;
        font-size: 0.75rem;
    }

    .vip-badge, .admin-badge, .user-badge {
        padding: 3px 6px;
        font-size: 0.7rem;
    }

    .checkin-streak {
        flex-direction: column;
        gap: 5px;
    }
}
