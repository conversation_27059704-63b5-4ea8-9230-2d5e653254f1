# 7788商城

这是一个用于展示SCUM游戏物品的网站，解决了HTTPS网站无法加载HTTP资源的混合内容问题。

## 问题描述

当网站通过HTTPS访问(https://scum.scum7788商城.dpdns.org/)，但尝试加载HTTP资源(http://47.121.131.132:5778/...)时，现代浏览器会默认阻止这些不安全的请求，导致图片无法正常加载。

## 解决方案

通过实现后端定时同步服务，解决混合内容问题：

1. 使用Python脚本定期从API获取数据
2. 下载并缓存所有图片到本地
3. 修改图片路径，不再直接使用HTTP链接
4. 通过Flask应用提供本地缓存的图片文件

这样，用户就可以通过HTTPS协议访问所有资源，避免混合内容问题。

## 功能

- 展示游戏中的传送点信息
- 展示各种游戏物品包信息
- 自动同步API数据，定期检查更新
- 自动下载并缓存图片资源
- 响应式设计，适配各种屏幕尺寸

## 安装与运行

### 基本安装

1. 确保已安装Python 3.6+
2. 安装依赖
```
pip install -r requirements.txt
```

### Windows下运行

在Windows环境下，可以使用提供的批处理文件启动服务：
```
start_services.bat
```

### Linux下运行

在Linux环境下，有多种方式可以运行服务：

#### 方式一：使用守护进程脚本

这是最直接的方式，可以通过daemon.sh脚本管理服务：

```bash
# 给脚本添加执行权限
chmod +x daemon.sh

# 启动服务
./daemon.sh start

# 查看服务状态
./daemon.sh status

# 查看应用日志
./daemon.sh log-app

# 查看同步服务日志
./daemon.sh log-sync

# 停止服务
./daemon.sh stop

# 重启服务
./daemon.sh restart
```

#### 方式二：使用systemd管理（推荐）

如果您的系统使用systemd（大多数现代Linux发行版），可以将应用配置为系统服务：

1. 编辑systemd_service.txt文件，修改用户名、用户组和应用路径
2. 将修改后的内容保存到系统服务目录：
```bash
sudo cp systemd_service.txt /etc/systemd/system/scum-shop.service
sudo systemctl daemon-reload
sudo systemctl enable scum-shop.service
sudo systemctl start scum-shop.service
```

#### 方式三：使用supervisor管理

如果您更习惯使用supervisor，可以使用supervisord_conf.txt配置：

1. 安装supervisor：`sudo apt-get install supervisor`
2. 编辑supervisord_conf.txt文件，修改用户名和应用路径
3. 将修改后的内容保存到supervisor配置目录：
```bash
sudo cp supervisord_conf.txt /etc/supervisor/conf.d/scum-shop.conf
sudo supervisorctl reread
sudo supervisorctl update
sudo supervisorctl start scum-shop:*
```

#### 方式四：使用crontab设置开机自启动

最简单的方式是使用crontab设置系统启动时自动运行服务：

1. 编辑crontab：`crontab -e`
2. 添加以下内容（修改为实际路径）：
```
@reboot cd /path/to/application && ./daemon.sh start > /path/to/application/logs/cron_startup.log 2>&1
```

## 技术细节

- `sync_api.py`: API同步服务，负责获取数据和下载图片
- `app.py`: Flask Web应用，提供网页访问
- `data/all-data.json`: 本地缓存的API数据
- `static/uploads/`: 本地缓存的图片文件
- `daemon.sh`: 守护进程管理脚本（Linux环境）

## 技术栈

- Flask: Web框架
- Requests: HTTP客户端
- HTML/CSS/JavaScript: 前端展示 