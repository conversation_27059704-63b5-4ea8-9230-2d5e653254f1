<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>战斗通行证 - 7788商城</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='vendor/bootstrap/css/bootstrap.min.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='vendor/fontawesome/css/all.min.css') }}">
<style>
    /* 重置所有样式 */
    * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
    }

    html, body {
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        min-height: 100vh;
        font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'PingFang SC', 'Microsoft YaHei', sans-serif;
        color: #1d1d1f;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
    }

    .battlepass-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 40px 20px;
    }

    .battlepass-header {
        background: rgba(255, 255, 255, 0.8);
        backdrop-filter: blur(20px);
        border-radius: 20px;
        padding: 40px;
        color: #1d1d1f;
        margin-bottom: 40px;
        text-align: center;
        border: 1px solid rgba(255, 255, 255, 0.2);
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    }

    .battlepass-title {
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 10px;
        color: #1d1d1f;
        letter-spacing: -0.02em;
    }

    .battlepass-subtitle {
        font-size: 1.2rem;
        color: #86868b;
        font-weight: 400;
    }

    .main-content {
        display: grid;
        grid-template-columns: 1fr 350px;
        gap: 30px;
    }

    .rewards-section {
        background: rgba(255, 255, 255, 0.7);
        backdrop-filter: blur(20px);
        border-radius: 16px;
        padding: 30px;
        border: 1px solid rgba(255, 255, 255, 0.2);
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    }

    .sidebar {
        display: flex;
        flex-direction: column;
        gap: 20px;
    }

    .progress-card, .recent-quests-card {
        background: rgba(255, 255, 255, 0.7);
        backdrop-filter: blur(20px);
        border-radius: 16px;
        padding: 24px;
        border: 1px solid rgba(255, 255, 255, 0.2);
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    }

    .level-badge {
        background: #007aff;
        color: white;
        padding: 12px 20px;
        border-radius: 20px;
        font-weight: 600;
        font-size: 1.1rem;
        display: inline-block;
        margin-bottom: 16px;
        box-shadow: 0 4px 16px rgba(0, 122, 255, 0.3);
    }

    .progress-bar-container {
        background: rgba(0, 0, 0, 0.1);
        border-radius: 12px;
        height: 8px;
        overflow: hidden;
        margin: 16px 0;
    }

    .progress-bar {
        background: linear-gradient(90deg, #007aff 0%, #5ac8fa 100%);
        height: 100%;
        border-radius: 12px;
        transition: width 0.5s ease;
    }

    .level-range-selector {
        display: flex;
        gap: 8px;
        margin-bottom: 24px;
        flex-wrap: wrap;
        justify-content: center;
    }

    .range-btn {
        padding: 8px 16px;
        border: 1px solid rgba(0, 0, 0, 0.1);
        background: rgba(255, 255, 255, 0.8);
        color: #1d1d1f;
        border-radius: 20px;
        cursor: pointer;
        transition: all 0.2s ease;
        font-size: 0.9rem;
        font-weight: 500;
        backdrop-filter: blur(10px);
    }

    .range-btn.active {
        background: #007aff;
        color: white;
        border-color: #007aff;
        box-shadow: 0 2px 8px rgba(0, 122, 255, 0.3);
    }

    .range-btn:hover {
        background: #f2f2f7;
        transform: translateY(-1px);
    }

    .rewards-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
        gap: 20px;
    }

    .reward-card {
        background: rgba(255, 255, 255, 0.8);
        backdrop-filter: blur(20px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 16px;
        padding: 24px;
        transition: all 0.3s ease;
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    }

    .reward-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
    }

    .reward-card.claimed {
        background: rgba(52, 199, 89, 0.1);
        border-color: rgba(52, 199, 89, 0.3);
    }

    .reward-card.available {
        background: rgba(0, 122, 255, 0.1);
        border-color: rgba(0, 122, 255, 0.3);
        box-shadow: 0 4px 16px rgba(0, 122, 255, 0.2);
    }

    .reward-card.locked {
        opacity: 0.6;
        background: rgba(142, 142, 147, 0.1);
        border-color: rgba(142, 142, 147, 0.2);
    }

    .reward-level {
        background: #007aff;
        color: white;
        padding: 6px 12px;
        border-radius: 12px;
        font-size: 0.9rem;
        font-weight: 600;
        display: inline-block;
        margin-bottom: 16px;
    }

    .reward-items {
        display: flex;
        flex-direction: column;
        gap: 12px;
        margin-bottom: 20px;
    }

    .reward-item {
        display: flex;
        align-items: center;
        gap: 12px;
        padding: 12px;
        background: rgba(248, 248, 248, 0.8);
        border-radius: 12px;
        border: 1px solid rgba(0, 0, 0, 0.05);
        transition: all 0.2s ease;
    }

    .reward-item:hover {
        background: rgba(242, 242, 247, 0.9);
        transform: translateY(-1px);
    }

    .item-icon {
        width: 48px;
        height: 48px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: #007aff;
        overflow: hidden;
        flex-shrink: 0;
    }

    .item-icon img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        border-radius: 8px;
    }

    .item-details h4 {
        margin: 0 0 4px 0;
        color: #1d1d1f;
        font-size: 1rem;
        font-weight: 600;
    }

    .item-details p {
        margin: 0;
        color: #86868b;
        font-size: 0.85rem;
    }

    .claim-btn {
        width: 100%;
        padding: 12px;
        border: none;
        border-radius: 12px;
        font-weight: 600;
        font-size: 1rem;
        cursor: pointer;
        transition: all 0.2s ease;
    }

    .claim-btn.available {
        background: #007aff;
        color: white;
    }

    .claim-btn.available:hover {
        background: #0056cc;
        transform: translateY(-1px);
    }

    .claim-btn.claimed {
        background: #34c759;
        color: white;
        cursor: not-allowed;
    }

    .claim-btn.locked {
        background: #f2f2f7;
        color: #8e8e93;
        cursor: not-allowed;
        border: 1px solid rgba(142, 142, 147, 0.2);
    }

    .recent-quests-list {
        max-height: 300px;
        overflow-y: auto;
    }

    .quest-item {
        padding: 10px;
        border-bottom: 1px solid var(--border-color);
        font-size: 0.9rem;
    }

    .quest-item:last-child {
        border-bottom: none;
    }

    .quest-name {
        font-weight: bold;
        color: var(--text-primary);
    }

    .quest-user {
        color: var(--text-secondary);
        font-size: 0.8rem;
    }

    .quest-time {
        color: var(--text-secondary);
        font-size: 0.75rem;
    }

    .quest-item {
        padding: 12px;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        font-size: 0.9rem;
        color: #1d1d1f;
        transition: all 0.2s ease;
        border-radius: 8px;
        margin-bottom: 4px;
    }

    .quest-item:hover {
        background: rgba(242, 242, 247, 0.8);
    }

    .quest-item:last-child {
        border-bottom: none;
    }

    .quest-name {
        font-weight: 600;
        color: #1d1d1f;
        margin-bottom: 4px;
    }

    .quest-user {
        color: #86868b;
        font-size: 0.8rem;
        margin-bottom: 2px;
    }

    .quest-time {
        color: #86868b;
        font-size: 0.75rem;
    }

    /* 移除粒子效果背景 */



    /* 移动端适配 */
    @media (max-width: 768px) {
        .main-content {
            grid-template-columns: 1fr;
        }

        .battlepass-container {
            padding: 15px;
        }

        .rewards-grid {
            grid-template-columns: 1fr;
        }

        .level-range-selector {
            justify-content: center;
        }

        .battlepass-title {
            font-size: 2rem;
        }

        .battlepass-header {
            padding: 25px;
        }
    }

    /* 返回按钮样式 */
    .back-button {
        position: fixed;
        top: 20px;
        left: 20px;
        background: rgba(255, 255, 255, 0.9);
        color: #1d1d1f;
        border: 1px solid rgba(0, 0, 0, 0.1);
        padding: 8px 16px;
        border-radius: 20px;
        font-weight: 600;
        cursor: pointer;
        z-index: 1000;
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
        backdrop-filter: blur(20px);
        font-size: 0.9rem;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 8px;
    }

    .back-button:hover {
        background: rgba(255, 255, 255, 1);
        transform: translateY(-1px);
        box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
        text-decoration: none;
    }
</style>
</head>
<body>
<!-- 返回按钮 -->
<a href="/home" class="back-button">
    <i class="fas fa-arrow-left"></i>
    返回主页
</a>

<div class="battlepass-container">
    <!-- 通行证头部 -->
    <div class="battlepass-header">
        <h1 class="battlepass-title">
            <i class="fas fa-trophy"></i>
            战斗通行证
        </h1>
        <p>完成任务，获得经验，手动领取丰厚奖励</p>
    </div>

    <div class="main-content">
        <!-- 主要奖励区域 -->
        <div class="rewards-section">
            <h2 style="margin-bottom: 25px; color: white; font-size: 1.8rem; text-shadow: 1px 1px 2px rgba(0,0,0,0.3);">
                <i class="fas fa-gift" style="color: #feca57; margin-right: 10px;"></i>
                等级奖励
                <span style="font-size: 0.8rem; color: rgba(255,255,255,0.7); margin-left: 15px;">
                    <i class="fas fa-hand-paper"></i> 手动领取
                </span>
            </h2>

            <!-- 等级范围选择器 -->
            <div class="level-range-selector">
                <button class="range-btn active" data-range="current">
                    当前范围 ({{ battlepass.current_level - 5 }}-{{ battlepass.current_level + 5 }})
                </button>
                <button class="range-btn" data-range="all">显示全部</button>
                <button class="range-btn" data-range="1-10">1-10级</button>
                <button class="range-btn" data-range="11-20">11-20级</button>
                <button class="range-btn" data-range="21-30">21-30级</button>
                <button class="range-btn" data-range="31-40">31-40级</button>
                <button class="range-btn" data-range="41-50">41-50级</button>
            </div>

            <!-- 奖励网格 -->
            <div class="rewards-grid" id="rewards-grid">
                {% for level_config in levels %}
                    {% set level_rewards = rewards_by_level.get(level_config.level, []) %}
                    {% set claimed_levels = claimed_rewards|map(attribute='level')|list %}
                    {% set is_claimed = level_config.level in claimed_levels %}
                    {% set is_available = level_config.level <= battlepass.current_level and not is_claimed %}
                    {% set is_locked = level_config.level > battlepass.current_level %}
                    
                    <div class="reward-card {% if is_claimed %}claimed{% elif is_available %}available{% else %}locked{% endif %}" 
                         data-level="{{ level_config.level }}">
                        
                        <div class="reward-level">等级 {{ level_config.level }}</div>

                        <div class="reward-items">
                            <!-- 积分奖励 -->
                            {% if level_config.points_reward > 0 %}
                                <div class="reward-item">
                                    <div class="item-icon">
                                        <i class="fas fa-coins" style="color: #ffd700; font-size: 1.5rem;"></i>
                                    </div>
                                    <div class="item-details">
                                        <h4>{{ level_config.points_reward | format_number }} 积分</h4>
                                        <p>升级奖励积分</p>
                                    </div>
                                </div>
                            {% endif %}

                            <!-- 物品奖励 -->
                            {% for reward in level_rewards %}
                                <div class="reward-item">
                                    <div class="item-icon">
                                        {% if reward.image_path %}
                                            <img src="{{ reward.image_path }}"
                                                 alt="{{ reward.item_name }}"
                                                 onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                                            <div style="display: none; width: 100%; height: 100%; align-items: center; justify-content: center; background: #4CAF50; color: white;">
                                                <i class="fas fa-cube"></i>
                                            </div>
                                        {% else %}
                                            <i class="fas fa-cube" style="color: #4CAF50; font-size: 1.5rem;"></i>
                                        {% endif %}
                                    </div>
                                    <div class="item-details">
                                        <h4>{{ reward.item_name }}</h4>
                                        <p>数量: {{ reward.quantity }}</p>
                                    </div>
                                </div>
                            {% endfor %}
                        </div>

                        <!-- 领取按钮 -->
                        <button class="claim-btn {% if is_claimed %}claimed{% elif is_available %}available{% else %}locked{% endif %}"
                                onclick="claimReward({{ level_config.level }})"
                                {% if is_claimed or is_locked %}disabled{% endif %}>
                            {% if is_claimed %}
                                <i class="fas fa-check"></i> 已领取
                            {% elif is_available %}
                                <i class="fas fa-hand-paper"></i> 点击领取
                            {% else %}
                                <i class="fas fa-lock"></i> 未解锁
                            {% endif %}
                        </button>
                    </div>
                {% endfor %}
            </div>
        </div>

        <!-- 侧边栏 -->
        <div class="sidebar">
            <!-- 进度卡片 -->
            <div class="progress-card">
                <h3 style="margin-bottom: 16px; color: #1d1d1f; font-size: 1.3rem; font-weight: 700;">
                    <i class="fas fa-chart-line" style="color: #007aff; margin-right: 8px;"></i>
                    我的进度
                </h3>
                
                <div class="level-badge">
                    <i class="fas fa-star"></i>
                    等级 {{ battlepass.current_level }}
                </div>

                <div style="margin-bottom: 12px; color: #1d1d1f; font-size: 1rem;">
                    <i class="fas fa-star" style="color: #ff9500; margin-right: 8px;"></i>
                    <strong>总经验:</strong> {{ battlepass.total_exp | format_number }}
                </div>

                {% if next_level_exp > current_level_exp %}
                    <div style="margin-bottom: 12px; color: #1d1d1f; font-size: 1rem;">
                        <i class="fas fa-arrow-up" style="color: #007aff; margin-right: 8px;"></i>
                        <strong>下级需要:</strong> {{ next_level_exp | format_number }}
                    </div>

                    <div class="progress-bar-container">
                        <div class="progress-bar" style="width: {{ level_progress }}%"></div>
                    </div>

                    <div style="text-align: center; font-size: 0.9rem; color: #86868b; font-weight: 500;">
                        {{ (battlepass.total_exp|int - current_level_exp|int) | format_number }} / {{ (next_level_exp|int - current_level_exp|int) | format_number }} 经验
                        <br><span style="color: #007aff; font-weight: 600;">({{ "%.1f" | format(level_progress) }}%)</span>
                    </div>
                {% else %}
                    <div style="text-align: center; color: #34c759; font-weight: 600; font-size: 1.1rem;">
                        <i class="fas fa-crown" style="color: #ff9500;"></i>
                        已达到最高等级！
                    </div>
                {% endif %}

                <div style="margin-top: 16px; padding: 12px; background: rgba(0, 122, 255, 0.1); color: #007aff; border-radius: 12px; text-align: center; border: 1px solid rgba(0, 122, 255, 0.2);">
                    <i class="fas fa-tasks" style="margin-right: 8px; font-size: 1rem;"></i>
                    <strong>今日任务: {{ battlepass.daily_quest_count }}/{{ config.daily_quest_limit }}</strong>
                </div>
            </div>

            <!-- 近期任务记录 -->
            <div class="recent-quests-card">
                <h3 style="margin-bottom: 16px; color: #1d1d1f; font-size: 1.3rem; font-weight: 700;">
                    <i class="fas fa-history" style="color: #007aff; margin-right: 8px;"></i>
                    近期任务
                </h3>
                
                <div class="recent-quests-list" id="recent-quests-list">
                    <!-- 这里将通过JavaScript动态加载 -->
                    <div style="text-align: center; color: var(--text-secondary);">
                        <i class="fas fa-spinner fa-spin"></i>
                        加载中...
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const currentLevel = {{ battlepass.current_level }};
    
    // 初始化显示当前等级范围
    showLevelRange(Math.max(1, currentLevel - 5), Math.min(50, currentLevel + 5));
    
    // 等级范围选择器事件
    document.querySelectorAll('.range-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            document.querySelectorAll('.range-btn').forEach(b => b.classList.remove('active'));
            this.classList.add('active');
            
            const range = this.dataset.range;
            if (range === 'current') {
                showLevelRange(Math.max(1, currentLevel - 5), Math.min(50, currentLevel + 5));
            } else if (range === 'all') {
                showLevelRange(1, 50);
            } else {
                const [start, end] = range.split('-').map(Number);
                showLevelRange(start, end);
            }
        });
    });
    
    // 加载近期任务记录
    loadRecentQuests();
});

function showLevelRange(start, end) {
    const cards = document.querySelectorAll('.reward-card');
    cards.forEach(card => {
        const level = parseInt(card.dataset.level);
        if (level >= start && level <= end) {
            card.style.display = 'block';
        } else {
            card.style.display = 'none';
        }
    });
}

function claimReward(level) {
    fetch(`/api/battlepass/claim/${level}`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showMessage('success', data.message);
            setTimeout(() => location.reload(), 1500);
        } else {
            showMessage('error', data.message);
        }
    })
    .catch(error => {
        showMessage('error', '请求失败: ' + error.message);
    });
}

function loadRecentQuests() {
    fetch('/api/battlepass/recent-quests')
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const container = document.getElementById('recent-quests-list');
            if (data.data.length === 0) {
                container.innerHTML = '<div style="text-align: center; color: var(--text-secondary);">暂无记录</div>';
                return;
            }
            
            container.innerHTML = data.data.map(quest => `
                <div class="quest-item">
                    <div class="quest-name">${quest.quest_name}</div>
                    <div class="quest-user">玩家: ${quest.username}</div>
                    <div class="quest-time">${new Date(quest.completed_at).toLocaleString()}</div>
                </div>
            `).join('');
        } else {
            document.getElementById('recent-quests-list').innerHTML = 
                '<div style="text-align: center; color: var(--error-color);">加载失败</div>';
        }
    })
    .catch(error => {
        document.getElementById('recent-quests-list').innerHTML = 
            '<div style="text-align: center; color: var(--error-color);">加载失败</div>';
    });
}

function showMessage(type, message) {
    // 创建消息提示
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type}`;
    alertDiv.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 15px 20px;
        border-radius: 5px;
        z-index: 9999;
        max-width: 300px;
        ${type === 'success' ? 'background: var(--success-color); color: white;' : 'background: var(--error-color); color: white;'}
    `;
    alertDiv.textContent = message;
    
    document.body.appendChild(alertDiv);
    
    setTimeout(() => {
        alertDiv.remove();
    }, 3000);
}
</script>
</body>
</html>
