{% extends "base.html" %}

{% block title %}通行证 - 7788商城{% endblock %}

{% block head_extra %}
<style>
    .battlepass-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 20px;
    }

    .battlepass-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 15px;
        padding: 30px;
        color: white;
        margin-bottom: 30px;
        text-align: center;
    }

    .battlepass-title {
        font-size: 2.5rem;
        font-weight: bold;
        margin-bottom: 10px;
    }

    .main-content {
        display: grid;
        grid-template-columns: 1fr 300px;
        gap: 30px;
    }

    .rewards-section {
        background: var(--card-bg);
        border-radius: 15px;
        padding: 25px;
        border: 1px solid var(--border-color);
    }

    .sidebar {
        display: flex;
        flex-direction: column;
        gap: 20px;
    }

    .progress-card, .recent-quests-card {
        background: var(--card-bg);
        border-radius: 15px;
        padding: 20px;
        border: 1px solid var(--border-color);
    }

    .level-badge {
        background: linear-gradient(135deg, #ffd700, #ffed4e);
        color: #333;
        padding: 10px 20px;
        border-radius: 25px;
        font-weight: bold;
        font-size: 1.2rem;
        display: inline-block;
        margin-bottom: 15px;
    }

    .progress-bar-container {
        background: var(--bg-secondary);
        border-radius: 10px;
        height: 20px;
        overflow: hidden;
        margin: 15px 0;
    }

    .progress-bar {
        background: linear-gradient(90deg, #4CAF50, #45a049);
        height: 100%;
        border-radius: 10px;
        transition: width 0.3s ease;
    }

    .level-range-selector {
        display: flex;
        gap: 10px;
        margin-bottom: 20px;
        flex-wrap: wrap;
    }

    .range-btn {
        padding: 8px 15px;
        border: 1px solid var(--border-color);
        background: var(--bg-secondary);
        color: var(--text-primary);
        border-radius: 20px;
        cursor: pointer;
        transition: all 0.3s ease;
        font-size: 0.9rem;
    }

    .range-btn.active {
        background: var(--accent-color);
        color: white;
        border-color: var(--accent-color);
    }

    .range-btn:hover {
        background: var(--accent-color);
        color: white;
    }

    .rewards-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
        gap: 20px;
    }

    .reward-card {
        background: var(--bg-secondary);
        border: 1px solid var(--border-color);
        border-radius: 12px;
        padding: 20px;
        transition: all 0.3s ease;
        position: relative;
    }

    .reward-card.claimed {
        background: rgba(76, 175, 80, 0.1);
        border-color: var(--success-color);
    }

    .reward-card.available {
        border-color: var(--accent-color);
        box-shadow: 0 0 15px rgba(102, 126, 234, 0.3);
    }

    .reward-card.locked {
        opacity: 0.6;
    }

    .reward-level {
        background: var(--accent-color);
        color: white;
        padding: 5px 12px;
        border-radius: 15px;
        font-size: 0.9rem;
        font-weight: bold;
        display: inline-block;
        margin-bottom: 15px;
    }

    .reward-items {
        display: flex;
        flex-direction: column;
        gap: 10px;
        margin-bottom: 15px;
    }

    .reward-item {
        display: flex;
        align-items: center;
        gap: 12px;
        padding: 8px;
        background: var(--card-bg);
        border-radius: 8px;
    }

    .item-icon {
        width: 50px;
        height: 50px;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: var(--bg-secondary);
        overflow: hidden;
    }

    .item-icon img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    .item-details h4 {
        margin: 0 0 5px 0;
        color: var(--text-primary);
        font-size: 0.95rem;
    }

    .item-details p {
        margin: 0;
        color: var(--text-secondary);
        font-size: 0.85rem;
    }

    .claim-btn {
        width: 100%;
        padding: 10px;
        border: none;
        border-radius: 8px;
        font-weight: bold;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .claim-btn.available {
        background: var(--accent-color);
        color: white;
    }

    .claim-btn.claimed {
        background: var(--success-color);
        color: white;
        cursor: not-allowed;
    }

    .claim-btn.locked {
        background: var(--bg-secondary);
        color: var(--text-secondary);
        cursor: not-allowed;
    }

    .recent-quests-list {
        max-height: 300px;
        overflow-y: auto;
    }

    .quest-item {
        padding: 10px;
        border-bottom: 1px solid var(--border-color);
        font-size: 0.9rem;
    }

    .quest-item:last-child {
        border-bottom: none;
    }

    .quest-name {
        font-weight: bold;
        color: var(--text-primary);
    }

    .quest-user {
        color: var(--text-secondary);
        font-size: 0.8rem;
    }

    .quest-time {
        color: var(--text-secondary);
        font-size: 0.75rem;
    }

    /* 移动端适配 */
    @media (max-width: 768px) {
        .main-content {
            grid-template-columns: 1fr;
        }

        .battlepass-container {
            padding: 15px;
        }

        .rewards-grid {
            grid-template-columns: 1fr;
        }

        .level-range-selector {
            justify-content: center;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="battlepass-container">
    <!-- 通行证头部 -->
    <div class="battlepass-header">
        <h1 class="battlepass-title">
            <i class="fas fa-trophy"></i>
            战斗通行证
        </h1>
        <p>完成任务，获得经验，手动领取丰厚奖励</p>
    </div>

    <div class="main-content">
        <!-- 主要奖励区域 -->
        <div class="rewards-section">
            <h2 style="margin-bottom: 20px; color: var(--text-primary);">
                <i class="fas fa-gift"></i>
                等级奖励
            </h2>

            <!-- 等级范围选择器 -->
            <div class="level-range-selector">
                <button class="range-btn active" data-range="current">
                    当前范围 ({{ battlepass.current_level - 5 }}-{{ battlepass.current_level + 5 }})
                </button>
                <button class="range-btn" data-range="all">显示全部</button>
                <button class="range-btn" data-range="1-10">1-10级</button>
                <button class="range-btn" data-range="11-20">11-20级</button>
                <button class="range-btn" data-range="21-30">21-30级</button>
                <button class="range-btn" data-range="31-40">31-40级</button>
                <button class="range-btn" data-range="41-50">41-50级</button>
            </div>

            <!-- 奖励网格 -->
            <div class="rewards-grid" id="rewards-grid">
                {% for level_config in levels %}
                    {% set level_rewards = rewards_by_level.get(level_config.level, []) %}
                    {% set claimed_levels = claimed_rewards|map(attribute='level')|list %}
                    {% set is_claimed = level_config.level in claimed_levels %}
                    {% set is_available = level_config.level <= battlepass.current_level and not is_claimed %}
                    {% set is_locked = level_config.level > battlepass.current_level %}
                    
                    <div class="reward-card {% if is_claimed %}claimed{% elif is_available %}available{% else %}locked{% endif %}" 
                         data-level="{{ level_config.level }}">
                        
                        <div class="reward-level">等级 {{ level_config.level }}</div>

                        <div class="reward-items">
                            <!-- 积分奖励 -->
                            {% if level_config.points_reward > 0 %}
                                <div class="reward-item">
                                    <div class="item-icon">
                                        <i class="fas fa-coins" style="color: #ffd700; font-size: 1.5rem;"></i>
                                    </div>
                                    <div class="item-details">
                                        <h4>{{ level_config.points_reward | format_number }} 积分</h4>
                                        <p>升级奖励积分</p>
                                    </div>
                                </div>
                            {% endif %}

                            <!-- 物品奖励 -->
                            {% for reward in level_rewards %}
                                <div class="reward-item">
                                    <div class="item-icon">
                                        {% if reward.item_id %}
                                            <img src="/static/images/items/{{ reward.item_id }}.png" 
                                                 alt="{{ reward.item_name }}" 
                                                 onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                                            <div style="display: none; width: 100%; height: 100%; align-items: center; justify-content: center; background: #4CAF50; color: white;">
                                                <i class="fas fa-cube"></i>
                                            </div>
                                        {% else %}
                                            <i class="fas fa-cube" style="color: #4CAF50; font-size: 1.5rem;"></i>
                                        {% endif %}
                                    </div>
                                    <div class="item-details">
                                        <h4>{{ reward.item_name }}</h4>
                                        <p>数量: {{ reward.quantity }}</p>
                                    </div>
                                </div>
                            {% endfor %}
                        </div>

                        <!-- 领取按钮 -->
                        <button class="claim-btn {% if is_claimed %}claimed{% elif is_available %}available{% else %}locked{% endif %}"
                                onclick="claimReward({{ level_config.level }})"
                                {% if is_claimed or is_locked %}disabled{% endif %}>
                            {% if is_claimed %}
                                <i class="fas fa-check"></i> 已领取
                            {% elif is_available %}
                                <i class="fas fa-hand-paper"></i> 点击领取
                            {% else %}
                                <i class="fas fa-lock"></i> 未解锁
                            {% endif %}
                        </button>
                    </div>
                {% endfor %}
            </div>
        </div>

        <!-- 侧边栏 -->
        <div class="sidebar">
            <!-- 进度卡片 -->
            <div class="progress-card">
                <h3 style="margin-bottom: 15px; color: var(--text-primary);">
                    <i class="fas fa-chart-line"></i>
                    我的进度
                </h3>
                
                <div class="level-badge">
                    <i class="fas fa-star"></i>
                    等级 {{ battlepass.current_level }}
                </div>

                <div style="margin-bottom: 10px;">
                    <strong>总经验:</strong> {{ battlepass.total_exp | format_number }}
                </div>
                
                {% if next_level_exp > current_level_exp %}
                    <div style="margin-bottom: 10px;">
                        <strong>下级需要:</strong> {{ next_level_exp | format_number }}
                    </div>
                    
                    <div class="progress-bar-container">
                        <div class="progress-bar" style="width: {{ level_progress }}%"></div>
                    </div>
                    
                    <div style="text-align: center; font-size: 0.9rem; color: var(--text-secondary);">
                        {{ (battlepass.total_exp|int - current_level_exp|int) | format_number }} / {{ (next_level_exp|int - current_level_exp|int) | format_number }} 经验
                        ({{ "%.1f" | format(level_progress) }}%)
                    </div>
                {% else %}
                    <div style="text-align: center; color: var(--success-color); font-weight: bold;">
                        已达到最高等级！
                    </div>
                {% endif %}

                <div style="margin-top: 15px; padding: 10px; background: var(--accent-color); color: white; border-radius: 8px; text-align: center;">
                    <i class="fas fa-tasks"></i>
                    今日任务: {{ battlepass.daily_quest_count }}/{{ config.daily_quest_limit }}
                </div>
            </div>

            <!-- 近期任务记录 -->
            <div class="recent-quests-card">
                <h3 style="margin-bottom: 15px; color: var(--text-primary);">
                    <i class="fas fa-history"></i>
                    近期任务
                </h3>
                
                <div class="recent-quests-list" id="recent-quests-list">
                    <!-- 这里将通过JavaScript动态加载 -->
                    <div style="text-align: center; color: var(--text-secondary);">
                        <i class="fas fa-spinner fa-spin"></i>
                        加载中...
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const currentLevel = {{ battlepass.current_level }};
    
    // 初始化显示当前等级范围
    showLevelRange(Math.max(1, currentLevel - 5), Math.min(50, currentLevel + 5));
    
    // 等级范围选择器事件
    document.querySelectorAll('.range-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            document.querySelectorAll('.range-btn').forEach(b => b.classList.remove('active'));
            this.classList.add('active');
            
            const range = this.dataset.range;
            if (range === 'current') {
                showLevelRange(Math.max(1, currentLevel - 5), Math.min(50, currentLevel + 5));
            } else if (range === 'all') {
                showLevelRange(1, 50);
            } else {
                const [start, end] = range.split('-').map(Number);
                showLevelRange(start, end);
            }
        });
    });
    
    // 加载近期任务记录
    loadRecentQuests();
});

function showLevelRange(start, end) {
    const cards = document.querySelectorAll('.reward-card');
    cards.forEach(card => {
        const level = parseInt(card.dataset.level);
        if (level >= start && level <= end) {
            card.style.display = 'block';
        } else {
            card.style.display = 'none';
        }
    });
}

function claimReward(level) {
    fetch(`/api/battlepass/claim/${level}`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showMessage('success', data.message);
            setTimeout(() => location.reload(), 1500);
        } else {
            showMessage('error', data.message);
        }
    })
    .catch(error => {
        showMessage('error', '请求失败: ' + error.message);
    });
}

function loadRecentQuests() {
    fetch('/api/battlepass/recent-quests')
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const container = document.getElementById('recent-quests-list');
            if (data.data.length === 0) {
                container.innerHTML = '<div style="text-align: center; color: var(--text-secondary);">暂无记录</div>';
                return;
            }
            
            container.innerHTML = data.data.map(quest => `
                <div class="quest-item">
                    <div class="quest-name">${quest.quest_name}</div>
                    <div class="quest-user">玩家: ${quest.username}</div>
                    <div class="quest-time">${new Date(quest.completed_at).toLocaleString()}</div>
                </div>
            `).join('');
        } else {
            document.getElementById('recent-quests-list').innerHTML = 
                '<div style="text-align: center; color: var(--error-color);">加载失败</div>';
        }
    })
    .catch(error => {
        document.getElementById('recent-quests-list').innerHTML = 
            '<div style="text-align: center; color: var(--error-color);">加载失败</div>';
    });
}

function showMessage(type, message) {
    // 创建消息提示
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type}`;
    alertDiv.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 15px 20px;
        border-radius: 5px;
        z-index: 9999;
        max-width: 300px;
        ${type === 'success' ? 'background: var(--success-color); color: white;' : 'background: var(--error-color); color: white;'}
    `;
    alertDiv.textContent = message;
    
    document.body.appendChild(alertDiv);
    
    setTimeout(() => {
        alertDiv.remove();
    }, 3000);
}
</script>
{% endblock %}
