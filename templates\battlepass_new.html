{% extends "base.html" %}

{% block title %}战斗通行证 - 7788商城{% endblock %}

{% block head_extra %}
<style>
    body {
        background: linear-gradient(135deg, #ff6b6b 0%, #feca57 25%, #48dbfb 50%, #ff9ff3 75%, #54a0ff 100%);
        min-height: 100vh;
        animation: backgroundShift 10s ease-in-out infinite alternate;
    }

    @keyframes backgroundShift {
        0% { background: linear-gradient(135deg, #ff6b6b 0%, #feca57 25%, #48dbfb 50%, #ff9ff3 75%, #54a0ff 100%); }
        100% { background: linear-gradient(135deg, #54a0ff 0%, #ff9ff3 25%, #48dbfb 50%, #feca57 75%, #ff6b6b 100%); }
    }

    .battlepass-container {
        max-width: 1400px;
        margin: 0 auto;
        padding: 20px;
    }

    .battlepass-header {
        background: linear-gradient(135deg, #ff6b6b 0%, #feca57 25%, #48dbfb 50%, #ff9ff3 75%, #54a0ff 100%);
        border-radius: 20px;
        padding: 40px;
        color: white;
        margin-bottom: 30px;
        text-align: center;
        box-shadow: 0 20px 40px rgba(0,0,0,0.3);
        position: relative;
        overflow: hidden;
    }

    .battlepass-header::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
        animation: shine 3s infinite;
    }

    @keyframes shine {
        0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
        100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
    }

    .battlepass-title {
        font-size: 3rem;
        font-weight: bold;
        margin-bottom: 15px;
        text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
        position: relative;
        z-index: 1;
    }

    .battlepass-subtitle {
        font-size: 1.4rem;
        opacity: 0.95;
        position: relative;
        z-index: 1;
        text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
    }

    .main-content {
        display: grid;
        grid-template-columns: 1fr 350px;
        gap: 30px;
    }

    .rewards-section {
        background: linear-gradient(135deg, rgba(255,107,107,0.2) 0%, rgba(254,202,87,0.2) 25%, rgba(72,219,251,0.2) 50%, rgba(255,159,243,0.2) 75%, rgba(84,160,255,0.2) 100%);
        backdrop-filter: blur(15px);
        border-radius: 20px;
        padding: 30px;
        border: 3px solid rgba(255,255,255,0.4);
        box-shadow: 0 20px 40px rgba(255,107,107,0.3);
    }

    .sidebar {
        display: flex;
        flex-direction: column;
        gap: 25px;
    }

    .progress-card, .recent-quests-card {
        background: linear-gradient(135deg, rgba(255,159,243,0.3) 0%, rgba(84,160,255,0.2) 50%, rgba(72,219,251,0.2) 100%);
        backdrop-filter: blur(15px);
        border-radius: 20px;
        padding: 25px;
        border: 3px solid rgba(255,159,243,0.4);
        box-shadow: 0 15px 35px rgba(255,159,243,0.3);
    }

    .level-badge {
        background: linear-gradient(135deg, #ff6b6b 0%, #feca57 50%, #ff9ff3 100%);
        color: white;
        padding: 15px 25px;
        border-radius: 30px;
        font-weight: bold;
        font-size: 1.3rem;
        display: inline-block;
        margin-bottom: 20px;
        text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
        box-shadow: 0 8px 20px rgba(255,107,107,0.4);
        animation: levelPulse 2s infinite;
    }

    @keyframes levelPulse {
        0%, 100% { transform: scale(1); }
        50% { transform: scale(1.05); }
    }

    .progress-bar-container {
        background: rgba(255,255,255,0.2);
        border-radius: 15px;
        height: 25px;
        overflow: hidden;
        margin: 20px 0;
        box-shadow: inset 0 2px 4px rgba(0,0,0,0.2);
    }

    .progress-bar {
        background: linear-gradient(90deg, #ff6b6b 0%, #feca57 50%, #48dbfb 100%);
        height: 100%;
        border-radius: 15px;
        transition: width 0.5s ease;
        position: relative;
        overflow: hidden;
    }

    .progress-bar::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
        animation: progressShine 2s infinite;
    }

    @keyframes progressShine {
        0% { left: -100%; }
        100% { left: 100%; }
    }

    .level-range-selector {
        display: flex;
        gap: 12px;
        margin-bottom: 25px;
        flex-wrap: wrap;
        justify-content: center;
    }

    .range-btn {
        padding: 12px 20px;
        border: 2px solid transparent;
        background: linear-gradient(135deg, rgba(255,255,255,0.1), rgba(255,255,255,0.05));
        color: white;
        border-radius: 25px;
        cursor: pointer;
        transition: all 0.3s ease;
        font-size: 0.95rem;
        font-weight: 600;
        backdrop-filter: blur(10px);
        box-shadow: 0 4px 15px rgba(0,0,0,0.2);
    }

    .range-btn.active {
        background: linear-gradient(135deg, #ff6b6b 0%, #feca57 100%);
        border-color: rgba(255,255,255,0.3);
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(255,107,107,0.4);
    }

    .range-btn:hover {
        background: linear-gradient(135deg, #48dbfb 0%, #0abde3 100%);
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(72,219,251,0.4);
    }

    .rewards-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
        gap: 25px;
    }

    .reward-card {
        background: linear-gradient(135deg, rgba(72,219,251,0.2) 0%, rgba(84,160,255,0.2) 50%, rgba(255,159,243,0.2) 100%);
        backdrop-filter: blur(15px);
        border: 3px solid rgba(72,219,251,0.4);
        border-radius: 20px;
        padding: 25px;
        transition: all 0.4s ease;
        position: relative;
        overflow: hidden;
        box-shadow: 0 10px 30px rgba(72,219,251,0.3);
    }

    .reward-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
        transition: left 0.6s;
    }

    .reward-card:hover::before {
        left: 100%;
    }

    .reward-card.claimed {
        background: linear-gradient(135deg, rgba(76,175,80,0.3) 0%, rgba(76,175,80,0.15) 100%);
        border-color: #4CAF50;
        box-shadow: 0 10px 30px rgba(76,175,80,0.3);
    }

    .reward-card.available {
        background: linear-gradient(135deg, rgba(255,107,107,0.2) 0%, rgba(254,202,87,0.2) 50%, rgba(72,219,251,0.2) 100%);
        border-color: #ff6b6b;
        box-shadow: 0 15px 40px rgba(255,107,107,0.4);
        animation: availablePulse 2s infinite;
    }

    @keyframes availablePulse {
        0%, 100% { transform: scale(1); box-shadow: 0 15px 40px rgba(255,107,107,0.4); }
        50% { transform: scale(1.02); box-shadow: 0 20px 50px rgba(255,107,107,0.6); }
    }

    .reward-card.locked {
        opacity: 0.6;
        background: linear-gradient(135deg, rgba(255,107,107,0.1) 0%, rgba(254,202,87,0.1) 50%, rgba(72,219,251,0.1) 100%);
        border-color: rgba(255,107,107,0.3);
        box-shadow: 0 10px 30px rgba(255,107,107,0.2);
    }

    .reward-level {
        background: linear-gradient(135deg, #ff6b6b 0%, #feca57 100%);
        color: white;
        padding: 8px 16px;
        border-radius: 20px;
        font-size: 1rem;
        font-weight: bold;
        display: inline-block;
        margin-bottom: 20px;
        text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
        box-shadow: 0 4px 15px rgba(255,107,107,0.3);
    }

    .reward-items {
        display: flex;
        flex-direction: column;
        gap: 15px;
        margin-bottom: 20px;
    }

    .reward-item {
        display: flex;
        align-items: center;
        gap: 15px;
        padding: 12px;
        background: linear-gradient(135deg, rgba(254,202,87,0.15) 0%, rgba(255,159,243,0.1) 50%, rgba(84,160,255,0.1) 100%);
        backdrop-filter: blur(10px);
        border-radius: 12px;
        border: 2px solid rgba(254,202,87,0.3);
        transition: all 0.3s ease;
    }

    .reward-item:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(254,202,87,0.4);
        border-color: rgba(254,202,87,0.5);
    }

    .item-icon {
        width: 60px;
        height: 60px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: linear-gradient(135deg, #48dbfb 0%, #0abde3 100%);
        overflow: hidden;
        box-shadow: 0 4px 15px rgba(72,219,251,0.3);
        position: relative;
    }

    .item-icon::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: linear-gradient(45deg, transparent, rgba(255,255,255,0.2), transparent);
        animation: iconShine 3s infinite;
    }

    @keyframes iconShine {
        0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
        100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
    }

    .item-icon img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        border-radius: 8px;
        position: relative;
        z-index: 1;
    }

    .item-details h4 {
        margin: 0 0 8px 0;
        color: white;
        font-size: 1.1rem;
        font-weight: bold;
        text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
    }

    .item-details p {
        margin: 0;
        color: rgba(255,255,255,0.8);
        font-size: 0.9rem;
    }

    .claim-btn {
        width: 100%;
        padding: 15px;
        border: none;
        border-radius: 15px;
        font-weight: bold;
        font-size: 1.1rem;
        cursor: pointer;
        transition: all 0.4s ease;
        position: relative;
        overflow: hidden;
        text-transform: uppercase;
        letter-spacing: 1px;
    }

    .claim-btn::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
        transition: left 0.6s;
    }

    .claim-btn:hover::before {
        left: 100%;
    }

    .claim-btn.available {
        background: linear-gradient(135deg, #ff6b6b 0%, #feca57 100%);
        color: white;
        box-shadow: 0 8px 25px rgba(255,107,107,0.4);
        text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
    }

    .claim-btn.available:hover {
        transform: translateY(-3px);
        box-shadow: 0 12px 35px rgba(255,107,107,0.6);
    }

    .claim-btn.claimed {
        background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
        color: white;
        cursor: not-allowed;
        box-shadow: 0 8px 25px rgba(76,175,80,0.4);
    }

    .claim-btn.locked {
        background: linear-gradient(135deg, rgba(255,107,107,0.3) 0%, rgba(254,202,87,0.2) 100%);
        color: rgba(255,255,255,0.7);
        cursor: not-allowed;
        box-shadow: 0 4px 15px rgba(255,107,107,0.2);
        border: 2px solid rgba(255,107,107,0.3);
    }

    .recent-quests-list {
        max-height: 300px;
        overflow-y: auto;
    }

    .quest-item {
        padding: 10px;
        border-bottom: 1px solid var(--border-color);
        font-size: 0.9rem;
    }

    .quest-item:last-child {
        border-bottom: none;
    }

    .quest-name {
        font-weight: bold;
        color: var(--text-primary);
    }

    .quest-user {
        color: var(--text-secondary);
        font-size: 0.8rem;
    }

    .quest-time {
        color: var(--text-secondary);
        font-size: 0.75rem;
    }

    .quest-item {
        padding: 12px;
        border-bottom: 1px solid rgba(255,255,255,0.1);
        font-size: 0.95rem;
        color: white;
        transition: all 0.3s ease;
        border-radius: 8px;
        margin-bottom: 5px;
    }

    .quest-item:hover {
        background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
        transform: translateX(5px);
    }

    .quest-item:last-child {
        border-bottom: none;
    }

    .quest-name {
        font-weight: bold;
        color: white;
        margin-bottom: 4px;
    }

    .quest-user {
        color: rgba(255,255,255,0.8);
        font-size: 0.85rem;
        margin-bottom: 2px;
    }

    .quest-time {
        color: rgba(255,255,255,0.6);
        font-size: 0.8rem;
    }

    /* 添加粒子效果背景 */
    .battlepass-container::before {
        content: '';
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-image:
            radial-gradient(circle at 20% 80%, rgba(255,107,107,0.1) 0%, transparent 50%),
            radial-gradient(circle at 80% 20%, rgba(254,202,87,0.1) 0%, transparent 50%),
            radial-gradient(circle at 40% 40%, rgba(72,219,251,0.1) 0%, transparent 50%);
        pointer-events: none;
        z-index: -1;
    }

    /* 移动端适配 */
    @media (max-width: 768px) {
        .main-content {
            grid-template-columns: 1fr;
        }

        .battlepass-container {
            padding: 15px;
        }

        .rewards-grid {
            grid-template-columns: 1fr;
        }

        .level-range-selector {
            justify-content: center;
        }

        .battlepass-title {
            font-size: 2rem;
        }

        .battlepass-header {
            padding: 25px;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="battlepass-container">
    <!-- 通行证头部 -->
    <div class="battlepass-header">
        <h1 class="battlepass-title">
            <i class="fas fa-trophy"></i>
            战斗通行证
        </h1>
        <p>完成任务，获得经验，手动领取丰厚奖励</p>
    </div>

    <div class="main-content">
        <!-- 主要奖励区域 -->
        <div class="rewards-section">
            <h2 style="margin-bottom: 25px; color: white; font-size: 1.8rem; text-shadow: 1px 1px 2px rgba(0,0,0,0.3);">
                <i class="fas fa-gift" style="color: #feca57; margin-right: 10px;"></i>
                等级奖励
                <span style="font-size: 0.8rem; color: rgba(255,255,255,0.7); margin-left: 15px;">
                    <i class="fas fa-hand-paper"></i> 手动领取
                </span>
            </h2>

            <!-- 等级范围选择器 -->
            <div class="level-range-selector">
                <button class="range-btn active" data-range="current">
                    当前范围 ({{ battlepass.current_level - 5 }}-{{ battlepass.current_level + 5 }})
                </button>
                <button class="range-btn" data-range="all">显示全部</button>
                <button class="range-btn" data-range="1-10">1-10级</button>
                <button class="range-btn" data-range="11-20">11-20级</button>
                <button class="range-btn" data-range="21-30">21-30级</button>
                <button class="range-btn" data-range="31-40">31-40级</button>
                <button class="range-btn" data-range="41-50">41-50级</button>
            </div>

            <!-- 奖励网格 -->
            <div class="rewards-grid" id="rewards-grid">
                {% for level_config in levels %}
                    {% set level_rewards = rewards_by_level.get(level_config.level, []) %}
                    {% set claimed_levels = claimed_rewards|map(attribute='level')|list %}
                    {% set is_claimed = level_config.level in claimed_levels %}
                    {% set is_available = level_config.level <= battlepass.current_level and not is_claimed %}
                    {% set is_locked = level_config.level > battlepass.current_level %}
                    
                    <div class="reward-card {% if is_claimed %}claimed{% elif is_available %}available{% else %}locked{% endif %}" 
                         data-level="{{ level_config.level }}">
                        
                        <div class="reward-level">等级 {{ level_config.level }}</div>

                        <div class="reward-items">
                            <!-- 积分奖励 -->
                            {% if level_config.points_reward > 0 %}
                                <div class="reward-item">
                                    <div class="item-icon">
                                        <i class="fas fa-coins" style="color: #ffd700; font-size: 1.5rem;"></i>
                                    </div>
                                    <div class="item-details">
                                        <h4>{{ level_config.points_reward | format_number }} 积分</h4>
                                        <p>升级奖励积分</p>
                                    </div>
                                </div>
                            {% endif %}

                            <!-- 物品奖励 -->
                            {% for reward in level_rewards %}
                                <div class="reward-item">
                                    <div class="item-icon">
                                        {% if reward.image_path %}
                                            <img src="{{ reward.image_path }}"
                                                 alt="{{ reward.item_name }}"
                                                 onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                                            <div style="display: none; width: 100%; height: 100%; align-items: center; justify-content: center; background: #4CAF50; color: white;">
                                                <i class="fas fa-cube"></i>
                                            </div>
                                        {% else %}
                                            <i class="fas fa-cube" style="color: #4CAF50; font-size: 1.5rem;"></i>
                                        {% endif %}
                                    </div>
                                    <div class="item-details">
                                        <h4>{{ reward.item_name }}</h4>
                                        <p>数量: {{ reward.quantity }}</p>
                                    </div>
                                </div>
                            {% endfor %}
                        </div>

                        <!-- 领取按钮 -->
                        <button class="claim-btn {% if is_claimed %}claimed{% elif is_available %}available{% else %}locked{% endif %}"
                                onclick="claimReward({{ level_config.level }})"
                                {% if is_claimed or is_locked %}disabled{% endif %}>
                            {% if is_claimed %}
                                <i class="fas fa-check"></i> 已领取
                            {% elif is_available %}
                                <i class="fas fa-hand-paper"></i> 点击领取
                            {% else %}
                                <i class="fas fa-lock"></i> 未解锁
                            {% endif %}
                        </button>
                    </div>
                {% endfor %}
            </div>
        </div>

        <!-- 侧边栏 -->
        <div class="sidebar">
            <!-- 进度卡片 -->
            <div class="progress-card">
                <h3 style="margin-bottom: 20px; color: white; font-size: 1.4rem; text-shadow: 1px 1px 2px rgba(0,0,0,0.3);">
                    <i class="fas fa-chart-line" style="color: #48dbfb; margin-right: 8px;"></i>
                    我的进度
                </h3>
                
                <div class="level-badge">
                    <i class="fas fa-star"></i>
                    等级 {{ battlepass.current_level }}
                </div>

                <div style="margin-bottom: 15px; color: white; font-size: 1.1rem;">
                    <i class="fas fa-star" style="color: #feca57; margin-right: 8px;"></i>
                    <strong>总经验:</strong> {{ battlepass.total_exp | format_number }}
                </div>

                {% if next_level_exp > current_level_exp %}
                    <div style="margin-bottom: 15px; color: white; font-size: 1.1rem;">
                        <i class="fas fa-arrow-up" style="color: #ff9ff3; margin-right: 8px;"></i>
                        <strong>下级需要:</strong> {{ next_level_exp | format_number }}
                    </div>
                    
                    <div class="progress-bar-container">
                        <div class="progress-bar" style="width: {{ level_progress }}%"></div>
                    </div>
                    
                    <div style="text-align: center; font-size: 1rem; color: rgba(255,255,255,0.9); font-weight: 500;">
                        {{ (battlepass.total_exp|int - current_level_exp|int) | format_number }} / {{ (next_level_exp|int - current_level_exp|int) | format_number }} 经验
                        <br><span style="color: #feca57; font-weight: bold;">({{ "%.1f" | format(level_progress) }}%)</span>
                    </div>
                {% else %}
                    <div style="text-align: center; color: #4CAF50; font-weight: bold; font-size: 1.2rem;">
                        <i class="fas fa-crown" style="color: #ffd700;"></i>
                        已达到最高等级！
                    </div>
                {% endif %}

                <div style="margin-top: 20px; padding: 15px; background: linear-gradient(135deg, #ff9ff3 0%, #f368e0 100%); color: white; border-radius: 15px; text-align: center; box-shadow: 0 8px 20px rgba(255,159,243,0.4);">
                    <i class="fas fa-tasks" style="margin-right: 8px; font-size: 1.2rem;"></i>
                    <strong>今日任务: {{ battlepass.daily_quest_count }}/{{ config.daily_quest_limit }}</strong>
                </div>
            </div>

            <!-- 近期任务记录 -->
            <div class="recent-quests-card">
                <h3 style="margin-bottom: 20px; color: white; font-size: 1.4rem; text-shadow: 1px 1px 2px rgba(0,0,0,0.3);">
                    <i class="fas fa-history" style="color: #54a0ff; margin-right: 8px;"></i>
                    近期任务
                </h3>
                
                <div class="recent-quests-list" id="recent-quests-list">
                    <!-- 这里将通过JavaScript动态加载 -->
                    <div style="text-align: center; color: var(--text-secondary);">
                        <i class="fas fa-spinner fa-spin"></i>
                        加载中...
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const currentLevel = {{ battlepass.current_level }};
    
    // 初始化显示当前等级范围
    showLevelRange(Math.max(1, currentLevel - 5), Math.min(50, currentLevel + 5));
    
    // 等级范围选择器事件
    document.querySelectorAll('.range-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            document.querySelectorAll('.range-btn').forEach(b => b.classList.remove('active'));
            this.classList.add('active');
            
            const range = this.dataset.range;
            if (range === 'current') {
                showLevelRange(Math.max(1, currentLevel - 5), Math.min(50, currentLevel + 5));
            } else if (range === 'all') {
                showLevelRange(1, 50);
            } else {
                const [start, end] = range.split('-').map(Number);
                showLevelRange(start, end);
            }
        });
    });
    
    // 加载近期任务记录
    loadRecentQuests();
});

function showLevelRange(start, end) {
    const cards = document.querySelectorAll('.reward-card');
    cards.forEach(card => {
        const level = parseInt(card.dataset.level);
        if (level >= start && level <= end) {
            card.style.display = 'block';
        } else {
            card.style.display = 'none';
        }
    });
}

function claimReward(level) {
    fetch(`/api/battlepass/claim/${level}`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showMessage('success', data.message);
            setTimeout(() => location.reload(), 1500);
        } else {
            showMessage('error', data.message);
        }
    })
    .catch(error => {
        showMessage('error', '请求失败: ' + error.message);
    });
}

function loadRecentQuests() {
    fetch('/api/battlepass/recent-quests')
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const container = document.getElementById('recent-quests-list');
            if (data.data.length === 0) {
                container.innerHTML = '<div style="text-align: center; color: var(--text-secondary);">暂无记录</div>';
                return;
            }
            
            container.innerHTML = data.data.map(quest => `
                <div class="quest-item">
                    <div class="quest-name">${quest.quest_name}</div>
                    <div class="quest-user">玩家: ${quest.username}</div>
                    <div class="quest-time">${new Date(quest.completed_at).toLocaleString()}</div>
                </div>
            `).join('');
        } else {
            document.getElementById('recent-quests-list').innerHTML = 
                '<div style="text-align: center; color: var(--error-color);">加载失败</div>';
        }
    })
    .catch(error => {
        document.getElementById('recent-quests-list').innerHTML = 
            '<div style="text-align: center; color: var(--error-color);">加载失败</div>';
    });
}

function showMessage(type, message) {
    // 创建消息提示
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type}`;
    alertDiv.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 15px 20px;
        border-radius: 5px;
        z-index: 9999;
        max-width: 300px;
        ${type === 'success' ? 'background: var(--success-color); color: white;' : 'background: var(--error-color); color: white;'}
    `;
    alertDiv.textContent = message;
    
    document.body.appendChild(alertDiv);
    
    setTimeout(() => {
        alertDiv.remove();
    }, 3000);
}
</script>
{% endblock %}
