#!/usr/bin/env python
import os
import json
import time
import requests
import hashlib
from datetime import datetime
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("api_sync.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("api_sync")

# 配置
# API_URL = "http://47.121.131.132:5778/api/all-data"
# 使用本地数据源
API_URL = "http://127.0.0.1:5778/api/all-data"
LOCAL_DATA_DIR = "data"
LOCAL_IMAGES_DIR = "static/uploads"
CHECK_INTERVAL = 60  # 每60秒检查一次，生产环境建议1分钟或更长

# 确保目录存在
os.makedirs(LOCAL_DATA_DIR, exist_ok=True)
os.makedirs(LOCAL_IMAGES_DIR, exist_ok=True)

def get_file_hash(file_path):
    """获取文件的MD5哈希值"""
    if not os.path.exists(file_path):
        return None
    
    with open(file_path, 'rb') as f:
        file_hash = hashlib.md5(f.read()).hexdigest()
    return file_hash

def get_data_hash(data):
    """获取数据的哈希值"""
    if not data:
        return None
    data_str = json.dumps(data, sort_keys=True)
    return hashlib.md5(data_str.encode('utf-8')).hexdigest()

def download_image(image_url, save_path):
    """下载图片并保存到本地"""
    try:
        response = requests.get(image_url, stream=True, timeout=10)
        if response.status_code == 200:
            with open(save_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    f.write(chunk)
            logger.info(f"下载图片成功: {save_path}")
            return True
        else:
            logger.error(f"下载图片失败，状态码: {response.status_code}, URL: {image_url}")
            return False
    except Exception as e:
        logger.error(f"下载图片出错: {e}, URL: {image_url}")
        return False

def process_data(api_data, old_data=None):
    """处理API返回的数据，只下载新增和变更的图片"""
    if not api_data:
        return api_data
    
    # 深拷贝数据以避免修改原始数据
    updated_data = json.loads(json.dumps(api_data))
    
    # 创建旧数据中图片路径的映射，用于判断图片是否已存在
    old_image_paths = {}
    if old_data and 'packages' in old_data and old_data['packages']:
        for package in old_data['packages']:
            if 'imagePath' in package and package['imagePath'] and 'original_imagePath' in package:
                old_image_paths[package['original_imagePath']] = package['imagePath']
    
    # 处理packages中的图片
    new_images_count = 0
    if 'packages' in updated_data and updated_data['packages']:
        for package in updated_data['packages']:
            if 'imagePath' in package and package['imagePath']:
                # 保存原始图片路径
                original_image_path = package['imagePath']
                
                # 检查这个图片是否在旧数据中已经处理过
                if original_image_path in old_image_paths:
                    # 图片已处理过，直接使用旧的本地路径
                    package['original_imagePath'] = original_image_path
                    package['imagePath'] = old_image_paths[original_image_path]
                else:
                    # 这是新图片，需要下载
                    # 使用本地路径而不是远程URL
                    # image_url = f"http://47.121.131.132:5778{original_image_path}"
                    image_url = f"http://127.0.0.1:5778{original_image_path}"
                    
                    # 提取文件名
                    image_filename = os.path.basename(original_image_path)
                    local_image_path = os.path.join(LOCAL_IMAGES_DIR, image_filename)
                    relative_path = f"/static/uploads/{image_filename}"
                    
                    # 检查本地是否已存在图片（防止重复下载）
                    if not os.path.exists(local_image_path):
                        download_image(image_url, local_image_path)
                        new_images_count += 1
                    
                    # 更新图片路径为本地路径
                    package['original_imagePath'] = original_image_path
                    package['imagePath'] = relative_path
    
    if new_images_count > 0:
        logger.info(f"已下载 {new_images_count} 个新图片")
    else:
        logger.info("没有新图片需要下载")
    
    return updated_data

def load_local_data():
    """加载本地缓存的数据"""
    local_data_file = os.path.join(LOCAL_DATA_DIR, "all-data.json")
    if os.path.exists(local_data_file):
        try:
            with open(local_data_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            logger.error(f"读取本地数据出错: {e}")
    return None

def sync_api_data():
    """从API获取数据，处理并保存到本地"""
    local_data_file = os.path.join(LOCAL_DATA_DIR, "all-data.json")
    
    # 加载本地已有数据
    old_data = load_local_data()
    
    try:
        # 从API获取数据
        logger.info(f"正在从API获取数据: {API_URL}")
        response = requests.get(API_URL, timeout=30)
        
        if response.status_code == 200:
            api_data = response.json()
            
            # 检查API数据是否与本地数据相同（除了imagePath字段）
            if old_data:
                # 创建用于比较的数据副本（忽略imagePath和original_imagePath字段）
                api_data_for_compare = json.loads(json.dumps(api_data))
                old_data_for_compare = json.loads(json.dumps(old_data))
                
                # 处理packages中的imagePath字段（用于比较）
                if 'packages' in api_data_for_compare and api_data_for_compare['packages']:
                    for package in api_data_for_compare['packages']:
                        if 'imagePath' in package:
                            # 将ImagePath设为相同的占位符，这样就不会因为路径不同导致数据被认为不同
                            package['imagePath'] = 'PLACEHOLDER'
                
                if 'packages' in old_data_for_compare and old_data_for_compare['packages']:
                    for package in old_data_for_compare['packages']:
                        if 'imagePath' in package:
                            package['imagePath'] = 'PLACEHOLDER'
                        if 'original_imagePath' in package:
                            del package['original_imagePath']
                
                # 检查数据是否有实质性变化
                api_data_hash = get_data_hash(api_data_for_compare)
                old_data_hash = get_data_hash(old_data_for_compare)
                
                if api_data_hash == old_data_hash:
                    logger.info("API数据与本地数据内容相同，无需更新")
                    return False
            
            # 数据有更新，处理新数据并下载图片
            processed_data = process_data(api_data, old_data)
            
            # 将处理后的数据保存到本地
            with open(local_data_file, 'w', encoding='utf-8') as f:
                json.dump(processed_data, f, ensure_ascii=False, indent=2)
            
            logger.info(f"数据已更新，保存到: {local_data_file}")
            return True
        else:
            logger.error(f"API请求失败，状态码: {response.status_code}")
            return False
    except Exception as e:
        logger.error(f"同步数据时出错: {e}")
        return False

def main():
    """主函数，定期同步API数据"""
    logger.info("API同步服务已启动")
    
    # 首次运行立即同步一次
    try:
        sync_result = sync_api_data()
        if sync_result:
            logger.info("首次同步成功")
        else:
            logger.warning("首次同步未成功完成，将在下一周期重试")
    except Exception as e:
        logger.error(f"首次同步过程中发生错误: {e}")
    
    # 定期同步
    while True:
        logger.info(f"等待 {CHECK_INTERVAL} 秒后再次同步...")
        time.sleep(CHECK_INTERVAL)
        
        try:
            sync_api_data()
        except Exception as e:
            logger.error(f"同步过程中发生未预期的错误: {e}")

if __name__ == "__main__":
    main() 