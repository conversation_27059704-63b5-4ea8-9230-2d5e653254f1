#!/bin/bash

# 脚本路径配置
SCRIPT_DIR=$(cd "$(dirname "${BASH_SOURCE[0]}")" &>/dev/null && pwd)
APP_PY="${SCRIPT_DIR}/app.py"
SYNC_PY="${SCRIPT_DIR}/sync_api.py"
PID_DIR="${SCRIPT_DIR}/run"
LOG_DIR="${SCRIPT_DIR}/logs"
REQUIREMENTS_FILE="${SCRIPT_DIR}/requirements.txt" # 新增: requirements 文件路径

# 虚拟环境配置
VENV_DIR="${SCRIPT_DIR}/venv"
PYTHON_BIN="${VENV_DIR}/bin/python"

# PID文件路径
APP_PID="${PID_DIR}/app.pid"
SYNC_PID="${PID_DIR}/sync.pid"

# 日志文件路径
APP_LOG="${LOG_DIR}/app.log"
SYNC_LOG="${LOG_DIR}/sync.log"

# 确保必要目录存在
mkdir -p "${PID_DIR}" "${LOG_DIR}" "${SCRIPT_DIR}/data" "${SCRIPT_DIR}/static/uploads"

# --- 新增: 设置虚拟环境并安装/更新依赖 ---
setup_venv_and_deps() {
    echo "检查虚拟环境和依赖..."

    # 检查 requirements.txt 是否存在
    if [ ! -f "$REQUIREMENTS_FILE" ]; then
        echo "警告: 依赖文件 requirements.txt 未在 ${SCRIPT_DIR} 找到!"
        echo "将无法自动安装或更新依赖。请创建该文件并列出所需包。"
        # 如果没有 requirements.txt，对于新环境是致命的，对于旧环境可能还能运行
    fi

    if [ -d "$VENV_DIR" ] && [ -f "$PYTHON_BIN" ]; then
        # 虚拟环境已存在
        echo "虚拟环境已找到: $VENV_DIR"
        if [ -f "$REQUIREMENTS_FILE" ]; then
            echo "正在从 requirements.txt 更新依赖..."
            # 使用虚拟环境中的 pip 更新依赖
            if ! "${PYTHON_BIN}" -m pip install --upgrade pip > /dev/null 2>&1; then
                 echo "警告: 升级 pip 失败，继续尝试安装依赖..."
            fi
            if ! "${PYTHON_BIN}" -m pip install -r "$REQUIREMENTS_FILE"; then
                echo "警告: 从 requirements.txt 更新依赖失败。请手动检查。"
                echo "你可以尝试手动运行: source \"${VENV_DIR}/bin/activate\" && pip install -r \"${REQUIREMENTS_FILE}\""
                # 不退出，也许旧的依赖仍然可用
            else
                echo "依赖更新完成。"
            fi
        else
             echo "跳过依赖更新，因为 requirements.txt 未找到。"
        fi
    else
        # 虚拟环境不存在，创建它
        echo "虚拟环境未找到，正在创建..."
        # 使用 python3 创建 venv
        if ! python3 -m venv "$VENV_DIR"; then
            echo "错误: 创建虚拟环境失败。请确保 python3 和 venv 模块已安装。"
            exit 1
        fi
        # 再次检查 Python 可执行文件是否存在
        if [ ! -f "$PYTHON_BIN" ]; then
             echo "错误: 虚拟环境已创建但 Python 可执行文件 (${PYTHON_BIN}) 未找到。"
             exit 1
        fi
        echo "虚拟环境已创建于: $VENV_DIR"

        if [ -f "$REQUIREMENTS_FILE" ]; then
            echo "正在从 requirements.txt 安装依赖..."
            # 先升级 pip
             "${PYTHON_BIN}" -m pip install --upgrade pip
            # 使用新创建的虚拟环境中的 pip 安装依赖
            if ! "${PYTHON_BIN}" -m pip install -r "$REQUIREMENTS_FILE"; then
                echo "错误: 从 requirements.txt 安装依赖失败。"
                echo "请检查 ${REQUIREMENTS_FILE} 文件内容和网络连接。"
                echo "你可以尝试手动运行: source \"${VENV_DIR}/bin/activate\" && pip install -r \"${REQUIREMENTS_FILE}\""
                # 新环境必须成功安装依赖才能继续
                exit 1
            else
                echo "依赖安装完成。"
            fi
        else
            echo "错误: requirements.txt 未找到。无法在新虚拟环境中安装依赖。"
            echo "请创建 ${REQUIREMENTS_FILE} 并重新运行脚本，或手动安装依赖。"
            # 新环境没有依赖无法运行
            exit 1
        fi
    fi
    echo "虚拟环境和依赖准备就绪。"
}

# 检查程序是否已经在运行 (保持不变)
check_running() {
    local pid_file=$1
    local name=$2

    if [ -f "$pid_file" ]; then
        local pid=$(cat "$pid_file")
        if ps -p "$pid" > /dev/null 2>&1; then
            echo "$name 已在运行，PID: $pid"
            return 0
        else
            echo "$name PID文件存在但进程已不存在，正在清理..."
            rm -f "$pid_file"
        fi
    fi
    return 1
}

# --- 修改: 启动服务函数 ---
start_service() {
    echo "正在启动服务..."

    # **第一步: 设置虚拟环境并安装/更新依赖**
    setup_venv_and_deps

    # 检查API同步服务是否已在运行
    if ! check_running "$SYNC_PID" "API同步服务"; then
        echo "启动API同步服务..."
        # 使用虚拟环境的 Python 启动
        nohup "$PYTHON_BIN" "$SYNC_PY" > "$SYNC_LOG" 2>&1 &
        echo $! > "$SYNC_PID"
        echo "API同步服务已启动，PID: $(cat "$SYNC_PID")"
    fi

    # 检查数据文件是否存在 (逻辑保持不变)
    DATA_FILE="${SCRIPT_DIR}/data/all-data.json"
    MAX_WAIT=60  # 最多等待60秒
    WAIT_TIME=0

    if [ ! -f "$DATA_FILE" ]; then
        echo "等待API同步服务初始化数据..."
        while [ ! -f "$DATA_FILE" ] && [ $WAIT_TIME -lt $MAX_WAIT ]; do
            sleep 5
            WAIT_TIME=$((WAIT_TIME + 5))
            echo "已等待 $WAIT_TIME 秒..."
        done

        if [ -f "$DATA_FILE" ]; then
            echo "数据文件已创建，准备启动Web应用"
        else
            echo "警告: 等待超时，数据文件尚未创建，仍将继续启动Web应用"
        fi
    else
        echo "数据文件已存在，可以启动Web应用"
    fi

    # 检查Web应用是否已在运行
    if ! check_running "$APP_PID" "Web应用"; then
        echo "启动Web应用..."
         # 使用虚拟环境的 Python 启动
        nohup "$PYTHON_BIN" "$APP_PY" > "$APP_LOG" 2>&1 &
        echo $! > "$APP_PID"
        echo "Web应用已启动，PID: $(cat "$APP_PID")"
    fi

    echo "所有服务已在后台启动"
    echo "访问 http://localhost:80 查看网站 (如果 app.py 监听的是 80 端口)"
    echo "查看日志: $LOG_DIR"
}

# 停止服务函数 (保持不变)
stop_service() {
    echo "正在停止服务..."

    # 停止Web应用
    if [ -f "$APP_PID" ]; then
        local pid=$(cat "$APP_PID")
        if ps -p "$pid" > /dev/null 2>&1; then
            echo "停止Web应用，PID: $pid"
            kill "$pid"
            sleep 2
            if ps -p "$pid" > /dev/null 2>&1; then
                echo "强制终止Web应用，PID: $pid"
                kill -9 "$pid"
            fi
        else
            echo "Web应用不在运行状态"
        fi
        rm -f "$APP_PID"
    else
        echo "没有找到Web应用的PID文件"
    fi

    # 停止API同步服务
    if [ -f "$SYNC_PID" ]; then
        local pid=$(cat "$SYNC_PID")
        if ps -p "$pid" > /dev/null 2>&1; then
            echo "停止API同步服务，PID: $pid"
            kill "$pid"
            sleep 2
            if ps -p "$pid" > /dev/null 2>&1; then
                echo "强制终止API同步服务，PID: $pid"
                kill -9 "$pid"
            fi
        else
            echo "API同步服务不在运行状态"
        fi
        rm -f "$SYNC_PID"
    else
        echo "没有找到API同步服务的PID文件"
    fi

    echo "所有服务已停止"
}

# --- 修改: 检查虚拟环境状态函数 ---
check_venv_status() {
    if [ -d "$VENV_DIR" ] && [ -f "$PYTHON_BIN" ]; then
        echo "虚拟环境: 已找到 ($VENV_DIR)"
    else
        echo "虚拟环境: 未找到!"
    fi
}

# 查看服务状态函数
status_service() {
    echo "服务状态："

    # 检查虚拟环境状态
    check_venv_status

    # 检查服务运行状态 (逻辑保持不变)
    if [ -f "$SYNC_PID" ]; then
        local pid=$(cat "$SYNC_PID")
        if ps -p "$pid" > /dev/null 2>&1; then
            echo "API同步服务运行中，PID: $pid"
        else
            echo "API同步服务已停止（PID文件存在但进程不存在）"
        fi
    else
        echo "API同步服务未运行"
    fi

    if [ -f "$APP_PID" ]; then
        local pid=$(cat "$APP_PID")
        if ps -p "$pid" > /dev/null 2>&1; then
            echo "Web应用运行中，PID: $pid"
        else
            echo "Web应用已停止（PID文件存在但进程不存在）"
        fi
    else
        echo "Web应用未运行"
    fi

    # 检查数据文件状态 (逻辑保持不变)
    DATA_FILE="${SCRIPT_DIR}/data/all-data.json"
    if [ -f "$DATA_FILE" ]; then
        echo "数据文件存在: $DATA_FILE"
        echo "文件大小: $(du -h "$DATA_FILE" | cut -f1)"
        echo "最后修改时间: $(stat -c %y "$DATA_FILE")" # 注意: stat -c %y 是 GNU stat 的语法，macOS/BSD 使用 stat -f %Sm -t %Y-%m-%d_%H:%M:%S
    else
        echo "警告: 数据文件不存在"
    fi
}

# --- 修改: 创建虚拟环境函数 ---
create_venv() {
    echo "创建Python虚拟环境..."

    if [ -d "$VENV_DIR" ]; then
        echo "虚拟环境目录 '$VENV_DIR' 已存在。你想删除并重新创建吗？(y/n)"
        read -r answer
        if [ "$answer" != "${answer#[Yy]}" ]; then
            echo "删除旧虚拟环境..."
            rm -rf "$VENV_DIR"
        else
            echo "保留现有虚拟环境。如果你想强制重新安装依赖，请使用 'start' 命令。"
            return 0 # 返回成功，因为环境存在
        fi
    fi

    # 创建虚拟环境
    echo "正在创建新的虚拟环境于 $VENV_DIR ..."
    if ! python3 -m venv "$VENV_DIR"; then
         echo "错误: 创建虚拟环境失败。"
         return 1
    fi

    if [ ! -f "$PYTHON_BIN" ]; then
        echo "错误: 虚拟环境已创建但 Python 可执行文件 (${PYTHON_BIN}) 未找到。"
        return 1
    fi

    # 检查并安装依赖
    if [ -f "$REQUIREMENTS_FILE" ]; then
        echo "正在从 requirements.txt 安装依赖..."
        # 先升级 pip
        "$PYTHON_BIN" -m pip install --upgrade pip
        if ! "$PYTHON_BIN" -m pip install -r "$REQUIREMENTS_FILE"; then
             echo "错误: 依赖安装失败。请检查 ${REQUIREMENTS_FILE} 或手动安装。"
             echo "手动运行: source \"${VENV_DIR}/bin/activate\" && pip install -r \"${REQUIREMENTS_FILE}\""
             return 1
        fi
    else
        echo "警告: ${REQUIREMENTS_FILE} 未找到。未安装任何依赖。"
        echo "请创建 requirements.txt 并重新运行 create-venv 或 start，或手动安装依赖:"
        echo "source \"${VENV_DIR}/bin/activate\" && pip install <your-packages>"
    fi

    echo "虚拟环境创建完成并已安装依赖 (如果 requirements.txt 存在)。"
    echo "路径: $VENV_DIR"
}

# 显示日志函数 (保持不变)
show_logs() {
    local log_file=$1
    if [ -f "$log_file" ]; then
        echo "显示日志 (按 Ctrl+C 停止): $log_file"
        tail -f "$log_file"
    else
        echo "日志文件不存在: $log_file"
    fi
}

# 根据命令行参数执行相应操作 (保持不变)
case "$1" in
    start)
        start_service
        ;;
    stop)
        stop_service
        ;;
    restart)
        stop_service
        sleep 2
        start_service
        ;;
    status)
        status_service
        ;;
    create-venv)
        create_venv
        ;;
    log-app)
        show_logs "$APP_LOG"
        ;;
    log-sync)
        show_logs "$SYNC_LOG"
        ;;
    *)
        echo "用法: $0 {start|stop|restart|status|create-venv|log-app|log-sync}"
        exit 1
        ;;
esac

exit 0