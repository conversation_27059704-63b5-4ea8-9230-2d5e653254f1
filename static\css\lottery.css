/* 抽奖页面样式 */
.lottery-container {
    margin-top: 30px;
    margin-bottom: 40px;
}

.lottery-header {
    text-align: center;
    margin-bottom: 40px;
}

.lottery-header h1 {
    font-size: 2.2rem;
    font-weight: 700;
    margin-bottom: 16px;
    color: var(--text-primary);
}

.lottery-header p {
    font-size: 1.1rem;
    color: var(--text-secondary);
    max-width: 700px;
    margin: 0 auto;
}

/* 奖品展示区 */
.prize-showcase {
    background-color: var(--card-bg);
    border-radius: var(--border-radius);
    padding: 30px;
    margin-bottom: 40px;
    border: 1px solid #333;
    box-shadow: var(--box-shadow);
}

.section-header {
    margin-bottom: 25px;
    text-align: center;
}

.section-header h2 {
    font-size: 1.8rem;
    font-weight: 600;
    margin-bottom: 10px;
    color: var(--text-primary);
}

.section-header p {
    font-size: 1rem;
    color: var(--text-secondary);
}

.prize-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 20px;
}

.prize-card {
    background-color: #2a2a2a;
    border-radius: 8px;
    overflow: hidden;
    transition: transform 0.3s, box-shadow 0.3s;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.prize-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.2);
}

.prize-image {
    height: 160px;
    overflow: hidden;
}

.prize-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s;
}

.prize-card:hover .prize-image img {
    transform: scale(1.05);
}

.prize-info {
    padding: 15px;
}

.prize-info h3 {
    font-size: 1.1rem;
    margin-bottom: 10px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.prize-probability {
    font-size: 0.9rem;
    color: #ffc107;
}

.no-prizes {
    grid-column: 1 / -1;
    text-align: center;
    padding: 40px;
    color: var(--text-secondary);
}

/* 抽奖区域 */
.lottery-section {
    background-color: var(--card-bg);
    border-radius: var(--border-radius);
    padding: 30px;
    margin-bottom: 40px;
    border: 1px solid #333;
    box-shadow: var(--box-shadow);
}

.lottery-options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

.lottery-option {
    background-color: #2a2a2a;
    border-radius: 8px;
    padding: 20px;
    text-align: center;
    transition: transform 0.3s, box-shadow 0.3s;
}

.lottery-option:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.2);
}

.option-header {
    margin-bottom: 20px;
}

.option-header h3 {
    font-size: 1.4rem;
    margin-bottom: 10px;
}

.option-cost {
    font-size: 1rem;
    color: var(--text-secondary);
}

.discount {
    display: block;
    color: #4caf50;
    margin-top: 5px;
    font-size: 0.9rem;
}

.lottery-btn {
    background: linear-gradient(135deg, #ff9800, #f44336);
    color: white;
    border: none;
    border-radius: 30px;
    padding: 12px 25px;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.3s;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.lottery-btn:hover {
    background: linear-gradient(135deg, #ff5722, #e91e63);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.2);
}

/* 抽奖历史 */
.lottery-history {
    background-color: var(--card-bg);
    border-radius: var(--border-radius);
    padding: 30px;
    border: 1px solid #333;
    box-shadow: var(--box-shadow);
}

.history-table-container {
    overflow-x: auto;
}

.history-table {
    width: 100%;
    border-collapse: collapse;
}

.history-table th,
.history-table td {
    padding: 12px 15px;
    text-align: left;
    border-bottom: 1px solid #333;
}

.history-table th {
    background-color: #222;
    color: var(--text-primary);
    font-weight: 600;
}

.history-table tr:hover {
    background-color: #2a2a2a;
}

.no-records {
    text-align: center;
    padding: 20px;
    color: var(--text-secondary);
}

/* 抽奖结果模态框 */
.lottery-result-modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
    z-index: 1000;
    justify-content: center;
    align-items: center;
}

.modal-content {
    background-color: #2a2a2a;
    border-radius: 10px;
    width: 90%;
    max-width: 600px;
    max-height: 90vh;
    overflow: hidden;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.5);
    animation: modalFadeIn 0.3s;
}

@keyframes modalFadeIn {
    from {
        opacity: 0;
        transform: translateY(-50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.modal-header {
    background-color: #333;
    padding: 15px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #444;
}

.modal-header h2 {
    margin: 0;
    color: white;
    font-size: 1.5rem;
}

.modal-close {
    color: #aaa;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    transition: color 0.2s;
}

.modal-close:hover {
    color: white;
}

.modal-body {
    padding: 30px;
    overflow-y: auto;
    max-height: calc(80vh - 140px);
}

.result-animation {
    text-align: center;
    margin-bottom: 30px;
}

.single-prize {
    display: inline-block;
    animation: prizeReveal 1s;
}

@keyframes prizeReveal {
    0% {
        transform: scale(0.5);
        opacity: 0;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

.single-prize .prize-image {
    width: 150px;
    height: 150px;
    margin: 0 auto 15px;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

.single-prize .prize-name {
    font-size: 1.2rem;
    color: white;
    margin-top: 10px;
}

.batch-summary {
    text-align: center;
}

.summary-items {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 10px;
    margin-bottom: 20px;
}

.summary-item {
    width: 60px;
    height: 60px;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.2);
}

.summary-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.summary-group {
    display: flex;
    align-items: center;
    background-color: #333;
    border-radius: 8px;
    padding: 10px;
    margin-bottom: 10px;
    width: 100%;
    max-width: 300px;
    margin-left: auto;
    margin-right: auto;
}

.group-image {
    width: 50px;
    height: 50px;
    border-radius: 6px;
    overflow: hidden;
    margin-right: 15px;
}

.group-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.group-info {
    flex: 1;
    text-align: left;
}

.group-name {
    font-size: 1rem;
    margin-bottom: 5px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.group-count {
    font-size: 0.9rem;
    color: #ffc107;
}

.result-detail {
    text-align: center;
    padding: 20px;
    background-color: #333;
    border-radius: 8px;
}

.result-detail p {
    margin-bottom: 10px;
}

.result-detail strong {
    color: #ffc107;
}

.modal-footer {
    padding: 15px 20px;
    text-align: center;
    border-top: 1px solid #444;
}

.draw-again-btn {
    background-color: #4caf50;
    color: white;
    border: none;
    border-radius: 30px;
    padding: 10px 20px;
    margin-right: 10px;
    cursor: pointer;
    transition: background-color 0.3s;
}

.draw-again-btn:hover {
    background-color: #45a049;
}

.close-btn {
    background-color: #555;
    color: white;
    border: none;
    border-radius: 30px;
    padding: 10px 20px;
    cursor: pointer;
    transition: background-color 0.3s;
}

.close-btn:hover {
    background-color: #666;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .prize-grid {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    }
    
    .lottery-options {
        grid-template-columns: 1fr;
    }
    
    .single-prize .prize-image {
        width: 120px;
        height: 120px;
    }
}
