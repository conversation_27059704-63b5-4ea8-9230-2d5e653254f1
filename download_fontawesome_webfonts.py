import os
import requests
import time
from concurrent.futures import ThreadPoolExecutor

# Font Awesome webfonts列表 - 只保留确认存在的文件
FA_WEBFONTS = [
    # 已成功下载的文件
    {
        'url': 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.2.0/webfonts/fa-solid-900.woff2',
        'local_path': 'static/vendor/fontawesome/webfonts/fa-solid-900.woff2'
    },
    {
        'url': 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.2.0/webfonts/fa-solid-900.ttf',
        'local_path': 'static/vendor/fontawesome/webfonts/fa-solid-900.ttf'
    },
    {
        'url': 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.2.0/webfonts/fa-regular-400.woff2',
        'local_path': 'static/vendor/fontawesome/webfonts/fa-regular-400.woff2'
    },
    {
        'url': 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.2.0/webfonts/fa-regular-400.ttf',
        'local_path': 'static/vendor/fontawesome/webfonts/fa-regular-400.ttf'
    },
    {
        'url': 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.2.0/webfonts/fa-brands-400.woff2',
        'local_path': 'static/vendor/fontawesome/webfonts/fa-brands-400.woff2'
    },
    {
        'url': 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.2.0/webfonts/fa-brands-400.ttf',
        'local_path': 'static/vendor/fontawesome/webfonts/fa-brands-400.ttf'
    }
]

def download_file(font_info):
    """下载单个字体文件"""
    url = font_info['url']
    local_path = font_info['local_path']
    
    try:
        # 确保目录存在
        os.makedirs(os.path.dirname(local_path), exist_ok=True)
        
        # 检查文件是否已存在
        if os.path.exists(local_path):
            print(f"文件已存在: {local_path}")
            return True
        
        # 下载文件
        print(f"正在下载: {url} -> {local_path}")
        start_time = time.time()
        
        response = requests.get(url, stream=True)
        if response.status_code == 404:
            print(f"文件不存在: {url}")
            return False
            
        response.raise_for_status()  # 检查响应状态
        
        with open(local_path, 'wb') as f:
            for chunk in response.iter_content(chunk_size=8192):
                if chunk:
                    f.write(chunk)
        
        elapsed_time = time.time() - start_time
        file_size = os.path.getsize(local_path) / 1024  # KB
        print(f"下载完成: {local_path} ({file_size:.2f} KB, {elapsed_time:.2f}秒)")
        return True
    except Exception as e:
        print(f"下载失败 {url}: {e}")
        return False

def main():
    print("开始下载Font Awesome字体文件...")
    
    # 创建字体目录
    os.makedirs('static/vendor/fontawesome/webfonts', exist_ok=True)
    
    # 使用多线程并行下载
    with ThreadPoolExecutor(max_workers=5) as executor:
        results = list(executor.map(download_file, FA_WEBFONTS))
    
    # 统计结果
    success_count = sum(1 for r in results if r)
    fail_count = sum(1 for r in results if not r)
    
    print(f"\n下载完成: 成功 {success_count} 个, 失败 {fail_count} 个")
    print("现在请确保CSS文件中的字体引用路径正确指向这些文件")

if __name__ == "__main__":
    main() 