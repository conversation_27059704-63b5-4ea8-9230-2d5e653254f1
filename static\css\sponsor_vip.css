/* 赞助页面VIP部分样式 */
.sponsor-vip-section {
    margin-bottom: 50px;
}

.vip-intro {
    background-color: var(--card-bg);
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--box-shadow);
}

.vip-intro-content {
    padding: 30px;
}

.vip-intro-content h3 {
    font-size: 1.6rem;
    margin-bottom: 15px;
    color: var(--text-primary);
    display: flex;
    align-items: center;
}

.vip-intro-content h3 i {
    margin-right: 10px;
    color: #ffd700;
    text-shadow: 0 0 10px rgba(255, 215, 0, 0.5);
}

.vip-intro-content p {
    font-size: 1.1rem;
    color: var(--text-secondary);
    margin-bottom: 25px;
    line-height: 1.6;
}

.vip-benefits-list {
    margin-bottom: 30px;
}

.vip-benefit-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 20px;
    background-color: rgba(0, 0, 0, 0.05);
    padding: 15px;
    border-radius: 8px;
    transition: var(--transition);
}

.vip-benefit-item:hover {
    background-color: rgba(0, 0, 0, 0.1);
    transform: translateX(5px);
}

.vip-benefit-item i {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, var(--primary-color), #0077cc);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    color: white;
    font-size: 1.2rem;
    flex-shrink: 0;
}

.vip-benefit-text h4 {
    font-size: 1.2rem;
    margin-bottom: 5px;
    color: var(--text-primary);
}

.vip-benefit-text p {
    font-size: 0.95rem;
    color: var(--text-secondary);
    margin-bottom: 0;
    line-height: 1.5;
}

.vip-levels-summary {
    background-color: rgba(0, 0, 0, 0.05);
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 25px;
}

.vip-levels-summary p {
    font-size: 1.1rem;
    margin-bottom: 15px;
}

.vip-levels-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 10px;
}

.vip-level-item {
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.1), rgba(0, 0, 0, 0.2));
    padding: 10px 15px;
    border-radius: 6px;
    font-size: 0.95rem;
    color: var(--text-primary);
    text-align: center;
    transition: var(--transition);
}

.vip-level-item:hover {
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.2), rgba(0, 0, 0, 0.3));
    transform: translateY(-3px);
}

.vip-details-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 12px 25px;
    background: linear-gradient(135deg, #ff9800, #ff5722);
    color: white;
    border-radius: 30px;
    font-size: 1.1rem;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(255, 87, 34, 0.3);
}

.vip-details-btn i {
    margin-right: 10px;
}

.vip-details-btn:hover {
    background: linear-gradient(135deg, #ff5722, #ff9800);
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(255, 87, 34, 0.4);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .vip-levels-grid {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    }
}
