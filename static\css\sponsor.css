/* 赞助页面样式 */
.sponsor-container {
    margin-top: 30px;
    margin-bottom: 40px;
}

.sponsor-header {
    text-align: center;
    margin-bottom: 40px;
    background-color: var(--card-bg);
    border-radius: var(--border-radius);
    padding: 40px;
    border: 1px solid #333;
    box-shadow: var(--box-shadow);
}

.sponsor-header h1 {
    font-size: 2.2rem;
    font-weight: 700;
    margin-bottom: 16px;
    color: var(--text-primary);
}

.sponsor-header p {
    font-size: 1.1rem;
    color: var(--text-secondary);
    max-width: 700px;
    margin: 0 auto;
}

.section-heading {
    text-align: center;
    font-size: 1.8rem;
    font-weight: 600;
    margin-bottom: 30px;
    color: var(--text-primary);
    position: relative;
    padding-bottom: 15px;
}

.section-heading:after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 3px;
    background-color: var(--primary-color);
    border-radius: 3px;
}

.sponsor-section {
    margin-bottom: 50px;
}

.why-sponsor-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 25px;
    margin-top: 30px;
}

.why-sponsor-card {
    background-color: var(--card-bg);
    border-radius: var(--border-radius);
    padding: 30px;
    text-align: center;
    border: 1px solid #333;
    transition: var(--transition);
}

.why-sponsor-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 12px 30px rgba(0, 0, 0, 0.4);
    border-color: var(--primary-color);
}

.sponsor-icon {
    width: 70px;
    height: 70px;
    background-color: rgba(0, 168, 255, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    border: 2px solid rgba(0, 168, 255, 0.3);
}

.sponsor-icon i {
    font-size: 30px;
    color: var(--primary-color);
}

.why-sponsor-card h3 {
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 15px;
    color: var(--text-primary);
}

.why-sponsor-card p {
    color: var(--text-secondary);
    font-size: 1rem;
    line-height: 1.6;
}

.sponsor-options-section {
    margin-bottom: 50px;
}

.sponsor-options {
    display: flex;
    justify-content: center;
    gap: 25px;
    margin-top: 30px;
}

.sponsor-option-card {
    background-color: var(--card-bg);
    border-radius: var(--border-radius);
    border: 1px solid #333;
    width: 300px;
    padding-bottom: 24px;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.sponsor-option-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 12px 30px rgba(0, 0, 0, 0.4);
    border-color: var(--primary-color);
}

.sponsor-option-card.featured {
    border-color: var(--primary-color);
    transform: scale(1.05);
    box-shadow: 0 12px 30px rgba(0, 0, 0, 0.4);
    z-index: 1;
}

.sponsor-option-card.featured:hover {
    transform: scale(1.05) translateY(-8px);
}

.sponsor-tag {
    position: absolute;
    top: 15px;
    right: -30px;
    background-color: var(--primary-color);
    color: #000;
    font-size: 0.8rem;
    font-weight: 600;
    padding: 5px 30px;
    transform: rotate(45deg);
}

.sponsor-option-header {
    padding: 24px;
    border-bottom: 1px solid #333;
    text-align: center;
}

.sponsor-option-header h3 {
    font-size: 1.4rem;
    font-weight: 600;
    margin-bottom: 12px;
    color: var(--text-primary);
}

.sponsor-price {
    font-size: 2rem;
    font-weight: 700;
    color: var(--primary-color);
}

.sponsor-option-content {
    padding: 24px;
}

.sponsor-features {
    list-style: none;
    padding: 0;
    margin: 0;
}

.sponsor-features li {
    margin-bottom: 12px;
    color: var(--text-secondary);
    font-size: 0.95rem;
    display: flex;
    align-items: center;
}

.sponsor-features li i {
    color: var(--primary-color);
    margin-right: 10px;
    font-size: 1rem;
}

.sponsor-btn {
    display: block;
    width: 80%;
    margin: 0 auto;
    padding: 12px;
    background-color: var(--primary-color);
    color: #000;
    border-radius: 30px;
    font-weight: 600;
    font-size: 1rem;
    text-align: center;
    transition: var(--transition);
    text-decoration: none;
}

.sponsor-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 15px rgba(0, 168, 255, 0.3);
    opacity: 1;
    text-decoration: none;
    color: #000;
}

.sponsor-qrcode-section {
    text-align: center;
    margin-top: 40px;
    margin-bottom: 40px;
}

.sponsor-qrcode {
    background-color: var(--card-bg);
    border-radius: var(--border-radius);
    padding: 40px;
    border: 1px solid #333;
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.25);
    display: inline-block;
    margin: 0 auto;
}

.sponsor-qrcode h3 {
    font-size: 1.6rem;
    font-weight: 600;
    margin-bottom: 30px;
    color: var(--text-primary);
}

.qrcode-images {
    display: flex;
    justify-content: center;
    gap: 40px;
    margin-bottom: 30px;
}

.qrcode {
    transition: transform 0.3s ease;
}

.qrcode:hover {
    transform: scale(1.05);
}

.qrcode img {
    width: 180px;
    height: 180px;
    border-radius: 12px;
    border: 2px solid #444;
    margin-bottom: 15px;
    background-color: #fff;
    padding: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.qrcode p {
    color: var(--text-secondary);
    font-size: 1.1rem;
    font-weight: 500;
}

.sponsor-note {
    color: var(--text-secondary);
    font-size: 1rem;
    margin-top: 20px;
    background-color: rgba(255, 255, 255, 0.1);
    padding: 12px 20px;
    border-radius: 8px;
    display: inline-block;
}

@media (max-width: 992px) {
    .sponsor-options {
        flex-direction: column;
        align-items: center;
    }

    .sponsor-option-card {
        width: 100%;
        max-width: 350px;
    }

    .sponsor-option-card.featured {
        transform: none;
    }

    .sponsor-option-card.featured:hover {
        transform: translateY(-8px);
    }

    .qrcode-images {
        flex-direction: column;
        gap: 30px;
        align-items: center;
    }
}

@media (max-width: 768px) {
    .why-sponsor-grid {
        grid-template-columns: 1fr;
    }

    .sponsor-header {
        padding: 30px 20px;
    }

    .sponsor-header h1 {
        font-size: 1.8rem;
    }

    .section-heading {
        font-size: 1.6rem;
    }
}

@media (max-width: 576px) {
    .sponsor-qrcode {
        padding: 30px 15px;
    }

    .qrcode img {
        width: 180px;
        height: 180px;
    }
}
