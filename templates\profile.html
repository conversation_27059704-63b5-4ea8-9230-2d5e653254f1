{% extends "base.html" %}
{% block title %}个人信息 - SCUM物品代码网站{% endblock %}
{% block content %}
<div class="container mt-5">
    <div class="row justify-content-center">
        <div class="col-md-6 text-center mb-4">
            <h1>个人中心</h1>
            <p class="text-muted">您的游戏数据和个人信息</p>
        </div>
    </div>

    <div class="row justify-content-center">
        <div class="col-md-6">
            <div class="user-info-card">
                <div class="user-info-card-content">
                    <div class="user-info-card-username">
                        {{ user.username if user else '用户名' }}
                    </div>
                    <div class="text-muted small mb-3">加入于 2024年5月</div>

                    <div class="user-info-card-stats">
                        <div class="user-info-card-stat">
                            <div class="user-info-card-stat-label">积分</div>
                            <div class="user-info-card-stat-value">{{ user.points if user else '0' }}</div>
                        </div>
                        <div class="user-info-card-stat">
                            <div class="user-info-card-stat-label">击杀数</div>
                            <div class="user-info-card-stat-value">{{ user.kills if user else '0' }}</div>
                        </div>
                        <div class="user-info-card-stat">
                            <div class="user-info-card-stat-label">死亡数</div>
                            <div class="user-info-card-stat-value">{{ user.deaths if user else '0' }}</div>
                        </div>
                        <div class="user-info-card-stat">
                            <div class="user-info-card-stat-label">KD比例</div>
                            <div class="user-info-card-stat-value">{{ user.kd|round(2) if user and user.kd else '0' }}</div>
                        </div>
                    </div>

                    {% if user and user.is_vip %}
                    <div class="user-info-card-vip">
                        <i class="fas fa-crown"></i> VIP用户
                    </div>
                    {% endif %}

                    {% if user and user.xyz %}
                    <div class="user-info-card-coordinate">
                        <div class="small mb-1">回家坐标</div>
                        <div>{{ user.xyz }}</div>
                    </div>
                    {% else %}
                    <div class="user-info-card-coordinate">
                        <div class="small mb-1">回家坐标</div>
                        <div>未设置</div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- 每日签到部分 -->
    <div class="row justify-content-center mt-4">
        <div class="col-md-6">
            <div class="card bg-dark text-white">
                <div class="card-header">
                    <i class="fas fa-calendar-check"></i> 每日签到
                </div>
                <div class="card-body">
                    <div class="checkin-card">
                        <div class="checkin-info">
                            <div class="checkin-status">
                                {% if is_signed_in_today %}
                                <div class="status-signed">
                                    <i class="fas fa-check-circle"></i>
                                    <span>今日已签到</span>
                                </div>
                                <div class="next-checkin">
                                    <p>下次签到时间: {{ (now + timedelta(days=1)).strftime('%Y-%m-%d') }} 00:00</p>
                                </div>
                                {% else %}
                                <div class="status-unsigned">
                                    <i class="fas fa-calendar-day"></i>
                                    <span>今日未签到</span>
                                </div>
                                <div class="checkin-reward">
                                    <p>签到可获得 <span class="reward-points">1000</span> 积分</p>
                                </div>
                                {% endif %}
                            </div>
                            <div class="checkin-streak">
                                <p>当前连续签到: <span class="streak-days">{{ user.checkin_streak if user and user.checkin_streak else 0 }}</span> 天</p>
                                <p>累计签到: <span class="total-days">{{ user.checkin_days if user and user.checkin_days else 0 }}</span> 天</p>
                            </div>
                        </div>
                        <div class="checkin-action">
                            {% if is_signed_in_today %}
                            <button class="checkin-btn signed" disabled>
                                <i class="fas fa-check"></i> 已签到
                            </button>
                            {% else %}
                            <button id="sign-in-btn" class="checkin-btn">
                                <i class="fas fa-calendar-check"></i> 立即签到
                            </button>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 收藏物品部分 -->
    <div class="row justify-content-center mt-4">
        <div class="col-md-6">
            <div class="card bg-dark text-white">
                <div class="card-header">
                    <i class="fas fa-heart"></i> 收藏物品
                </div>
                <div class="card-body">
                    {% if user and user.favorites %}
                    <div class="row">
                        {% for item in user.favorites %}
                        <div class="col-md-6 mb-3">
                            <div class="p-2 bg-secondary rounded">
                                <div class="small text-white">{{ item.name }}</div>
                                <div class="text-light small">{{ item.code }}</div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                    {% else %}
                    <p class="text-center text-muted my-4">
                        <i class="fas fa-info-circle"></i> 您还没有收藏任何物品
                    </p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // 签到按钮点击事件
        const signInBtn = document.getElementById('sign-in-btn');
        if (signInBtn) {
            signInBtn.addEventListener('click', function() {
                // 禁用按钮，防止重复点击
                signInBtn.disabled = true;
                signInBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 签到中...';

                // 发送签到请求
                fetch('/sign_in', {
                    method: 'POST',
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // 签到成功，更新页面
                        const checkinStatus = document.querySelector('.checkin-status');
                        const checkinAction = document.querySelector('.checkin-action');
                        const streakDays = document.querySelector('.streak-days');
                        const totalDays = document.querySelector('.total-days');
                        const pointsDisplay = document.querySelector('.user-info-card-stat-value');

                        // 更新签到状态
                        checkinStatus.innerHTML = `
                            <div class="status-signed">
                                <i class="fas fa-check-circle"></i>
                                <span>今日已签到</span>
                            </div>
                            <div class="next-checkin">
                                <p>下次签到时间: ${new Date(Date.now() + 86400000).toISOString().split('T')[0]} 00:00</p>
                            </div>
                        `;

                        // 更新签到按钮
                        checkinAction.innerHTML = `
                            <button class="checkin-btn signed" disabled>
                                <i class="fas fa-check"></i> 已签到
                            </button>
                        `;

                        // 更新连续签到天数和总签到天数
                        streakDays.textContent = data.checkin_streak;

                        // 更新积分显示
                        if (pointsDisplay) {
                            pointsDisplay.textContent = data.points;
                        }

                        // 显示成功消息
                        alert(data.message);
                    } else {
                        // 签到失败，恢复按钮状态
                        signInBtn.disabled = false;
                        signInBtn.innerHTML = '<i class="fas fa-calendar-check"></i> 立即签到';

                        // 显示错误消息
                        alert(data.message || '签到失败，请稍后再试');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    // 恢复按钮状态
                    signInBtn.disabled = false;
                    signInBtn.innerHTML = '<i class="fas fa-calendar-check"></i> 立即签到';

                    // 显示错误消息
                    alert('签到失败，请稍后再试');
                });
            });
        }
    });
</script>
{% endblock %}