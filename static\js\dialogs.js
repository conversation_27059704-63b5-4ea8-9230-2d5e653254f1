/**
 * 自定义对话框函数
 * 提供全局可用的自定义确认和提示对话框
 */

// 自定义确认对话框函数
function customConfirm(message, callback) {
    const dialog = document.getElementById('custom-confirm');
    const messageEl = document.getElementById('dialog-message');
    const confirmBtn = document.getElementById('dialog-confirm');
    const cancelBtn = document.getElementById('dialog-cancel');
    const closeBtn = dialog.querySelector('.dialog-close');
    
    // 设置消息
    messageEl.innerHTML = message;
    
    // 显示对话框
    dialog.style.display = 'flex';
    
    // 确认按钮事件
    const confirmHandler = function() {
        dialog.style.display = 'none';
        confirmBtn.removeEventListener('click', confirmHandler);
        cancelBtn.removeEventListener('click', cancelHandler);
        closeBtn.removeEventListener('click', cancelHandler);
        callback(true);
    };
    
    // 取消按钮事件
    const cancelHandler = function() {
        dialog.style.display = 'none';
        confirmBtn.removeEventListener('click', confirmHandler);
        cancelBtn.removeEventListener('click', cancelHandler);
        closeBtn.removeEventListener('click', cancelHandler);
        callback(false);
    };
    
    // 添加事件监听
    confirmBtn.addEventListener('click', confirmHandler);
    cancelBtn.addEventListener('click', cancelHandler);
    closeBtn.addEventListener('click', cancelHandler);
}

// 自定义提示对话框函数
function customAlert(message, callback) {
    const dialog = document.getElementById('custom-alert');
    const messageEl = document.getElementById('alert-message');
    const confirmBtn = document.getElementById('alert-confirm');
    const closeBtn = dialog.querySelector('.dialog-close');
    
    // 设置消息
    messageEl.innerHTML = message;
    
    // 显示对话框
    dialog.style.display = 'flex';
    
    // 确认按钮事件
    const confirmHandler = function() {
        dialog.style.display = 'none';
        confirmBtn.removeEventListener('click', confirmHandler);
        closeBtn.removeEventListener('click', confirmHandler);
        if (callback) callback();
    };
    
    // 添加事件监听
    confirmBtn.addEventListener('click', confirmHandler);
    closeBtn.addEventListener('click', confirmHandler);
}
