{% extends "base.html" %}

{% block title %}物品回收 - 7788商城{% endblock %}

{% block head_extra %}
<style>
    .recycle-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 20px;
    }

    .recycle-header {
        margin-bottom: 30px;
        text-align: center;
    }

    .recycle-header h1 {
        font-size: 2.5rem;
        margin-bottom: 10px;
        color: #4CAF50;
    }

    .recycle-header p {
        font-size: 1.1rem;
        color: #aaa;
        max-width: 800px;
        margin: 0 auto 20px;
    }

    .recycle-stats {
        display: flex;
        justify-content: center;
        gap: 30px;
        margin-top: 20px;
        margin-bottom: 20px;
    }

    .recycle-stat-item {
        background-color: #2a2a2a;
        border-radius: 8px;
        padding: 15px 25px;
        display: flex;
        align-items: center;
        border: 1px solid #3a3a3a;
    }

    .recycle-stat-item i {
        font-size: 1.5rem;
        color: #4CAF50;
        margin-right: 10px;
    }

    .recycle-stat-label {
        color: #aaa;
        margin-right: 8px;
    }

    .recycle-stat-value {
        font-size: 1.2rem;
        font-weight: bold;
        color: #fff;
    }

    .recycle-search {
        margin-bottom: 30px;
        display: flex;
        max-width: 600px;
        margin-left: auto;
        margin-right: auto;
    }

    .recycle-search input {
        flex: 1;
        padding: 12px 15px;
        border: 1px solid #333;
        background-color: #222;
        color: #fff;
        border-radius: 4px 0 0 4px;
        font-size: 16px;
    }

    .recycle-search button {
        padding: 12px 20px;
        background-color: #4CAF50;
        color: white;
        border: none;
        border-radius: 0 4px 4px 0;
        cursor: pointer;
        font-size: 16px;
    }

    .recycle-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
        gap: 20px;
    }

    .recycle-item {
        background-color: #2a2a2a;
        border-radius: 8px;
        padding: 20px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        transition: transform 0.2s, box-shadow 0.2s;
        position: relative;
        border: 1px solid #3a3a3a;
    }

    .recycle-item:hover {
        transform: translateY(-5px);
        box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
    }

    .recycle-item-name {
        font-size: 18px;
        font-weight: bold;
        margin-bottom: 15px;
        color: #fff;
        word-break: break-word;
        background-color: rgba(76, 175, 80, 0.1);
        padding: 8px 12px;
        border-radius: 4px;
        border-left: 3px solid #4CAF50;
    }

    .recycle-item-points {
        font-size: 16px;
        color: #4CAF50;
        margin-bottom: 10px;
        display: flex;
        align-items: center;
        background-color: rgba(76, 175, 80, 0.1);
        border-radius: 4px;
        padding: 8px 12px;
        margin-top: 10px;
    }

    .recycle-item-points i {
        margin-right: 8px;
        font-size: 18px;
    }

    .points-content {
        flex: 1;
    }

    .points-value {
        font-weight: bold;
        font-size: 16px;
    }

    .recycle-item-area {
        font-size: 14px;
        color: #aaa;
        margin-bottom: 15px;
        display: flex;
        align-items: flex-start;
        padding: 5px 0;
    }

    .recycle-item-area i {
        margin-right: 8px;
        margin-top: 3px;
        color: #4CAF50;
    }

    .area-content {
        flex: 1;
    }

    .area-label {
        font-weight: bold;
        margin-right: 5px;
    }

    .area-value {
        color: #ddd;
    }

    .area-coordinates {
        margin-top: 5px;
        background-color: rgba(0, 0, 0, 0.2);
        border-radius: 4px;
        padding: 8px;
        border: 1px solid #3a3a3a;
    }

    .coordinate-item {
        display: flex;
        margin-bottom: 5px;
    }

    .coordinate-item:last-child {
        margin-bottom: 0;
    }

    .coordinate-label {
        font-weight: bold;
        margin-right: 5px;
        min-width: 40px;
    }

    .coordinate-value {
        color: #ddd;
    }

    .recycle-item-stats {
        font-size: 14px;
        color: #ff9800;
        margin-bottom: 15px;
        display: flex;
        align-items: flex-start;
        padding: 5px 0;
        border-bottom: 1px dashed rgba(255, 255, 255, 0.1);
    }

    .recycle-item-stats i {
        margin-right: 8px;
        margin-top: 3px;
        color: #ff9800;
    }

    .stats-content {
        flex: 1;
    }

    .stats-label {
        font-weight: bold;
        margin-right: 5px;
    }

    .stats-value {
        color: #ddd;
    }

    .recycle-item-profession {
        font-size: 14px;
        color: #ff9800;
        margin-bottom: 15px;
        display: flex;
        align-items: flex-start;
        padding: 5px 0;
    }

    .recycle-item-profession i {
        margin-right: 8px;
        margin-top: 3px;
        color: #ff9800;
    }

    .profession-content {
        flex: 1;
    }

    .profession-label {
        font-weight: bold;
        margin-right: 5px;
    }

    .profession-value {
        color: #ddd;
    }

    .copy-btn {
        position: absolute;
        top: 15px;
        right: 15px;
        background-color: #3a3a3a;
        color: #fff;
        border: 1px solid #4CAF50;
        border-radius: 4px;
        width: 36px;
        height: 36px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.2s;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    }

    .copy-btn:hover {
        background-color: #4CAF50;
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
    }

    .no-items {
        grid-column: 1 / -1;
        text-align: center;
        padding: 50px 0;
        color: #aaa;
        font-size: 18px;
    }

    .area-info {
        margin-top: 10px;
        font-size: 13px;
        color: #888;
        line-height: 1.4;
    }
</style>
{% endblock %}

{% block content %}
<div class="recycle-container">
    <div class="recycle-header">
        <h1><i class="fas fa-recycle"></i> 物品回收</h1>
        <p>在指定区域回收物品可获得积分奖励。复制物品名称，在游戏中前往指定区域进行回收。</p>
        <div class="recycle-stats">
            <div class="recycle-stat-item">
                <i class="fas fa-recycle"></i>
                <span class="recycle-stat-label">总回收次数:</span>
                <span class="recycle-stat-value">{{ total_recycled_count }}</span>
            </div>
            <div class="recycle-stat-item">
                <i class="fas fa-box"></i>
                <span class="recycle-stat-label">可回收物品:</span>
                <span class="recycle-stat-value">{{ recycle_items|length }}</span>
            </div>
        </div>
    </div>

    <div class="recycle-search">
        <input type="text" id="searchInput" placeholder="搜索回收物品...">
        <button id="searchBtn"><i class="fas fa-search"></i></button>
    </div>

    <div class="recycle-grid" id="recycleGrid">
        {% if recycle_items %}
            {% for item in recycle_items %}
            <div class="recycle-item" data-name="@回收-{{ item.recycleName }}">
                <div class="recycle-item-name">@回收-{{ item.recycleName }}</div>
                <div class="recycle-item-points">
                    <i class="fas fa-coins"></i>
                    <div class="points-content">
                        <span class="points-value">{{ item.recyclePoints }} 积分</span>
                    </div>
                </div>
                <div class="recycle-item-stats">
                    <i class="fas fa-recycle"></i>
                    <div class="stats-content">
                        <span class="stats-label">已回收:</span>
                        <span class="stats-value">{{ item.total_recycled }} 次</span>
                    </div>
                </div>
                <div class="recycle-item-profession">
                    <i class="fas fa-user-tag"></i>
                    <div class="profession-content">
                        <span class="profession-label">职业:</span>
                        {% if item.profession and item.profession != "" %}
                            <span class="profession-value">{{ item.profession }}</span>
                        {% else %}
                            <span class="profession-value">无限制</span>
                        {% endif %}
                    </div>
                </div>
                <div class="recycle-item-area">
                    <i class="fas fa-map-marker-alt"></i>
                    <div class="area-content">
                        <span class="area-label">回收区域:</span>
                        {% if item.area and item.area.isAllArea %}
                            <span class="area-value">全地图</span>
                        {% elif item.area and item.area.topLeft and item.area.bottomRight %}
                            <div class="area-coordinates">
                                <div class="coordinate-item">
                                    <span class="coordinate-label">左上:</span>
                                    <span class="coordinate-value">X:{{ item.area.topLeft.x }}, Y:{{ item.area.topLeft.y }}</span>
                                </div>
                                <div class="coordinate-item">
                                    <span class="coordinate-label">右下:</span>
                                    <span class="coordinate-value">X:{{ item.area.bottomRight.x }}, Y:{{ item.area.bottomRight.y }}</span>
                                </div>
                            </div>
                        {% else %}
                            <span class="area-value">全地图</span>
                        {% endif %}
                    </div>
                </div>
                <button class="copy-btn" title="复制物品名称"><i class="fas fa-copy"></i></button>
            </div>
            {% endfor %}
        {% else %}
            <div class="no-items">
                <i class="fas fa-exclamation-circle"></i> 暂无回收物品数据
            </div>
        {% endif %}
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // 搜索功能
    const searchInput = document.getElementById('searchInput');
    const searchBtn = document.getElementById('searchBtn');
    const recycleItems = document.querySelectorAll('.recycle-item');

    function performSearch() {
        const searchTerm = searchInput.value.toLowerCase();
        let hasResults = false;

        recycleItems.forEach(item => {
            const itemName = item.getAttribute('data-name').toLowerCase();
            const itemText = item.textContent.toLowerCase();

            if (itemName.includes(searchTerm) || itemText.includes(searchTerm)) {
                item.style.display = '';
                hasResults = true;
            } else {
                item.style.display = 'none';
            }
        });

        // 显示无结果提示
        let noResultsMsg = document.querySelector('.no-search-results');
        if (!hasResults) {
            if (!noResultsMsg) {
                noResultsMsg = document.createElement('div');
                noResultsMsg.className = 'no-items no-search-results';
                noResultsMsg.innerHTML = '<i class="fas fa-search"></i> 没有找到匹配的回收物品';
                document.getElementById('recycleGrid').appendChild(noResultsMsg);
            }
            noResultsMsg.style.display = 'block';
        } else if (noResultsMsg) {
            noResultsMsg.style.display = 'none';
        }
    }

    searchBtn.addEventListener('click', performSearch);
    searchInput.addEventListener('keyup', function(e) {
        if (e.key === 'Enter') {
            performSearch();
        }
    });

    // 复制功能
    const copyButtons = document.querySelectorAll('.copy-btn');
    const copyTooltip = document.getElementById('copyTooltip');

    copyButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.stopPropagation(); // 阻止冒泡

            const item = this.closest('.recycle-item');
            const textToCopy = item.getAttribute('data-name');

            // 创建临时文本区域
            const textArea = document.createElement('textarea');
            textArea.value = textToCopy;
            document.body.appendChild(textArea);
            textArea.select();

            // 复制文本
            document.execCommand('copy');

            // 移除临时元素
            document.body.removeChild(textArea);

            // 显示提示
            copyTooltip.style.opacity = '1';
            copyTooltip.style.top = (item.getBoundingClientRect().top + window.scrollY + 50) + 'px';
            copyTooltip.style.left = (item.getBoundingClientRect().left + item.offsetWidth / 2) + 'px';

            // 2秒后隐藏提示
            setTimeout(function() {
                copyTooltip.style.opacity = '0';
            }, 2000);
        });
    });

    // 点击卡片也可以复制
    recycleItems.forEach(item => {
        item.addEventListener('click', function(e) {
            // 如果点击的是按钮或其子元素，不执行操作（让按钮事件处理）
            if (e.target.closest('.copy-btn')) {
                return;
            }

            const textToCopy = this.getAttribute('data-name');

            // 创建临时文本区域
            const textArea = document.createElement('textarea');
            textArea.value = textToCopy;
            document.body.appendChild(textArea);
            textArea.select();

            // 复制文本
            document.execCommand('copy');

            // 移除临时元素
            document.body.removeChild(textArea);

            // 显示提示
            copyTooltip.style.opacity = '1';
            copyTooltip.style.top = (this.getBoundingClientRect().top + window.scrollY + 50) + 'px';
            copyTooltip.style.left = (this.getBoundingClientRect().left + this.offsetWidth / 2) + 'px';

            // 2秒后隐藏提示
            setTimeout(function() {
                copyTooltip.style.opacity = '0';
            }, 2000);
        });
    });
});
</script>
{% endblock %}
