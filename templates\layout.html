<!DOCTYPE html>
<html lang="zh-CN" class="dark-theme">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}7788商城{% endblock %}</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='vendor/bootstrap/css/bootstrap.min.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='vendor/fontawesome/css/all.min.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
</head>
<body class="dark-theme">
    <!-- 导航栏 -->
    <nav class="main-nav">
        <div class="container nav-container">
            <div class="logo">
                <a href="/">
                    <i class="fas fa-gamepad"></i>
                    <span>7788商城</span>
                </a>
            </div>
            <ul class="nav-menu">
                <li class="nav-item"><a href="/home" class="nav-link {% if active_page == 'home' %}active{% endif %}"><i class="fas fa-home"></i> 主页</a></li>
                <li class="nav-item"><a href="/items" class="nav-link {% if active_page == 'items' %}active{% endif %}"><i class="fas fa-cube"></i> 物品代码</a></li>
                <li class="nav-item"><a href="/sponsor" class="nav-link {% if active_page == 'sponsor' %}active{% endif %}"><i class="fas fa-heart"></i> 赞助</a></li>
                <li class="nav-item"><a href="/entertainment" class="nav-link {% if active_page == 'entertainment' %}active{% endif %}"><i class="fas fa-dice"></i> 娱乐</a></li>
                <li class="nav-item"><a href="/profile" class="nav-link {% if active_page == 'profile' %}active{% endif %}"><i class="fas fa-user"></i> 个人信息</a></li>
                {% if session.username %}
                <li class="nav-item"><a href="/inventory" class="nav-link {% if active_page == 'inventory' %}active{% endif %}"><i class="fas fa-warehouse"></i> 我的仓库</a></li>
                {% endif %}
            </ul>
            <div class="nav-right">
                {% if session.username %}
                <div class="nav-points">
                    <i class="fas fa-star"></i>
                    <span>{{ user.points if user else '0' }}</span>
                </div>
                {% endif %}
                {% if active_page != 'items' %}
                <!-- 在非物品代码页面隐藏搜索框 -->
                {% endif %}
                <div class="auth-links">
                    {% if session.username %}
                    <div class="user-dropdown">
                        <button class="dropdown-toggle">
                            <i class="fas fa-user"></i> {{ session.username }}
                        </button>
                        <div class="dropdown-menu">
                            <a href="{{ url_for('profile') }}"><i class="fas fa-user"></i> 个人信息</a>
                            <a href="{{ url_for('logout') }}"><i class="fas fa-sign-out-alt"></i> 退出登录</a>
                        </div>
                    </div>
                    {% else %}
                    <a href="{{ url_for('login') }}" class="auth-button login-btn"><i class="fas fa-sign-in-alt"></i> 登录</a>
                    {% endif %}
                </div>
            </div>
        </div>
    </nav>

    <!-- 主内容区 -->
    <main class="container">
        {% block content %}{% endblock %}
    </main>

    <!-- 复制成功提示 -->
    <div class="copy-tooltip" id="copyTooltip">已复制到剪贴板<br>请前往游戏内指令框输入</div>

    <!-- 页脚 -->
    <footer class="main-footer">
        <div class="container">
            <div class="footer-bottom">
                <p>&copy; 2025 出其东门 | 保留所有权利</p>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script src="{{ url_for('static', filename='vendor/jquery/jquery-3.5.1.min.js') }}"></script>
    <script src="{{ url_for('static', filename='vendor/bootstrap/js/bootstrap.bundle.min.js') }}"></script>
    <script>
    document.addEventListener('DOMContentLoaded', function() {
        // 复制功能
        const copyButtons = document.querySelectorAll('.copy-btn');
        const copyTooltip = document.getElementById('copyTooltip');

        if (copyButtons.length > 0 && copyTooltip) {
            copyButtons.forEach(button => {
                button.addEventListener('click', function(e) {
                    e.stopPropagation();
                    const textToCopy = this.getAttribute('data-name') || this.getAttribute('data-code');

                    // 创建临时文本区域
                    const textArea = document.createElement('textarea');
                    textArea.value = textToCopy;
                    document.body.appendChild(textArea);
                    textArea.select();

                    // 复制文本
                    document.execCommand('copy');

                    // 移除临时元素
                    document.body.removeChild(textArea);

                    // 显示提示
                    copyTooltip.style.opacity = '1';
                    copyTooltip.style.top = (this.getBoundingClientRect().top - 60) + 'px';
                    copyTooltip.style.left = (this.getBoundingClientRect().left + this.offsetWidth / 2) + 'px';

                    // 3秒后隐藏提示
                    setTimeout(function() {
                        copyTooltip.style.opacity = '0';
                    }, 2000);
                });
            });
        }

        // 用户下拉菜单
        const userDropdown = document.querySelector('.user-dropdown');
        if (userDropdown) {
            const dropdownToggle = userDropdown.querySelector('.dropdown-toggle');
            const dropdownMenu = userDropdown.querySelector('.dropdown-menu');

            dropdownToggle.addEventListener('click', function() {
                dropdownMenu.classList.toggle('show');
            });

            // 点击外部关闭下拉菜单
            document.addEventListener('click', function(e) {
                if (!userDropdown.contains(e.target)) {
                    dropdownMenu.classList.remove('show');
                }
            });
        }
    });
    </script>
    <!-- 引入自定义对话框JS -->
    <script src="{{ url_for('static', filename='js/dialogs.js') }}"></script>

    {% block scripts %}{% endblock %}

    <!-- 自定义确认对话框 -->
    <div id="custom-confirm" class="custom-dialog">
        <div class="dialog-content">
            <div class="dialog-header">
                <span class="dialog-title"><i class="fas fa-exclamation-circle"></i> 确认操作</span>
                <span class="dialog-close">&times;</span>
            </div>
            <div class="dialog-body">
                <p id="dialog-message"></p>
            </div>
            <div class="dialog-footer">
                <button id="dialog-cancel" class="btn-cancel"><i class="fas fa-times"></i> 取消</button>
                <button id="dialog-confirm" class="btn-confirm"><i class="fas fa-check"></i> 确定</button>
            </div>
        </div>
    </div>

    <!-- 自定义提示对话框 -->
    <div id="custom-alert" class="custom-dialog">
        <div class="dialog-content">
            <div class="dialog-header">
                <span class="dialog-title"><i class="fas fa-info-circle"></i> 提示信息</span>
                <span class="dialog-close">&times;</span>
            </div>
            <div class="dialog-body">
                <p id="alert-message"></p>
            </div>
            <div class="dialog-footer">
                <button id="alert-confirm" class="btn-confirm"><i class="fas fa-check"></i> 确定</button>
            </div>
        </div>
    </div>
</body>
</html>