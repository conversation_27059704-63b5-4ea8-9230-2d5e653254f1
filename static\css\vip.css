/* VIP页面样式 */
.vip-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 30px 20px;
}

.vip-header {
    text-align: center;
    margin-bottom: 40px;
}

.vip-header h1 {
    font-size: 2.5rem;
    margin-bottom: 10px;
    color: var(--primary-color);
    display: flex;
    align-items: center;
    justify-content: center;
}

.vip-header h1 i {
    margin-right: 15px;
    color: #ffd700;
    text-shadow: 0 0 10px rgba(255, 215, 0, 0.5);
}

.vip-header p {
    font-size: 1.2rem;
    color: var(--text-secondary);
}

/* VIP特权卡片 */
.vip-benefits {
    display: flex;
    justify-content: space-between;
    margin-bottom: 50px;
    flex-wrap: wrap;
    gap: 20px;
}

.benefit-card {
    flex: 1;
    min-width: 250px;
    background-color: var(--card-bg);
    border-radius: var(--border-radius);
    padding: 25px;
    box-shadow: var(--box-shadow);
    display: flex;
    align-items: center;
    transition: var(--transition);
}

.benefit-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

.benefit-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, var(--primary-color), #0077cc);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 20px;
    flex-shrink: 0;
}

.benefit-icon i {
    font-size: 1.8rem;
    color: white;
}

.benefit-info h3 {
    font-size: 1.3rem;
    margin-bottom: 8px;
    color: var(--text-primary);
}

.benefit-info p {
    font-size: 0.95rem;
    color: var(--text-secondary);
    line-height: 1.5;
}

/* VIP等级部分 */
.vip-levels-section {
    margin-bottom: 50px;
}

.vip-levels-section h2 {
    font-size: 1.8rem;
    margin-bottom: 25px;
    text-align: center;
    color: var(--text-primary);
}

.vip-levels-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 25px;
}

.vip-level-card {
    background-color: var(--card-bg);
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    position: relative;
}

.vip-level-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
}

.vip-level-card.owned {
    border: 2px solid #4caf50;
}

.vip-level-card.next-level {
    border: 2px solid var(--primary-color);
}

.level-header {
    background: linear-gradient(135deg, #333, #222);
    padding: 15px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.level-header h3 {
    font-size: 1.4rem;
    color: white;
    margin: 0;
}

.level-status {
    font-size: 0.9rem;
    padding: 5px 10px;
    border-radius: 20px;
    display: flex;
    align-items: center;
}

.level-status i {
    margin-right: 5px;
}

.level-status.owned {
    background-color: #4caf50;
    color: white;
}

.level-status.next {
    background-color: var(--primary-color);
    color: white;
}

.level-price {
    padding: 20px;
    text-align: center;
    background-color: rgba(0, 0, 0, 0.05);
}

.price-value {
    font-size: 2rem;
    font-weight: 700;
    color: #ff5722;
}

.price-label {
    font-size: 1.1rem;
    color: var(--text-secondary);
    margin-left: 5px;
}

.level-benefits {
    padding: 20px;
}

.benefit-item {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
}

.benefit-item i {
    width: 30px;
    height: 30px;
    background-color: rgba(0, 0, 0, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    color: var(--primary-color);
}

.benefit-item span {
    font-size: 1rem;
    color: var(--text-primary);
}

.level-action {
    padding: 20px;
    text-align: center;
}

.vip-btn {
    padding: 12px 25px;
    border-radius: 30px;
    font-size: 1rem;
    font-weight: 600;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    width: 100%;
}

.vip-btn.upgrade {
    background: linear-gradient(135deg, #ff9800, #ff5722);
    color: white;
    box-shadow: 0 4px 15px rgba(255, 87, 34, 0.3);
}

.vip-btn.upgrade:hover {
    background: linear-gradient(135deg, #ff5722, #ff9800);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 87, 34, 0.4);
}

.vip-btn.owned {
    background-color: #4caf50;
    color: white;
    cursor: default;
}

.vip-btn.locked {
    background-color: #9e9e9e;
    color: white;
    cursor: not-allowed;
}

/* FAQ部分 */
.vip-faq {
    margin-bottom: 30px;
}

.vip-faq h2 {
    font-size: 1.8rem;
    margin-bottom: 25px;
    text-align: center;
    color: var(--text-primary);
}

.faq-item {
    background-color: var(--card-bg);
    border-radius: var(--border-radius);
    margin-bottom: 15px;
    overflow: hidden;
    box-shadow: var(--box-shadow);
}

.faq-question {
    padding: 20px;
    display: flex;
    align-items: center;
    cursor: pointer;
}

.faq-question i {
    font-size: 1.5rem;
    color: var(--primary-color);
    margin-right: 15px;
}

.faq-question h3 {
    font-size: 1.2rem;
    margin: 0;
    color: var(--text-primary);
}

.faq-answer {
    padding: 0 20px 20px 55px;
    color: var(--text-secondary);
    font-size: 1rem;
    line-height: 1.6;
}

/* 确认购买模态框 */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    z-index: 1000;
    justify-content: center;
    align-items: center;
}

.modal-content {
    background-color: var(--card-bg);
    border-radius: var(--border-radius);
    width: 400px;
    max-width: 90%;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    animation: modalFadeIn 0.3s;
    overflow: hidden;
}

@keyframes modalFadeIn {
    from {
        opacity: 0;
        transform: translateY(-50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.modal-header {
    background-color: #333;
    padding: 15px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h2 {
    color: white;
    margin: 0;
    font-size: 1.5rem;
}

.close {
    color: white;
    font-size: 1.8rem;
    cursor: pointer;
}

.modal-body {
    padding: 25px;
}

.modal-body p {
    margin-bottom: 15px;
    font-size: 1.1rem;
    color: var(--text-primary);
}

.modal-body span {
    font-weight: 600;
    color: var(--primary-color);
}

.modal-footer {
    padding: 15px 20px;
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    background-color: rgba(0, 0, 0, 0.05);
}

.primary-btn, .cancel-btn {
    padding: 10px 20px;
    border-radius: 4px;
    font-size: 1rem;
    font-weight: 500;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
}

.primary-btn {
    background-color: var(--primary-color);
    color: white;
}

.primary-btn:hover {
    background-color: #0077cc;
}

.cancel-btn {
    background-color: #9e9e9e;
    color: white;
}

.cancel-btn:hover {
    background-color: #757575;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .vip-benefits {
        flex-direction: column;
    }
    
    .benefit-card {
        margin-bottom: 20px;
    }
    
    .vip-levels-container {
        grid-template-columns: 1fr;
    }
}
