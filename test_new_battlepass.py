#!/usr/bin/env python3
"""
测试新的通行证系统功能
"""

import requests
import json

BASE_URL = "http://127.0.0.1"
TEST_STEAM_ID = "76561199149715992"

def test_quest_complete():
    """测试任务完成（不自动发放奖励）"""
    url = f"{BASE_URL}/api/quest/complete"
    
    data = {
        'steam_id': TEST_STEAM_ID,
        'quest_name': '击杀敌人'
    }
    
    try:
        print("🎯 测试任务完成...")
        response = requests.post(url, json=data)
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print("✅ 任务完成成功!")
                data = result.get('data', {})
                print(f"获得经验: {data.get('exp_gained', 0)}")
                print(f"总经验: {data.get('total_exp', 0)}")
                print(f"等级变化: {data.get('old_level', 0)} -> {data.get('new_level', 0)}")
                if data.get('level_up'):
                    print("🎉 升级了!")
                    print(f"可领取等级: {data.get('rewards_available', [])}")
                print(f"今日进度: {data.get('daily_quest_count', 0)}/{data.get('daily_quest_limit', 20)}")
                return data.get('new_level', 1)
            else:
                print(f"❌ 失败: {result.get('message', '未知错误')}")
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 异常: {e}")
    
    return None

def test_claim_reward(level):
    """测试手动领取奖励"""
    url = f"{BASE_URL}/api/battlepass/claim/{level}"
    
    # 需要先登录获取session
    session = requests.Session()
    
    # 模拟登录（这里需要根据实际登录流程调整）
    login_url = f"{BASE_URL}/login"
    login_data = {
        'steamid': TEST_STEAM_ID,
        'username': 'TestUser'
    }
    
    try:
        print(f"🎁 测试领取等级 {level} 奖励...")
        
        # 先尝试直接领取（如果已经登录）
        response = session.post(url)
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print("✅ 奖励领取成功!")
                data = result.get('data', {})
                print(f"等级: {data.get('level', 0)}")
                print(f"积分奖励: {data.get('points_reward', 0)}")
                rewards = data.get('rewards_given', [])
                if rewards:
                    print("物品奖励:")
                    for reward in rewards:
                        print(f"  - {reward['item_name']} x{reward['quantity']}")
            else:
                print(f"❌ 领取失败: {result.get('message', '未知错误')}")
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 异常: {e}")

def test_recent_quests():
    """测试获取近期任务记录"""
    url = f"{BASE_URL}/api/battlepass/recent-quests"
    
    try:
        print("📋 测试获取近期任务记录...")
        response = requests.get(url)
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print("✅ 获取成功!")
                quests = result.get('data', [])
                print(f"共 {len(quests)} 条记录:")
                for i, quest in enumerate(quests[:5]):  # 只显示前5条
                    print(f"  {i+1}. {quest['quest_name']} - {quest['username']} ({quest['completed_at']})")
            else:
                print(f"❌ 获取失败: {result.get('message', '未知错误')}")
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 异常: {e}")

def main():
    print("🚀 新通行证系统测试")
    print("=" * 50)
    
    # 1. 测试任务完成
    current_level = test_quest_complete()
    
    print("\n" + "-" * 30)
    
    # 2. 测试手动领取奖励
    if current_level and current_level >= 2:
        test_claim_reward(2)  # 尝试领取2级奖励
    
    print("\n" + "-" * 30)
    
    # 3. 测试获取近期任务记录
    test_recent_quests()
    
    print("\n🎊 测试完成!")

if __name__ == "__main__":
    main()
