#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
外部API抽奖接口测试脚本
用于测试 /api/external/lottery/draw 接口
"""

import requests
import json
import argparse
import sys

def test_lottery_api(server_url, steamid, times=1, verbose=False):
    """
    测试外部API抽奖接口
    
    参数:
        server_url: 服务器URL，例如 http://localhost:80
        steamid: 用户的Steam ID
        times: 抽奖次数
        verbose: 是否显示详细信息
    """
    # 构建完整的API URL
    api_url = f"{server_url}/api/external/lottery/draw"
    
    # 构建请求数据
    data = {
        "steamid": steamid,
        "times": times
    }
    
    print(f"正在测试外部API抽奖接口...")
    print(f"请求URL: {api_url}")
    print(f"请求数据: {json.dumps(data, ensure_ascii=False)}")
    
    try:
        # 发送POST请求
        response = requests.post(api_url, json=data)
        
        # 检查响应状态码
        if response.status_code != 200:
            print(f"错误: 服务器返回状态码 {response.status_code}")
            print(f"响应内容: {response.text}")
            return False
        
        # 打印原始响应文本
        print("\n原始响应文本:")
        print(response.text)
        
        # 解析响应JSON
        result = response.json()
        
        # 打印响应结果
        print("\n响应结果:")
        if verbose:
            # 详细模式，打印完整响应
            print(json.dumps(result, ensure_ascii=False, indent=2))
        else:
            # 简洁模式，只打印关键信息
            print(f"成功: {result.get('success', False)}")
            print(f"消息: {result.get('message', '')}")
            
            if result.get('success') and 'data' in result:
                data = result['data']
                print(f"\n总花费积分: {data.get('total_cost', 0)}")
                print(f"抽奖前积分: {data.get('points_before', 0)}")
                print(f"抽奖后积分: {data.get('points_after', 0)}")
                
                print("\n获得物品:")
                items = data.get('items', [])
                for item in items:
                    print(f"  - {item.get('item_name', '未知物品')} x{item.get('count', 1)}")
        
        return result.get('success', False)
    
    except requests.exceptions.ConnectionError:
        print(f"错误: 无法连接到服务器 {server_url}")
        return False
    except requests.exceptions.Timeout:
        print(f"错误: 连接服务器超时")
        return False
    except requests.exceptions.RequestException as e:
        print(f"错误: 请求异常 - {str(e)}")
        return False
    except json.JSONDecodeError:
        print(f"错误: 无法解析服务器响应为JSON")
        print(f"响应内容: {response.text}")
        return False
    except Exception as e:
        print(f"错误: {str(e)}")
        return False

def main():
    """主函数，直接设置参数并执行测试"""
    # 设置默认参数
    server_url = 'http://43.134.33.160:80'
    steamid = '76561199251708689'  # 您的 Steam ID
    times = 100  # 抽奖次数
    verbose = False  # 是否显示详细信息

    # 执行测试
    success = test_lottery_api(server_url, steamid, times, verbose)

    # 根据测试结果设置退出码
    sys.exit(0 if success else 1)

if __name__ == '__main__':
    main()
