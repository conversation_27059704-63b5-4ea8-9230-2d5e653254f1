/* 产品卡片样式 */
.product-card {
    background-color: var(--card-bg);
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    display: flex;
    flex-direction: column;
    height: 100%;
}

.product-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
    background-color: var(--card-hover);
}

.product-image {
    position: relative;
    overflow: hidden;
    height: 200px;
    background-color: #000;
}

.product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.product-card:hover .product-image img {
    transform: scale(1.05);
}

.image-zoom-icon {
    position: absolute;
    top: 10px;
    right: 10px;
    background-color: rgba(0, 0, 0, 0.5);
    color: #fff;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.product-card:hover .image-zoom-icon {
    opacity: 1;
}

.product-info {
    padding: 15px;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
}

.title-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.product-title {
    font-size: 1.1rem;
    font-weight: 600;
    margin: 0;
    color: var(--text-primary);
}

.product-category {
    font-size: 0.8rem;
    color: var(--text-secondary);
    background-color: rgba(0, 0, 0, 0.2);
    padding: 3px 8px;
    border-radius: 4px;
    display: inline-flex;
    align-items: center;
}

.product-category i {
    margin-right: 5px;
}

.price-row {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    flex-wrap: wrap;
    gap: 8px;
}

.product-price {
    font-size: 1.2rem;
    font-weight: 700;
    color: var(--primary-color);
    margin: 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.discounted-price {
    color: #ff3d00;
}

.original-price {
    color: #888;
    text-decoration: line-through;
    font-size: 0.9rem;
    font-weight: normal;
}

.discount-badge {
    background-color: #ff3d00;
    color: white;
    padding: 3px 8px;
    border-radius: 4px;
    font-size: 0.85rem;
    font-weight: bold;
    box-shadow: 0 2px 5px rgba(255, 61, 0, 0.3);
}

.vip-discount-badge {
    background-color: #ffc107;
    color: #000;
    width: auto;
    height: 22px;
    border-radius: 4px;
    font-size: 0.85rem;
    font-weight: bold;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 5px rgba(255, 215, 0, 0.4);
    border: 1px solid rgba(0, 0, 0, 0.2);
    position: relative;
    padding: 0 6px;
}

.vip-discount-badge i {
    display: inline-block;
    color: #ffd700;
    margin-right: 2px;
    font-size: 0.9rem;
    text-shadow: 0 0 2px rgba(0, 0, 0, 0.7);
}

.vip-discount-badge .vip-level {
    font-weight: 800;
    font-size: 1rem;
    display: inline-block;
    line-height: 1;
    color: #ffd700;
    text-shadow: 0 0 2px rgba(0, 0, 0, 0.7);
}

.vip-badge {
    background: linear-gradient(135deg, #ffd700, #ffaa00);
    color: #000;
    padding: 3px 8px;
    border-radius: 4px;
    font-size: 0.85rem;
    font-weight: bold;
    display: inline-flex;
    align-items: center;
    box-shadow: 0 2px 5px rgba(255, 215, 0, 0.4);
    border: 1px solid rgba(255, 255, 255, 0.3);
    text-shadow: 0 1px 1px rgba(255, 255, 255, 0.5);
}

.vip-badge i {
    margin-right: 5px;
    color: #ff6d00;
}

.product-description {
    font-size: 0.9rem;
    color: var(--text-secondary);
    margin-bottom: 15px;
    flex-grow: 1;
}

.product-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: auto;
}

.copy-btn {
    background-color: #2a2a2a;
    color: var(--text-primary);
    border: none;
    padding: 8px 12px;
    border-radius: 4px;
    font-size: 0.9rem;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
}

.copy-btn i {
    margin-right: 5px;
}

.copy-btn:hover {
    background-color: #333;
}

.add-to-cart-btn {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: #ff5722; /* 更亮的橙色 */
    color: white;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: var(--transition);
    box-shadow: 0 3px 8px rgba(255, 87, 34, 0.4);
    font-size: 1.1rem;
}

.add-to-cart-btn:hover {
    transform: scale(1.1);
    box-shadow: 0 5px 15px rgba(255, 87, 34, 0.6);
    background-color: #ff7043;
}

.add-to-cart-btn.disabled {
    background-color: #757575;
    color: #e0e0e0;
    cursor: not-allowed;
    box-shadow: none;
    position: relative;
    overflow: hidden;
}

.add-to-cart-btn.disabled:before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0) 50%);
}

.add-to-cart-btn.disabled:hover {
    transform: none;
    box-shadow: none;
    background-color: #757575;
}

.add-to-cart-btn.disabled i {
    color: #ffd700;
    text-shadow: 0 0 3px rgba(255, 215, 0, 0.5);
}
