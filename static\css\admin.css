/* 管理员页面样式 */
.admin-container {
    padding: 30px 0;
}

.admin-header {
    margin-bottom: 30px;
    text-align: center;
}

.admin-header h1 {
    font-size: 2.5rem;
    margin-bottom: 10px;
    color: #f8f9fa;
}

.admin-header p {
    color: #adb5bd;
    font-size: 1.1rem;
}

.admin-panel {
    background-color: #2a2a2a;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 30px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.admin-panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #444;
}

.admin-panel-title {
    font-size: 1.5rem;
    color: #f8f9fa;
    margin: 0;
}

.admin-search-form {
    display: flex;
    margin-bottom: 20px;
}

.admin-search-input {
    flex: 1;
    padding: 10px 15px;
    border: 1px solid #444;
    background-color: #333;
    color: #fff;
    border-radius: 4px 0 0 4px;
}

.admin-search-btn {
    padding: 10px 20px;
    background-color: #007bff;
    color: white;
    border: none;
    border-radius: 0 4px 4px 0;
    cursor: pointer;
    transition: background-color 0.3s;
}

.admin-search-btn:hover {
    background-color: #0069d9;
}

.admin-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 20px;
}

.admin-table th,
.admin-table td {
    padding: 12px 15px;
    text-align: left;
    border-bottom: 1px solid #444;
}

.admin-table th {
    background-color: #333;
    color: #f8f9fa;
    font-weight: 600;
}

.admin-table tr:hover {
    background-color: #333;
}

.admin-table .user-actions {
    display: flex;
    gap: 10px;
}

.admin-table .btn-edit {
    padding: 6px 12px;
    background-color: #28a745;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.3s;
}

.admin-table .btn-edit:hover {
    background-color: #218838;
}

.admin-pagination {
    display: flex;
    justify-content: center;
    margin-top: 20px;
}

.admin-pagination button {
    padding: 8px 15px;
    margin: 0 5px;
    background-color: #333;
    color: #fff;
    border: 1px solid #444;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.3s;
}

.admin-pagination button:hover {
    background-color: #444;
}

.admin-pagination button.active {
    background-color: #007bff;
    border-color: #007bff;
}

.admin-pagination button:disabled {
    background-color: #222;
    color: #666;
    cursor: not-allowed;
}

.admin-modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    z-index: 1000;
    overflow: auto;
}

.admin-modal-content {
    background-color: #2a2a2a;
    margin: 50px auto;
    padding: 20px;
    width: 80%;
    max-width: 600px;
    border-radius: 8px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
    position: relative;
}

.admin-modal-close {
    position: absolute;
    top: 15px;
    right: 20px;
    font-size: 24px;
    color: #aaa;
    cursor: pointer;
    transition: color 0.3s;
}

.admin-modal-close:hover {
    color: #fff;
}

.admin-modal-header {
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #444;
}

.admin-modal-title {
    font-size: 1.5rem;
    color: #f8f9fa;
    margin: 0;
}

.admin-form-group {
    margin-bottom: 20px;
}

.admin-form-group label {
    display: block;
    margin-bottom: 8px;
    color: #f8f9fa;
}

.admin-form-control {
    width: 100%;
    padding: 10px 15px;
    border: 1px solid #444;
    background-color: #333;
    color: #fff;
    border-radius: 4px;
}

.admin-form-check {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
}

.admin-form-check input[type="checkbox"] {
    margin-right: 10px;
}

.admin-form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    margin-top: 20px;
}

.admin-btn {
    padding: 10px 20px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.3s;
}

.admin-btn-primary {
    background-color: #007bff;
    color: white;
}

.admin-btn-primary:hover {
    background-color: #0069d9;
}

.admin-btn-secondary {
    background-color: #6c757d;
    color: white;
}

.admin-btn-secondary:hover {
    background-color: #5a6268;
}

.admin-badge {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.8rem;
    font-weight: 600;
}

.admin-badge-admin {
    background-color: #dc3545;
    color: white;
}

.admin-badge-vip {
    background-color: #ffc107;
    color: #212529;
}

.admin-badge-user {
    background-color: #6c757d;
    color: white;
}

.admin-alert {
    padding: 15px;
    margin-bottom: 20px;
    border-radius: 4px;
}

.admin-alert-success {
    background-color: rgba(40, 167, 69, 0.2);
    border: 1px solid #28a745;
    color: #28a745;
}

.admin-alert-danger {
    background-color: rgba(220, 53, 69, 0.2);
    border: 1px solid #dc3545;
    color: #dc3545;
}
