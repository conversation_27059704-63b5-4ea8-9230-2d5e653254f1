{% extends "base.html" %}

{% block title %}通行证 - 7788商城{% endblock %}

{% block head_extra %}
<style>
    .battlepass-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 20px;
    }

    .battlepass-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 15px;
        padding: 30px;
        color: white;
        margin-bottom: 30px;
        text-align: center;
    }

    .battlepass-title {
        font-size: 2.5rem;
        font-weight: bold;
        margin-bottom: 10px;
    }

    .battlepass-subtitle {
        font-size: 1.2rem;
        opacity: 0.9;
    }

    .level-progress-section {
        background: var(--card-bg);
        border-radius: 15px;
        padding: 25px;
        margin-bottom: 30px;
        border: 1px solid var(--border-color);
    }

    .current-level {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 20px;
    }

    .level-info {
        display: flex;
        align-items: center;
        gap: 15px;
    }

    .level-badge {
        background: linear-gradient(135deg, #ffd700, #ffed4e);
        color: #333;
        padding: 10px 20px;
        border-radius: 25px;
        font-weight: bold;
        font-size: 1.2rem;
    }

    .exp-info {
        color: var(--text-secondary);
    }

    .daily-quests {
        background: var(--accent-color);
        color: white;
        padding: 8px 15px;
        border-radius: 20px;
        font-size: 0.9rem;
    }

    .progress-bar-container {
        background: var(--bg-secondary);
        border-radius: 10px;
        height: 20px;
        overflow: hidden;
        margin-bottom: 10px;
    }

    .progress-bar {
        background: linear-gradient(90deg, #4CAF50, #45a049);
        height: 100%;
        border-radius: 10px;
        transition: width 0.3s ease;
        position: relative;
    }

    .progress-text {
        text-align: center;
        font-size: 0.9rem;
        color: var(--text-secondary);
    }

    .rewards-section {
        margin-bottom: 30px;
    }

    .section-title {
        font-size: 1.8rem;
        font-weight: bold;
        margin-bottom: 20px;
        color: var(--text-primary);
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .rewards-grid {
        display: flex;
        flex-direction: column;
        gap: 15px;
    }

    .level-group {
        background: var(--card-bg);
        border: 1px solid var(--border-color);
        border-radius: 12px;
        overflow: hidden;
    }

    .level-group.visible {
        display: block;
    }

    .level-group.hidden {
        display: none;
    }

    .level-group-header {
        background: var(--bg-secondary);
        padding: 15px 20px;
        border-bottom: 1px solid var(--border-color);
        cursor: pointer;
        display: flex;
        justify-content: space-between;
        align-items: center;
        transition: background 0.3s ease;
    }

    .level-group-header:hover {
        background: var(--accent-color);
        color: white;
    }

    .level-group-content {
        padding: 20px;
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
        gap: 15px;
    }

    .reward-card {
        background: var(--card-bg);
        border: 1px solid var(--border-color);
        border-radius: 12px;
        padding: 20px;
        transition: all 0.3s ease;
        position: relative;
    }

    .reward-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    }

    .reward-card.claimed {
        background: var(--success-color);
        border-color: var(--success-color);
        opacity: 0.8;
    }

    .reward-card.available {
        border-color: var(--accent-color);
        box-shadow: 0 0 15px rgba(102, 126, 234, 0.3);
    }

    .reward-level {
        background: var(--accent-color);
        color: white;
        padding: 5px 12px;
        border-radius: 15px;
        font-size: 0.9rem;
        font-weight: bold;
        display: inline-block;
        margin-bottom: 15px;
    }

    .reward-content {
        display: flex;
        align-items: center;
        gap: 15px;
        margin-bottom: 15px;
    }

    .reward-icon {
        width: 50px;
        height: 50px;
        background: var(--bg-secondary);
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
    }

    .reward-details h4 {
        margin: 0 0 5px 0;
        color: var(--text-primary);
    }

    .reward-details p {
        margin: 0;
        color: var(--text-secondary);
        font-size: 0.9rem;
    }

    .reward-status {
        text-align: center;
        padding: 8px;
        border-radius: 8px;
        font-size: 0.9rem;
        font-weight: bold;
    }

    .status-claimed {
        background: var(--success-color);
        color: white;
    }

    .status-available {
        background: var(--accent-color);
        color: white;
    }

    .status-locked {
        background: var(--bg-secondary);
        color: var(--text-secondary);
    }

    .claimed-badge {
        position: absolute;
        top: -5px;
        right: -5px;
        background: var(--success-color);
        color: white;
        border-radius: 50%;
        width: 25px;
        height: 25px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 0.8rem;
    }

    /* 移动端适配 */
    @media (max-width: 768px) {
        .battlepass-container {
            padding: 15px;
        }

        .battlepass-title {
            font-size: 2rem;
        }

        .current-level {
            flex-direction: column;
            gap: 15px;
            text-align: center;
        }

        .level-info {
            justify-content: center;
        }

        .rewards-grid {
            grid-template-columns: 1fr;
        }

        .reward-content {
            flex-direction: column;
            text-align: center;
        }
    }

    @media (max-width: 480px) {
        .battlepass-header {
            padding: 20px;
        }

        .level-progress-section {
            padding: 20px;
        }

        .reward-card {
            padding: 15px;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="battlepass-container">
    <!-- 通行证头部 -->
    <div class="battlepass-header">
        <h1 class="battlepass-title">
            <i class="fas fa-trophy"></i>
            战斗通行证
        </h1>
        <p class="battlepass-subtitle">完成任务，获得经验，解锁丰厚奖励</p>
    </div>

    <!-- 等级进度区域 -->
    <div class="level-progress-section">
        <div class="current-level">
            <div class="level-info">
                <div class="level-badge">
                    <i class="fas fa-star"></i>
                    等级 {{ battlepass.current_level }}
                </div>
                <div class="exp-info">
                    <div><strong>总经验:</strong> {{ battlepass.total_exp | format_number }}</div>
                    <div><strong>下级需要:</strong> {{ next_level_exp | format_number }}</div>
                </div>
            </div>
            <div class="daily-quests">
                <i class="fas fa-tasks"></i>
                今日任务: {{ battlepass.daily_quest_count }}/{{ config.daily_quest_limit }}
            </div>
        </div>

        <div class="progress-bar-container">
            <div class="progress-bar" style="width: {{ level_progress }}%"></div>
        </div>
        <div class="progress-text">
            {% if next_level_exp > current_level_exp %}
                {{ (battlepass.total_exp|int - current_level_exp|int) | format_number }} / {{ (next_level_exp|int - current_level_exp|int) | format_number }} 经验
                ({{ "%.1f" | format(level_progress) }}%)
            {% else %}
                已达到最高等级
            {% endif %}
        </div>
    </div>

    <!-- 奖励展示区域 -->
    <div class="rewards-section">
        <h2 class="section-title">
            <i class="fas fa-gift"></i>
            等级奖励
        </h2>

        <div class="rewards-grid">
            {% for level_config in levels %}
                {% set level_rewards = rewards_by_level.get(level_config.level, []) %}
                {% set claimed_levels = claimed_rewards|map(attribute='level')|list %}
                {% set is_claimed = level_config.level in claimed_levels %}
                {% set is_available = level_config.level <= battlepass.current_level and not is_claimed %}
                
                <div class="reward-card {% if is_claimed %}claimed{% elif is_available %}available{% endif %}">
                    {% if is_claimed %}
                        <div class="claimed-badge">
                            <i class="fas fa-check"></i>
                        </div>
                    {% endif %}

                    <div class="reward-level">等级 {{ level_config.level }}</div>

                    <!-- 积分奖励 -->
                    {% if level_config.points_reward > 0 %}
                        <div class="reward-content">
                            <div class="reward-icon">
                                <i class="fas fa-coins" style="color: #ffd700;"></i>
                            </div>
                            <div class="reward-details">
                                <h4>{{ level_config.points_reward | format_number }} 积分</h4>
                                <p>升级奖励积分</p>
                            </div>
                        </div>
                    {% endif %}

                    <!-- 物品奖励 -->
                    {% for reward in level_rewards %}
                        <div class="reward-content">
                            <div class="reward-icon">
                                {% if reward.item_id %}
                                    <!-- 如果有物品ID，尝试显示物品图片 -->
                                    <img src="/static/images/items/{{ reward.item_id }}.png"
                                         alt="{{ reward.item_name }}"
                                         style="width: 40px; height: 40px; object-fit: cover; border-radius: 4px;"
                                         onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                                    <div style="display: none; width: 40px; height: 40px; background: #4CAF50; border-radius: 4px; align-items: center; justify-content: center; color: white; font-size: 1.2rem;">
                                        <i class="fas fa-cube"></i>
                                    </div>
                                {% else %}
                                    <!-- 默认图标 -->
                                    <i class="fas fa-cube" style="color: #4CAF50;"></i>
                                {% endif %}
                            </div>
                            <div class="reward-details">
                                <h4>{{ reward.item_name }}</h4>
                                <p>数量: {{ reward.quantity }}</p>
                            </div>
                        </div>
                    {% endfor %}

                    <!-- 状态显示 -->
                    <div class="reward-status {% if is_claimed %}status-claimed{% elif is_available %}status-available{% else %}status-locked{% endif %}">
                        {% if is_claimed %}
                            <i class="fas fa-check"></i> 已领取
                        {% elif is_available %}
                            <i class="fas fa-unlock"></i> 可领取
                        {% else %}
                            <i class="fas fa-lock"></i> 未解锁
                        {% endif %}
                    </div>
                </div>
            {% endfor %}
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // 可以添加一些交互效果
    console.log('通行证页面加载完成');
});
</script>
{% endblock %}
