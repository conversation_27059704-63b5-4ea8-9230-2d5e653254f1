<!DOCTYPE html>
<html lang="zh-CN" class="dark-theme">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>7788商城</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='vendor/bootstrap/css/bootstrap.min.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='vendor/fontawesome/css/all.min.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
</head>
<body class="dark-theme">
    <!-- 导航栏 -->
    <nav class="main-nav">
        <div class="container nav-container">
            <div class="logo">
                <a href="/">
                    <i class="fas fa-gamepad"></i>
                    <span>7788商城</span>
                </a>
            </div>
            <ul class="nav-menu">
                <li class="nav-item"><a href="/home" class="nav-link {% if active_page == 'home' %}active{% endif %}"><i class="fas fa-home"></i> 主页</a></li>
                <li class="nav-item"><a href="/items" class="nav-link {% if active_page == 'items' %}active{% endif %}"><i class="fas fa-cube"></i> 物品代码</a></li>
                <li class="nav-item"><a href="/sponsor" class="nav-link {% if active_page == 'sponsor' %}active{% endif %}"><i class="fas fa-heart"></i> 赞助</a></li>
                <li class="nav-item"><a href="/entertainment" class="nav-link {% if active_page == 'entertainment' %}active{% endif %}"><i class="fas fa-dice"></i> 娱乐</a></li>
                <li class="nav-item"><a href="/profile" class="nav-link {% if active_page == 'profile' %}active{% endif %}"><i class="fas fa-user"></i> 个人信息</a></li>
                {% if session.username %}
                <li class="nav-item"><a href="/inventory" class="nav-link {% if active_page == 'inventory' %}active{% endif %}"><i class="fas fa-warehouse"></i> 我的仓库</a></li>
                {% endif %}
            </ul>
            <div class="nav-right">
                {% if session.username %}
                <div class="nav-points">
                    <i class="fas fa-coins"></i>
                    <span>{{ user.points if user else '0' }} 积分</span>
                </div>
                {% endif %}
                <div class="auth-links">
                    {% if session.username %}
                    <div class="user-dropdown">
                        <button class="dropdown-toggle">
                            <i class="fas fa-user"></i> {{ session.username }}
                        </button>
                        <div class="dropdown-menu">
                            <a href="{{ url_for('profile') }}"><i class="fas fa-user"></i> 个人信息</a>
                            <a href="{{ url_for('logout') }}"><i class="fas fa-sign-out-alt"></i> 退出登录</a>
                        </div>
                    </div>
                    {% else %}
                    <a href="{{ url_for('login') }}" class="auth-button login-btn"><i class="fas fa-sign-in-alt"></i> 登录</a>
                    {% endif %}
                </div>
            </div>
        </div>
    </nav>

    <!-- 主内容区 -->
    <main class="container">
        {% if active_page == 'home' %}
            <!-- 首页内容 -->
            <div class="home-container">
                <div class="hero-section">
                    <div class="hero-content">
                        <h1>欢迎来到 SCUM 物品商店</h1>
                        <p>获取游戏物品代码，提升您的游戏体验</p>
                        <div class="hero-buttons">
                            <a href="/items" class="primary-btn"><i class="fas fa-cube"></i> 浏览物品</a>
                            <a href="/sponsor" class="secondary-btn"><i class="fas fa-heart"></i> 赞助我们</a>
                        </div>
                    </div>
                    <div class="hero-image">
                        <img src="{{ url_for('static', filename='img/scum-hero.jpg') }}" alt="SCUM游戏">
                    </div>
                </div>

                <div class="features-section">
                    <h2 class="section-heading">主要功能</h2>
                    <div class="features-grid">
                        <div class="feature-card">
                            <div class="feature-icon"><i class="fas fa-cube"></i></div>
                            <h3>物品代码</h3>
                            <p>浏览并复制各种游戏物品代码，轻松获取游戏装备</p>
                        </div>
                        <div class="feature-card">
                            <div class="feature-icon"><i class="fas fa-map-marker-alt"></i></div>
                            <h3>传送点</h3>
                            <p>查找游戏中的各个传送点位置，快速移动到目标区域</p>
                        </div>
                        <div class="feature-card">
                            <div class="feature-icon"><i class="fas fa-crosshairs"></i></div>
                            <h3>武器装备</h3>
                            <p>获取各类武器和装备的代码，增强角色战斗能力</p>
                        </div>
                        <div class="feature-card">
                            <div class="feature-icon"><i class="fas fa-heart"></i></div>
                            <h3>赞助支持</h3>
                            <p>支持我们的发展，获取额外特权和更好的服务体验</p>
                        </div>
                    </div>
                </div>

                <div class="guide-section">
                    <h2 class="section-heading">使用指南</h2>
                    <div class="guide-steps">
                        <div class="guide-step">
                            <div class="step-number">1</div>
                            <h3>浏览物品</h3>
                            <p>在物品代码页面中浏览或搜索需要的物品</p>
                        </div>
                        <div class="guide-step">
                            <div class="step-number">2</div>
                            <h3>复制代码</h3>
                            <p>点击物品卡片或复制按钮获取物品代码</p>
                        </div>
                        <div class="guide-step">
                            <div class="step-number">3</div>
                            <h3>游戏使用</h3>
                            <p>在游戏中打开指令框，粘贴并使用物品代码</p>
                        </div>
                    </div>
                </div>
            </div>
        {% elif active_page == 'items' %}
            <!-- 物品代码页面内容 -->
            <div class="items-container">
                <!-- 搜索框 -->
                <div class="items-search-box">
                    <form action="/search" method="GET">
                        <input type="text" name="q" placeholder="搜索物品..." {% if search_query %}value="{{ search_query }}"{% endif %}>
                        <button type="submit"><i class="fas fa-search"></i></button>
                    </form>
                </div>

                <!-- 分类导航 -->
                <div class="category-tabs">
                    <a href="/category/传送点" class="tab {% if active_category is defined and active_category == '传送点' %}active{% endif %}">
                        <i class="fas fa-map-marker-alt"></i> 传送点
                    </a>
                    <a href="/category/武器" class="tab {% if active_category is defined and active_category == '武器' %}active{% endif %}">
                        <i class="fas fa-crosshairs"></i> 武器
                    </a>
                    {% if categories is defined and categories %}
                        {% for category in categories %}
                            {% if category != '武器' and category != '传送点' %}
                                <a href="/category/{{ category }}" class="tab {% if active_category is defined and active_category == category %}active{% endif %}">
                                    {% if category == '食物' %}
                                        <i class="fas fa-hamburger"></i>
                                    {% elif category == '衣服' %}
                                        <i class="fas fa-tshirt"></i>
                                    {% else %}
                                        <i class="fas fa-box"></i>
                                    {% endif %}
                                    {{ category }}
                                </a>
                            {% endif %}
                        {% endfor %}
                    {% endif %}
                </div>

                <!-- 过滤器 -->
                <div class="filter-bar">
                    <div class="sort-options">
                        <span><i class="fas fa-sort"></i> 排序:</span>
                        {% if display_category == 'search' %}
                            <a href="?q={{ search_query }}&sort=name" class="{% if sort is defined and sort == 'name' %}active{% endif %}">名称</a>
                            <a href="?q={{ search_query }}&sort=points_asc" class="{% if sort is defined and sort == 'points_asc' %}active{% endif %}">价格 <i class="fas fa-arrow-up"></i></a>
                            <a href="?q={{ search_query }}&sort=points_desc" class="{% if sort is defined and sort == 'points_desc' %}active{% endif %}">价格 <i class="fas fa-arrow-down"></i></a>
                        {% elif active_category is defined %}
                            <a href="/category/{{ active_category }}?sort=name" class="{% if sort is defined and sort == 'name' %}active{% endif %}">名称</a>
                            <a href="/category/{{ active_category }}?sort=points_asc" class="{% if sort is defined and sort == 'points_asc' %}active{% endif %}">价格 <i class="fas fa-arrow-up"></i></a>
                            <a href="/category/{{ active_category }}?sort=points_desc" class="{% if sort is defined and sort == 'points_desc' %}active{% endif %}">价格 <i class="fas fa-arrow-down"></i></a>
                        {% else %}
                            <a href="?sort=name" class="{% if sort is defined and sort == 'name' %}active{% endif %}">名称</a>
                            <a href="?sort=points_asc" class="{% if sort is defined and sort == 'points_asc' %}active{% endif %}">价格 <i class="fas fa-arrow-up"></i></a>
                            <a href="?sort=points_desc" class="{% if sort is defined and sort == 'points_desc' %}active{% endif %}">价格 <i class="fas fa-arrow-down"></i></a>
                        {% endif %}
                    </div>
                    <div class="view-options">
                        <button class="grid-view active" title="网格视图"><i class="fas fa-th"></i></button>
                        <button class="list-view" title="列表视图"><i class="fas fa-list"></i></button>
                    </div>
                </div>

                <!-- 商品展示 -->
                <div class="products-container">
                    {% if display_category is defined and display_category == '传送点' and current_items is defined and current_items and current_items.coordinates %}
                        <section class="product-section">
                            <h2 class="section-title"><i class="fas fa-map-marker-alt"></i> 传送点</h2>
                            <div class="products-grid coordinate-grid">
                                {% for coordinate in current_items.coordinates %}
                                    <div class="product-card coordinate-card" data-name="{{ coordinate.coordinateName }}">
                                        <div class="product-info">
                                            <div class="card-icon"><i class="fas fa-map-marker-alt"></i></div>
                                            <h2 class="product-title">{{ coordinate.coordinateName }}</h2>
                                            <p class="product-price">{{ coordinate.points }}</p>
                                            {% if coordinate.remark %}
                                                <p class="product-description">{{ coordinate.remark }}</p>
                                            {% endif %}
                                            <button class="copy-btn" data-name="{{ coordinate.coordinateName }}">
                                                <i class="fas fa-copy"></i> 复制名称
                                            </button>
                                        </div>
                                    </div>
                                {% endfor %}
                            </div>
                        </section>
                    {% endif %}

                    {% if display_category is defined and display_category != '传送点' and current_items is defined and current_items and current_items.packages %}
                        <section class="product-section">
                            <h2 class="section-title">
                                {% if active_category is defined and active_category == '武器' %}
                                    <i class="fas fa-crosshairs"></i> 武器
                                {% elif active_category is defined and active_category == '衣服' %}
                                    <i class="fas fa-tshirt"></i> 衣服
                                {% elif active_category is defined and active_category == '食物' %}
                                    <i class="fas fa-hamburger"></i> 食物
                                {% elif active_category is defined and active_category == 'search' %}
                                    <i class="fas fa-search"></i> 搜索结果：{{ search_query|default('') }}
                                {% else %}
                                    <i class="fas fa-box"></i> {{ active_category|default('物品') }}
                                {% endif %}
                            </h2>
                            <div class="products-grid">
                                {% for package in current_items.packages %}
                                    {% if active_category is not defined or active_category == package.category or active_category == 'all' or active_category == 'search' %}
                                        <div class="product-card" data-name="{{ package.packageName }}">
                                            {% if package.imagePath %}
                                                <div class="product-image">
                                                    <img src="{{ package.imagePath }}" alt="{{ package.packageName }}" class="item-image">
                                                    <div class="image-zoom-icon">
                                                        <i class="fas fa-search-plus"></i>
                                                    </div>
                                                </div>
                                            {% endif %}
                                            <div class="product-info">
                                                <div class="title-row">
                                                    <h2 class="product-title">{{ package.packageName }}</h2>
                                                    <span class="product-category">
                                                        {% if package.category == '武器' %}
                                                            <i class="fas fa-crosshairs"></i>
                                                        {% elif package.category == '衣服' %}
                                                            <i class="fas fa-tshirt"></i>
                                                        {% elif package.category == '食物' %}
                                                            <i class="fas fa-hamburger"></i>
                                                        {% else %}
                                                            <i class="fas fa-box"></i>
                                                        {% endif %}
                                                        {{ package.category }}
                                                    </span>
                                                </div>
                                                <p class="product-price">{{ package.points }}</p>
                                                {% if package.remark %}
                                                    <p class="product-description">{{ package.remark }}</p>
                                                {% endif %}
                                                <div class="product-actions">
                                                    <button class="copy-btn" data-name="{{ package.packageName }}">
                                                        <i class="fas fa-copy"></i> 复制名称
                                                    </button>
                                                    {% if user %}
                                                    <button class="add-to-cart-btn" data-id="{{ package.id }}" data-name="{{ package.packageName }}" data-price="{{ package.points }}">
                                                        <i class="fas fa-plus"></i>
                                                    </button>
                                                    {% endif %}
                                                </div>
                                            </div>
                                        </div>
                                    {% endif %}
                                {% endfor %}
                            </div>
                        </section>
                    {% endif %}

                    {% if (display_category is defined and display_category == '传送点' and (current_items is not defined or not current_items or not current_items.coordinates))
                        or (display_category is defined and display_category != '传送点' and (current_items is not defined or not current_items or not current_items.packages))
                        or (display_category is defined and display_category == 'search' and (current_items is not defined or not current_items or (not current_items.coordinates and not current_items.packages) or (current_items.coordinates|length == 0 and current_items.packages|length == 0))) %}
                        <div class="no-products">
                            <h2>该分类暂无商品</h2>
                            <p>请查看其他分类或稍后再试</p>
                        </div>
                    {% endif %}

                    <!-- 分页 -->
                    {% if total_pages is defined and total_pages > 1 %}
                        <div class="pagination">
                            {% if current_page is defined and current_page > 1 %}
                                {% if display_category == 'search' %}
                                    <a href="?q={{ search_query }}&page={{ current_page - 1 }}{% if sort is defined and sort %}&sort={{ sort }}{% endif %}" class="page-prev"><i class="fas fa-chevron-left"></i></a>
                                {% elif active_category is defined %}
                                    <a href="/category/{{ active_category }}?page={{ current_page - 1 }}{% if sort is defined and sort %}&sort={{ sort }}{% endif %}" class="page-prev"><i class="fas fa-chevron-left"></i></a>
                                {% else %}
                                    <a href="?page={{ current_page - 1 }}{% if sort is defined and sort %}&sort={{ sort }}{% endif %}" class="page-prev"><i class="fas fa-chevron-left"></i></a>
                                {% endif %}
                            {% endif %}

                            {% for i in range(1, total_pages + 1) %}
                                {% if current_page is defined and i == current_page %}
                                    {% if display_category == 'search' %}
                                        <a href="?q={{ search_query }}&page={{ i }}{% if sort is defined and sort %}&sort={{ sort }}{% endif %}" class="page-num active">{{ i }}</a>
                                    {% elif active_category is defined %}
                                        <a href="/category/{{ active_category }}?page={{ i }}{% if sort is defined and sort %}&sort={{ sort }}{% endif %}" class="page-num active">{{ i }}</a>
                                    {% else %}
                                        <a href="?page={{ i }}{% if sort is defined and sort %}&sort={{ sort }}{% endif %}" class="page-num active">{{ i }}</a>
                                    {% endif %}
                                {% elif current_page is defined and i >= current_page - 2 and i <= current_page + 2 %}
                                    {% if display_category == 'search' %}
                                        <a href="?q={{ search_query }}&page={{ i }}{% if sort is defined and sort %}&sort={{ sort }}{% endif %}" class="page-num">{{ i }}</a>
                                    {% elif active_category is defined %}
                                        <a href="/category/{{ active_category }}?page={{ i }}{% if sort is defined and sort %}&sort={{ sort }}{% endif %}" class="page-num">{{ i }}</a>
                                    {% else %}
                                        <a href="?page={{ i }}{% if sort is defined and sort %}&sort={{ sort }}{% endif %}" class="page-num">{{ i }}</a>
                                    {% endif %}
                                {% elif i == 1 or i == total_pages %}
                                    {% if display_category == 'search' %}
                                        <a href="?q={{ search_query }}&page={{ i }}{% if sort is defined and sort %}&sort={{ sort }}{% endif %}" class="page-num">{{ i }}</a>
                                    {% elif active_category is defined %}
                                        <a href="/category/{{ active_category }}?page={{ i }}{% if sort is defined and sort %}&sort={{ sort }}{% endif %}" class="page-num">{{ i }}</a>
                                    {% else %}
                                        <a href="?page={{ i }}{% if sort is defined and sort %}&sort={{ sort }}{% endif %}" class="page-num">{{ i }}</a>
                                    {% endif %}
                                {% elif current_page is defined and (i == current_page - 3 or i == current_page + 3) %}
                                    <span class="ellipsis">...</span>
                                {% endif %}
                            {% endfor %}

                            {% if current_page is defined and current_page < total_pages %}
                                {% if display_category == 'search' %}
                                    <a href="?q={{ search_query }}&page={{ current_page + 1 }}{% if sort is defined and sort %}&sort={{ sort }}{% endif %}" class="page-next"><i class="fas fa-chevron-right"></i></a>
                                {% elif active_category is defined %}
                                    <a href="/category/{{ active_category }}?page={{ current_page + 1 }}{% if sort is defined and sort %}&sort={{ sort }}{% endif %}" class="page-next"><i class="fas fa-chevron-right"></i></a>
                                {% else %}
                                    <a href="?page={{ current_page + 1 }}{% if sort is defined and sort %}&sort={{ sort }}{% endif %}" class="page-next"><i class="fas fa-chevron-right"></i></a>
                                {% endif %}
                            {% endif %}
                        </div>
                    {% endif %}
                </div>
            </div>
        {% elif active_page == 'sponsor' %}
            <!-- 赞助页面内容 -->
            <div class="sponsor-container">
                <div class="sponsor-header">
                    <h1>支持我们的发展</h1>
                    <p>您的赞助将帮助我们提供更好的服务和更多的功能</p>
                </div>

                <div class="sponsor-options">
                    <div class="sponsor-tier">
                        <div class="tier-header">
                            <h2>普通赞助</h2>
                            <div class="tier-price">¥10</div>
                        </div>
                        <div class="tier-benefits">
                            <ul>
                                <li><i class="fas fa-check"></i> 获得赞助者标识</li>
                                <li><i class="fas fa-check"></i> 优先客服支持</li>
                                <li><i class="fas fa-check"></i> 每日礼包 x1</li>
                            </ul>
                        </div>
                        <a href="#" class="sponsor-btn">立即赞助</a>
                    </div>

                    <div class="sponsor-tier featured">
                        <div class="tier-tag">推荐</div>
                        <div class="tier-header">
                            <h2>高级赞助</h2>
                            <div class="tier-price">¥50</div>
                        </div>
                        <div class="tier-benefits">
                            <ul>
                                <li><i class="fas fa-check"></i> 获得高级赞助者标识</li>
                                <li><i class="fas fa-check"></i> VIP客服支持</li>
                                <li><i class="fas fa-check"></i> 每日礼包 x3</li>
                                <li><i class="fas fa-check"></i> 专属武器皮肤</li>
                                <li><i class="fas fa-check"></i> 游戏内特殊称号</li>
                            </ul>
                        </div>
                        <a href="#" class="sponsor-btn">立即赞助</a>
                    </div>

                    <div class="sponsor-tier">
                        <div class="tier-header">
                            <h2>至尊赞助</h2>
                            <div class="tier-price">¥100</div>
                        </div>
                        <div class="tier-benefits">
                            <ul>
                                <li><i class="fas fa-check"></i> 获得至尊赞助者标识</li>
                                <li><i class="fas fa-check"></i> 24小时专属客服</li>
                                <li><i class="fas fa-check"></i> 每日礼包 x5</li>
                                <li><i class="fas fa-check"></i> 全套专属皮肤</li>
                                <li><i class="fas fa-check"></i> 游戏内特殊称号</li>
                                <li><i class="fas fa-check"></i> 专属服务器特权</li>
                            </ul>
                        </div>
                        <a href="#" class="sponsor-btn">立即赞助</a>
                    </div>
                </div>

                <div class="sponsor-qrcode">
                    <h2>扫码赞助</h2>
                    <div class="qrcode-images">
                        <div class="qrcode">
                            <img src="{{ url_for('static', filename='img/wechat-pay.jpg') }}" alt="微信支付">
                            <p>微信支付</p>
                        </div>
                        <div class="qrcode">
                            <img src="{{ url_for('static', filename='img/alipay.jpg') }}" alt="支付宝">
                            <p>支付宝</p>
                        </div>
                    </div>
                    <p class="sponsor-note">赞助后请联系客服QQ: 2397146940 获取赞助特权</p>
                </div>
            </div>
        {% elif under_construction %}
            <!-- 正在开发页面 -->
            <div class="under-construction-container">
                <div class="construction-content">
                    <div class="construction-icon">
                        <i class="fas fa-tools"></i>
                    </div>
                    <h1>{{ page_title }}</h1>
                    <div class="construction-message">
                        <p>{{ construction_message }}</p>
                    </div>
                    <div class="construction-progress">
                        <div class="progress">
                            <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 75%"></div>
                        </div>
                        <p class="progress-text">开发进度：75%</p>
                    </div>
                    <a href="/home" class="return-btn"><i class="fas fa-arrow-left"></i> 返回首页</a>
                </div>
            </div>
        {% elif redirect_message %}
            <!-- 重定向页面 -->
            <div class="redirect-container text-center">
                <div class="redirect-message">
                    <h2>{{ redirect_message }}</h2>
                    <div class="spinner-border mt-4 mb-4" role="status">
                        <span class="sr-only">Loading...</span>
                    </div>
                    <p>正在跳转到相关页面，请稍等...</p>
                </div>
                <script>
                    // 延迟2秒后重定向
                    setTimeout(function() {
                        window.location.href = "{{ redirect_url }}";
                    }, 2000);
                </script>
            </div>
        {% elif error %}
            <div class="error-message">
                <h2>{{ error }}</h2>
                <p>请稍后再试</p>
            </div>
        {% endif %}
    </main>

    <!-- 复制成功提示 -->
    <div class="copy-tooltip" id="copyTooltip">已复制到剪贴板<br>请前往游戏内指令框输入</div>

    <!-- 图片放大模态框 -->
    <div class="image-modal" id="imageModal">
        <span class="modal-close">&times;</span>
        <img class="modal-image" id="modalImage">
        <div class="modal-caption" id="modalCaption"></div>
    </div>

    <!-- 自定义确认对话框 -->
    <div id="custom-confirm" class="custom-dialog">
        <div class="dialog-content">
            <div class="dialog-header">
                <span class="dialog-title"><i class="fas fa-exclamation-circle"></i> 确认操作</span>
                <span class="dialog-close">&times;</span>
            </div>
            <div class="dialog-body">
                <p id="dialog-message"></p>
            </div>
            <div class="dialog-footer">
                <button id="dialog-cancel" class="btn-cancel"><i class="fas fa-times"></i> 取消</button>
                <button id="dialog-confirm" class="btn-confirm"><i class="fas fa-check"></i> 确定</button>
            </div>
        </div>
    </div>

    <!-- 自定义提示对话框 -->
    <div id="custom-alert" class="custom-dialog">
        <div class="dialog-content">
            <div class="dialog-header">
                <span class="dialog-title"><i class="fas fa-info-circle"></i> 提示信息</span>
                <span class="dialog-close">&times;</span>
            </div>
            <div class="dialog-body">
                <p id="alert-message"></p>
            </div>
            <div class="dialog-footer">
                <button id="alert-confirm" class="btn-confirm"><i class="fas fa-check"></i> 确定</button>
            </div>
        </div>
    </div>

    <!-- 浮动购物车 -->
    <div class="floating-cart" id="floatingCart">
        <div class="cart-collapsed" id="cartCollapsed">
            <i class="fas fa-shopping-cart cart-icon"></i>
            <span class="cart-badge" id="cartBadge">0</span>
        </div>
        <div class="cart-expanded" id="cartExpanded">
            <div class="cart-header">
                <div class="cart-title">
                    <i class="fas fa-shopping-cart"></i> 购物车
                </div>
                <div class="cart-close" id="cartClose">
                    <i class="fas fa-times"></i>
                </div>
            </div>
            <div class="cart-items" id="cartItems">
                <div class="cart-empty" id="cartEmpty">
                    <i class="fas fa-shopping-basket"></i>
                    <p>购物车为空</p>
                </div>
                <!-- 购物车物品将通过JavaScript动态添加 -->
            </div>
            <div class="cart-footer">
                <div class="cart-total">
                    <span>总计：</span>
                    <span class="cart-total-price" id="cartTotalPrice">0 积分</span>
                </div>
                <div class="cart-actions">
                    <button class="cart-clear" id="cartClear">清空</button>
                    <button class="cart-checkout" id="cartCheckout">结算</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 购物车取代了直接购买功能 -->

    <!-- 页脚 -->
    <footer class="main-footer">
        <div class="container">
            <div class="footer-bottom">
                <p>&copy; 2025 出其东门 | 保留所有权利</p>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <!-- 内联对话框函数，确保在所有页面都可用 -->
    <script>
    // 自定义确认对话框函数
    function customConfirm(message, callback) {
        const dialog = document.getElementById('custom-confirm');
        const messageEl = document.getElementById('dialog-message');
        const confirmBtn = document.getElementById('dialog-confirm');
        const cancelBtn = document.getElementById('dialog-cancel');
        const closeBtn = dialog.querySelector('.dialog-close');

        // 设置消息
        messageEl.innerHTML = message;

        // 显示对话框
        dialog.style.display = 'flex';

        // 确认按钮事件
        const confirmHandler = function() {
            dialog.style.display = 'none';
            confirmBtn.removeEventListener('click', confirmHandler);
            cancelBtn.removeEventListener('click', cancelHandler);
            closeBtn.removeEventListener('click', cancelHandler);
            callback(true);
        };

        // 取消按钮事件
        const cancelHandler = function() {
            dialog.style.display = 'none';
            confirmBtn.removeEventListener('click', confirmHandler);
            cancelBtn.removeEventListener('click', cancelHandler);
            closeBtn.removeEventListener('click', cancelHandler);
            callback(false);
        };

        // 添加事件监听
        confirmBtn.addEventListener('click', confirmHandler);
        cancelBtn.addEventListener('click', cancelHandler);
        closeBtn.addEventListener('click', cancelHandler);
    }

    // 自定义提示对话框函数
    function customAlert(message, callback) {
        const dialog = document.getElementById('custom-alert');
        const messageEl = document.getElementById('alert-message');
        const confirmBtn = document.getElementById('alert-confirm');
        const closeBtn = dialog.querySelector('.dialog-close');

        // 设置消息
        messageEl.innerHTML = message;

        // 显示对话框
        dialog.style.display = 'flex';

        // 确认按钮事件
        const confirmHandler = function() {
            dialog.style.display = 'none';
            confirmBtn.removeEventListener('click', confirmHandler);
            closeBtn.removeEventListener('click', confirmHandler);
            if (callback) callback();
        };

        // 添加事件监听
        confirmBtn.addEventListener('click', confirmHandler);
        closeBtn.addEventListener('click', confirmHandler);
    }
    </script>
    <script>
    document.addEventListener('DOMContentLoaded', function() {
        // 视图切换
        const gridViewBtn = document.querySelector('.grid-view');
        const listViewBtn = document.querySelector('.list-view');
        const productsGrids = document.querySelectorAll('.products-grid');

        if (gridViewBtn && listViewBtn && productsGrids.length > 0) {
            gridViewBtn.addEventListener('click', function() {
                productsGrids.forEach(grid => grid.classList.remove('list-layout'));
                gridViewBtn.classList.add('active');
                listViewBtn.classList.remove('active');
            });

            listViewBtn.addEventListener('click', function() {
                productsGrids.forEach(grid => grid.classList.add('list-layout'));
                listViewBtn.classList.add('active');
                gridViewBtn.classList.remove('active');
            });
        }

        // 复制功能
        const copyButtons = document.querySelectorAll('.copy-btn');
        const copyTooltip = document.getElementById('copyTooltip');

        copyButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                e.stopPropagation();
                const textToCopy = this.getAttribute('data-name');

                // 创建临时文本区域
                const textArea = document.createElement('textarea');
                textArea.value = textToCopy;
                document.body.appendChild(textArea);
                textArea.select();

                // 复制文本
                document.execCommand('copy');

                // 移除临时元素
                document.body.removeChild(textArea);

                // 显示提示
                copyTooltip.style.opacity = '1';
                copyTooltip.style.top = (this.getBoundingClientRect().top - 60) + 'px';
                copyTooltip.style.left = (this.getBoundingClientRect().left + this.offsetWidth / 2) + 'px';

                // 3秒后隐藏提示
                setTimeout(function() {
                    copyTooltip.style.opacity = '0';
                }, 2000);
            });
        });

        // 卡片点击也触发复制
        const productCards = document.querySelectorAll('.product-card');

        productCards.forEach(card => {
            card.addEventListener('click', function(e) {
                // 如果点击的是按钮或其子元素，不执行操作（让按钮事件处理）
                if (e.target.closest('.copy-btn')) {
                    return;
                }

                const textToCopy = this.getAttribute('data-name');

                // 创建临时文本区域
                const textArea = document.createElement('textarea');
                textArea.value = textToCopy;
                document.body.appendChild(textArea);
                textArea.select();

                // 复制文本
                document.execCommand('copy');

                // 移除临时元素
                document.body.removeChild(textArea);

                // 显示提示
                copyTooltip.style.opacity = '1';
                copyTooltip.style.top = (this.getBoundingClientRect().top + 50) + 'px';
                copyTooltip.style.left = (this.getBoundingClientRect().left + this.offsetWidth / 2) + 'px';

                // 2秒后隐藏提示
                setTimeout(function() {
                    copyTooltip.style.opacity = '0';
                }, 2000);
            });
        });

        // 个人信息页面标签切换
        const tabBtns = document.querySelectorAll('.tab-btn');
        const tabContents = document.querySelectorAll('.tab-content');

        if (tabBtns.length > 0) {
            tabBtns.forEach(btn => {
                btn.addEventListener('click', function() {
                    // 移除所有active类
                    tabBtns.forEach(b => b.classList.remove('active'));
                    tabContents.forEach(c => c.classList.remove('active'));

                    // 添加active类到当前点击的按钮
                    this.classList.add('active');

                    // 显示对应内容
                    const tabId = this.getAttribute('data-tab');
                    document.getElementById(tabId).classList.add('active');
                });
            });
        }

        // 图片放大功能
        const images = document.querySelectorAll('.item-image');
        const zoomIcons = document.querySelectorAll('.image-zoom-icon');
        const imageModal = document.getElementById('imageModal');
        const modalImage = document.getElementById('modalImage');
        const modalCaption = document.getElementById('modalCaption');
        const modalClose = document.querySelector('.modal-close');

        function openImageModal(imgSrc, caption) {
            modalImage.src = imgSrc;
            modalCaption.textContent = caption;
            imageModal.style.display = 'flex';
            document.body.style.overflow = 'hidden'; // 防止背景滚动
        }

        function closeImageModal() {
            imageModal.style.display = 'none';
            document.body.style.overflow = 'auto'; // 恢复背景滚动
        }

        // 点击图片或放大图标打开模态框
        if (images.length > 0) {
            images.forEach(img => {
                img.addEventListener('click', function(e) {
                    e.stopPropagation(); // 阻止冒泡，避免触发卡片的点击事件
                    openImageModal(this.src, this.alt);
                });
            });
        }

        if (zoomIcons.length > 0) {
            zoomIcons.forEach(icon => {
                icon.addEventListener('click', function(e) {
                    e.stopPropagation();
                    const img = this.parentElement.querySelector('.item-image');
                    openImageModal(img.src, img.alt);
                });
            });
        }

        // 关闭模态框
        if (modalClose) {
            modalClose.addEventListener('click', closeImageModal);
        }

        // 点击模态框背景关闭
        imageModal.addEventListener('click', function(e) {
            if (e.target === this) {
                closeImageModal();
            }
        });

        // ESC键关闭模态框
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape' && imageModal.style.display === 'flex') {
                closeImageModal();
            }
        });

        // 购物车功能
        const cart = {
            items: [],

            // 从 localStorage 加载购物车

            load: function() {
                const savedCart = localStorage.getItem('scumShopCart');
                if (savedCart) {
                    try {
                        this.items = JSON.parse(savedCart);
                        // 确保所有ID都是字符串类型
                        this.items = this.items.map(item => ({
                            ...item,
                            id: String(item.id)
                        }));
                        // 加载购物车成功
                    } catch (e) {
                        console.error('加载购物车失败:', e);
                        this.items = [];
                    }
                } else {
                    this.items = [];
                    // 购物车为空，初始化空数组
                }
            },

            // 保存购物车到 localStorage

            save: function() {
                // 确保所有ID都是字符串类型
                const itemsToSave = this.items.map(item => ({
                    ...item,
                    id: String(item.id)
                }));
                localStorage.setItem('scumShopCart', JSON.stringify(itemsToSave));
                // 保存购物车
            },

            // 添加商品到购物车

            addItem: function(id, name, price, original_price, quantity = 1) {
                // 使用物品名称和ID的组合作为唯一标识符
                const uniqueId = `${name}_${id}`;
                // 检查是否已存在相同唯一标识符的商品
                const existingItemIndex = this.items.findIndex(item => String(item.id) === uniqueId);

                if (existingItemIndex >= 0) {
                    this.items[existingItemIndex].quantity += quantity;
                } else {
                    const newItem = {
                        id: uniqueId, // 使用唯一标识符作为ID
                        name: name,
                        price: parseInt(price),
                        original_price: parseInt(original_price || price), // 保存原始价格
                        quantity: quantity,
                        originalId: id // 保存原始ID以便后续使用
                    };
                    this.items.push(newItem);
                }
                this.save();
                this.updateUI();
            },

            // 从购物车移除商品

            removeItem: function(id) {
                const itemId = String(id);
                this.items = this.items.filter(item => String(item.id) !== itemId);
                this.save();
                this.updateUI();
            },

            // 更新商品数量

            updateQuantity: function(id, quantity) {
                const itemId = String(id);
                const item = this.items.find(item => String(item.id) === itemId);
                if (item) {
                    item.quantity = Math.max(1, quantity);
                    this.save();
                    this.updateUI();
                }
            },

            // 清空购物车

            clear: function() {
                this.items = [];
                this.save();
                this.updateUI();
            },

            // 计算总价

            getTotal: function() {
                return this.items.reduce((total, item) => total + (item.price * item.quantity), 0);
            },

            // 获取商品总数

            getItemCount: function() {
                return this.items.reduce((count, item) => count + item.quantity, 0);
            },

            // 更新购物车UI

            updateUI: function() {
                const cartBadge = document.getElementById('cartBadge');
                const cartItems = document.getElementById('cartItems');
                const cartEmpty = document.getElementById('cartEmpty');
                const cartTotalPrice = document.getElementById('cartTotalPrice');

                // 更新UI

                // 更新购物车数量标记
                cartBadge.textContent = this.getItemCount();

                // 更新总价
                cartTotalPrice.textContent = `${this.getTotal()} 积分`;

                // 清空购物车列表
                while (cartItems.firstChild) {
                    if (cartItems.firstChild === cartEmpty) {
                        break;
                    }
                    cartItems.removeChild(cartItems.firstChild);
                }

                // 显示或隐藏空购物车提示
                if (this.items.length === 0) {
                    cartEmpty.style.display = 'block';
                } else {
                    cartEmpty.style.display = 'none';

                    // 添加商品到购物车列表
                    this.items.forEach((item, index) => {
                        const cartItem = document.createElement('div');
                        cartItem.className = 'cart-item';
                        cartItem.innerHTML = `
                            <div class="cart-item-info">
                                <div class="cart-item-name">${item.name}</div>
                                <div class="cart-item-price">${item.price} 积分</div>
                            </div>
                            <div class="cart-item-controls">
                                <button class="cart-quantity-btn decrease-btn" data-id="${item.id}">-</button>
                                <input type="number" class="cart-quantity" value="${item.quantity}" min="1" data-id="${item.id}">
                                <button class="cart-quantity-btn increase-btn" data-id="${item.id}">+</button>
                            </div>
                            <div class="cart-item-remove" data-id="${item.id}">
                                <i class="fas fa-times"></i>
                            </div>
                        `;

                        cartItems.insertBefore(cartItem, cartEmpty);
                        // 商品已添加到UI
                    });

                    // 添加事件监听器
                    const decreaseBtns = cartItems.querySelectorAll('.decrease-btn');
                    const increaseBtns = cartItems.querySelectorAll('.increase-btn');
                    const quantityInputs = cartItems.querySelectorAll('.cart-quantity');
                    const removeButtons = cartItems.querySelectorAll('.cart-item-remove');

                    decreaseBtns.forEach(btn => {
                        btn.addEventListener('click', function() {
                            const id = this.getAttribute('data-id');
                            const item = cart.items.find(item => String(item.id) === String(id));
                            if (item && item.quantity > 1) {
                                cart.updateQuantity(id, item.quantity - 1);
                            }
                        });
                    });

                    increaseBtns.forEach(btn => {
                        btn.addEventListener('click', function() {
                            const id = this.getAttribute('data-id');
                            const item = cart.items.find(item => String(item.id) === String(id));
                            if (item) {
                                cart.updateQuantity(id, item.quantity + 1);
                            }
                        });
                    });

                    quantityInputs.forEach(input => {
                        input.addEventListener('change', function() {
                            const id = this.getAttribute('data-id');
                            const quantity = parseInt(this.value) || 1;
                            cart.updateQuantity(id, quantity);
                        });
                    });

                    removeButtons.forEach(btn => {
                        btn.addEventListener('click', function() {
                            const id = this.getAttribute('data-id');
                            cart.removeItem(id);
                        });
                    });
                }
            }
        };

        // 初始化购物车
        cart.load();
        cart.updateUI();

        // 购物车展开/收起功能
        const cartCollapsed = document.getElementById('cartCollapsed');
        const cartExpanded = document.getElementById('cartExpanded');
        const cartClose = document.getElementById('cartClose');

        cartCollapsed.addEventListener('click', function() {
            cartExpanded.style.display = 'flex';
            cartCollapsed.style.display = 'none';
        });

        cartClose.addEventListener('click', function() {
            cartExpanded.style.display = 'none';
            cartCollapsed.style.display = 'flex';
        });

        // 添加到购物车按钮
        const addToCartButtons = document.querySelectorAll('.add-to-cart-btn');

        if (addToCartButtons.length > 0) {
            addToCartButtons.forEach(btn => {
                btn.addEventListener('click', function(e) {
                    e.stopPropagation();
                    const id = this.getAttribute('data-id');
                    const name = this.getAttribute('data-name');
                    const price = this.getAttribute('data-price');
                    const originalPrice = this.getAttribute('data-original-price') || price;

                    cart.addItem(id, name, price, originalPrice);

                    // 添加动画效果
                    this.classList.add('added');
                    setTimeout(() => {
                        this.classList.remove('added');
                    }, 500);

                    // 显示提示
                    const copyTooltip = document.getElementById('copyTooltip');
                    copyTooltip.textContent = '已添加到购物车';
                    copyTooltip.style.opacity = '1';
                    copyTooltip.style.top = (this.getBoundingClientRect().top - 40) + 'px';
                    copyTooltip.style.left = (this.getBoundingClientRect().left + this.offsetWidth / 2) + 'px';

                    setTimeout(function() {
                        copyTooltip.style.opacity = '0';
                        copyTooltip.textContent = '已复制到剪贴板\n请前往游戏内指令框输入';
                    }, 1000);
                });
            });
        }

        // 清空购物车
        const cartClear = document.getElementById('cartClear');
        cartClear.addEventListener('click', function() {
            customConfirm('确定要清空购物车吗？', (confirmed) => {
                if (confirmed) {
                    cart.clear();
                }
            });
        });

        // 结算功能
        const cartCheckout = document.getElementById('cartCheckout');
        cartCheckout.addEventListener('click', function() {
            if (cart.items.length === 0) {
                customAlert('购物车为空，请先添加商品');
                return;
            }

            // 显示详细的结算信息
            let itemsList = cart.items.map(item => `${item.name} x ${item.quantity} (${item.price * item.quantity} 积分)`).join('<br>');
            let confirmMessage = `确定要结算以下物品吗？<br><br>${itemsList}<br><br>总计: <b>${cart.getTotal()} 积分</b>`;

            customConfirm(confirmMessage, (confirmed) => {
                if (confirmed) {
                    // 发送结算请求

                    // 发送结算请求到服务器
                    fetch('/buy_items', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-Requested-With': 'XMLHttpRequest'
                        },
                        body: JSON.stringify({
                            items: cart.items.map(item => ({
                                id: item.originalId || item.id.split('_')[1], // 使用原始ID或从唯一ID中提取
                                name: item.name,
                                price: item.price,
                                original_price: item.original_price || item.price, // 添加原始价格
                                quantity: item.quantity
                            })),
                            total_price: cart.getTotal()
                        })
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            // 更新用户积分显示
                            const pointsDisplay = document.querySelector('.nav-points span');
                            if (pointsDisplay) {
                                pointsDisplay.textContent = data.user_points;
                            }

                            // 清空购物车
                            cart.clear();

                            // 显示成功提示
                            let successMessage = data.message || '购买成功！';
                            if (data.purchased_items && data.purchased_items.length > 0) {
                                successMessage += '<br><br>已购买的物品：<br>' +
                                    data.purchased_items.map(item => `${item.name} x ${item.quantity}`).join('<br>');
                            }
                            customAlert(successMessage);

                            // 关闭购物车
                            cartExpanded.style.display = 'none';
                            cartCollapsed.style.display = 'flex';
                        } else {
                            // 显示错误提示
                            customAlert(data.message || '购买失败，请稍后再试。');
                        }
                    })
                    .catch(error => {
                        console.error('结算请求失败:', error);
                        customAlert('网络错误，请稍后重试');
                    });
                }
            });
        });

        // ESC键关闭购物车
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape' && cartExpanded.style.display === 'flex') {
                cartExpanded.style.display = 'none';
                cartCollapsed.style.display = 'flex';
            }
        });
    });
    </script>
</body>
</html>