数据库 'scum' 的表结构：

表名: action_logs
--------------------------------------------------------------------------------
字段名                  类型                   可空         键          默认值                  额外                  
id                   int(11)              NO         PRI        None                 auto_increment      
steamid              varchar(255)         YES                   None                                     
log_time             bigint(20)           YES                   None                                     
action_type          varchar(50)          YES                   None                                     
details              text                 YES                   None                                     

建表语句:
CREATE TABLE `action_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `steamid` varchar(255) COLLATE utf8_bin DEFAULT NULL,
  `log_time` bigint(20) DEFAULT NULL,
  `action_type` varchar(50) COLLATE utf8_bin DEFAULT NULL,
  `details` text COLLATE utf8_bin,
  PRIMARY KEY (`id`)
) ENGINE=MyISAM AUTO_INCREMENT=157 DEFAULT CHARSET=utf8 COLLATE=utf8_bin

================================================================================

表名: admin_logs
--------------------------------------------------------------------------------
字段名                  类型                   可空         键          默认值                  额外                  
id                   int(11)              NO         PRI        None                 auto_increment      
steamid              varchar(255)         YES                   None                                     
log_time             bigint(20)           YES                   None                                     
action               varchar(255)         YES                   None                                     
target_steamid       varchar(255)         YES                   None                                     
details              text                 YES                   None                                     

建表语句:
CREATE TABLE `admin_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `steamid` varchar(255) COLLATE utf8_bin DEFAULT NULL,
  `log_time` bigint(20) DEFAULT NULL,
  `action` varchar(255) COLLATE utf8_bin DEFAULT NULL,
  `target_steamid` varchar(255) COLLATE utf8_bin DEFAULT NULL,
  `details` text COLLATE utf8_bin,
  PRIMARY KEY (`id`)
) ENGINE=MyISAM AUTO_INCREMENT=57 DEFAULT CHARSET=utf8 COLLATE=utf8_bin

================================================================================

表名: chat_logs
--------------------------------------------------------------------------------
字段名                  类型                   可空         键          默认值                  额外                  
id                   int(11)              NO         PRI        None                 auto_increment      
steamid              varchar(255)         YES                   None                                     
log_time             bigint(20)           YES                   None                                     
message              text                 YES                   None                                     
channel              varchar(50)          YES                   None                                     

建表语句:
CREATE TABLE `chat_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `steamid` varchar(255) COLLATE utf8_bin DEFAULT NULL,
  `log_time` bigint(20) DEFAULT NULL,
  `message` text COLLATE utf8_bin,
  `channel` varchar(50) COLLATE utf8_bin DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=MyISAM AUTO_INCREMENT=29889 DEFAULT CHARSET=utf8 COLLATE=utf8_bin

================================================================================

表名: gift_logs
--------------------------------------------------------------------------------
字段名                  类型                   可空         键          默认值                  额外                  
id                   int(11)              NO         PRI        None                 auto_increment      
steamid              varchar(255)         YES                   None                                     
gift_type            int(11)              YES                   None                                     
received_at          timestamp            NO                    CURRENT_TIMESTAMP                        
points               int(11)              YES                   0                                        

建表语句:
CREATE TABLE `gift_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `steamid` varchar(255) COLLATE utf8_bin DEFAULT NULL,
  `gift_type` int(11) DEFAULT NULL,
  `received_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `points` int(11) DEFAULT '0' COMMENT '获得积分数',
  PRIMARY KEY (`id`)
) ENGINE=MyISAM AUTO_INCREMENT=68 DEFAULT CHARSET=utf8 COLLATE=utf8_bin

================================================================================

表名: kill_logs
--------------------------------------------------------------------------------
字段名                  类型                   可空         键          默认值                  额外                  
id                   int(11)              NO         PRI        None                 auto_increment      
steamid              varchar(255)         YES                   None                                     
victimid             varchar(255)         YES                   None                                     
log_time             bigint(20)           YES                   None                                     
weapon               varchar(50)          YES                   None                                     
distance             float                YES                   None                                     

建表语句:
CREATE TABLE `kill_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `steamid` varchar(255) COLLATE utf8_bin DEFAULT NULL,
  `victimid` varchar(255) COLLATE utf8_bin DEFAULT NULL,
  `log_time` bigint(20) DEFAULT NULL,
  `weapon` varchar(50) COLLATE utf8_bin DEFAULT NULL,
  `distance` float DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8 COLLATE=utf8_bin

================================================================================

表名: login_logs
--------------------------------------------------------------------------------
字段名                  类型                   可空         键          默认值                  额外                  
id                   int(11)              NO         PRI        None                 auto_increment      
steamid              varchar(255)         YES                   None                                     
log_time             bigint(20)           YES                   None                                     
ip_address           varchar(50)          YES                   None                                     

建表语句:
CREATE TABLE `login_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `steamid` varchar(255) COLLATE utf8_bin DEFAULT NULL,
  `log_time` bigint(20) DEFAULT NULL,
  `ip_address` varchar(50) COLLATE utf8_bin DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=MyISAM AUTO_INCREMENT=18428 DEFAULT CHARSET=utf8 COLLATE=utf8_bin

================================================================================

表名: lottery_history
--------------------------------------------------------------------------------
字段名                  类型                   可空         键          默认值                  额外                  
id                   int(11)              NO         PRI        None                 auto_increment      
steamid              varchar(255)         NO                    None                                     
item_code            varchar(255)         NO                    None                                     
item_name            varchar(255)         NO                    None                                     
draw_time            datetime             NO                    None                                     

建表语句:
CREATE TABLE `lottery_history` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `steamid` varchar(255) NOT NULL,
  `item_code` varchar(255) NOT NULL,
  `item_name` varchar(255) NOT NULL,
  `draw_time` datetime NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4

================================================================================

表名: lottery_logs
--------------------------------------------------------------------------------
字段名                  类型                   可空         键          默认值                  额外                  
id                   int(11)              NO         PRI        None                 auto_increment      
steamid              varchar(255)         NO                    None                                     
item_id              varchar(255)         NO                    None                                     
item_name            varchar(255)         NO                    None                                     
points_cost          int(11)              NO                    None                                     
draw_time            datetime             NO                    None                                     
draw_type            varchar(20)          NO                    None                                     

建表语句:
CREATE TABLE `lottery_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `steamid` varchar(255) NOT NULL,
  `item_id` varchar(255) NOT NULL,
  `item_name` varchar(255) NOT NULL,
  `points_cost` int(11) NOT NULL,
  `draw_time` datetime NOT NULL,
  `draw_type` varchar(20) NOT NULL COMMENT '抽奖类型：single, ten, hundred',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=5100 DEFAULT CHARSET=utf8mb4

================================================================================

表名: lottery_prizes
--------------------------------------------------------------------------------
字段名                  类型                   可空         键          默认值                  额外                  
id                   int(11)              NO         PRI        None                 auto_increment      
item_id              varchar(255)         NO                    None                                     
item_name            varchar(255)         NO                    None                                     
probability          decimal(10,4)        NO                    None                                     
image_path           varchar(255)         YES                   None                                     
created_at           datetime             NO                    CURRENT_TIMESTAMP                        
updated_at           datetime             NO                    CURRENT_TIMESTAMP    on update CURRENT_TIMESTAMP
created_by           varchar(255)         NO                    None                                     
is_active            tinyint(1)           NO                    1                                        

建表语句:
CREATE TABLE `lottery_prizes` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `item_id` varchar(255) NOT NULL,
  `item_name` varchar(255) NOT NULL,
  `probability` decimal(10,4) NOT NULL COMMENT '中奖概率，例如1.5表示1.5%',
  `image_path` varchar(255) DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `created_by` varchar(255) NOT NULL COMMENT '创建者的steamid',
  `is_active` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否激活，可用于临时禁用某个奖品',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=88 DEFAULT CHARSET=utf8mb4

================================================================================

表名: purchase_history
--------------------------------------------------------------------------------
字段名                  类型                   可空         键          默认值                  额外                  
id                   int(11)              NO         PRI        None                 auto_increment      
steamid              varchar(50)          NO                    None                                     
item_name            varchar(255)         NO                    None                                     
item_id              varchar(255)         YES                   None                                     
quantity             int(11)              NO                    1                                        
points_spent         int(11)              NO                    None                                     
purchase_date        datetime             NO                    None                                     

建表语句:
CREATE TABLE `purchase_history` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `steamid` varchar(50) NOT NULL,
  `item_name` varchar(255) NOT NULL,
  `item_id` varchar(255) DEFAULT NULL,
  `quantity` int(11) NOT NULL DEFAULT '1',
  `points_spent` int(11) NOT NULL,
  `purchase_date` datetime NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=262 DEFAULT CHARSET=utf8mb4

================================================================================

表名: system_config
--------------------------------------------------------------------------------
字段名                  类型                   可空         键          默认值                  额外                  
config_key           varchar(50)          NO         PRI        None                                     
config_value         text                 NO                    None                                     
updated_at           datetime             NO                    CURRENT_TIMESTAMP    on update CURRENT_TIMESTAMP

建表语句:
CREATE TABLE `system_config` (
  `config_key` varchar(50) NOT NULL,
  `config_value` text NOT NULL,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`config_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4

================================================================================

表名: user_inventory
--------------------------------------------------------------------------------
字段名                  类型                   可空         键          默认值                  额外                  
id                   int(11)              NO         PRI        None                 auto_increment      
steamid              varchar(50)          NO                    None                                     
item_name            varchar(255)         NO                    None                                     
item_id              varchar(255)         YES                   None                                     
quantity             int(11)              NO                    1                                        
purchase_date        datetime             NO                    None                                     
used                 tinyint(1)           NO                    0                                        

建表语句:
CREATE TABLE `user_inventory` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `steamid` varchar(50) NOT NULL,
  `item_name` varchar(255) NOT NULL,
  `item_id` varchar(255) DEFAULT NULL,
  `quantity` int(11) NOT NULL DEFAULT '1',
  `purchase_date` datetime NOT NULL,
  `used` tinyint(1) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=5474 DEFAULT CHARSET=utf8mb4

================================================================================

表名: users
--------------------------------------------------------------------------------
字段名                  类型                   可空         键          默认值                  额外                  
steamid              varchar(255)         NO         PRI        None                                     
username             varchar(255)         YES        UNI        None                                     
password             varchar(255)         YES                   None                                     
is_signed_in         tinyint(1)           YES                   0                                        
is_online            tinyint(1)           YES                   0                                        
position             varchar(255)         YES                   None                                     
is_admin             tinyint(1)           YES                   0                                        
kills                int(11)              YES                   0                                        
deaths               int(11)              YES                   0                                        
kd                   float                YES                   None                                     
points               int(11)              YES                   0                                        
gift_received_1      int(11)              YES                   0                                        
gift_received_2      int(11)              YES                   0                                        
gift_received_3      int(11)              YES                   0                                        
gift_received_4      int(11)              YES                   0                                        
gift_received_5      int(11)              YES                   0                                        
other_gift_received  int(11)              YES                   0                                        
skill_cd_1           bigint(20)           YES                   0                                        
skill_cd_2           bigint(20)           YES                   0                                        
skill_cd_3           bigint(20)           YES                   0                                        
skill_cd_4           bigint(20)           YES                   0                                        
skill_cd_5           bigint(20)           YES                   0                                        
user_rank            int(11)              YES                   None                                     
country              varchar(255)         YES                   None                                     
season_points        int(11)              YES                   0                                        
is_vip               tinyint(1)           YES                   0                                        
xyz                  text                 YES                   None                                     
last_sign_time       datetime             YES                   None                                     
checkin_streak       int(11)              YES                   0                                        
checkin_days         int(11)              YES                   0                                        
vip_level            int(11)              YES                   0                                        

建表语句:
CREATE TABLE `users` (
  `steamid` varchar(255) COLLATE utf8_bin NOT NULL,
  `username` varchar(255) COLLATE utf8_bin DEFAULT NULL,
  `password` varchar(255) COLLATE utf8_bin DEFAULT NULL,
  `is_signed_in` tinyint(1) DEFAULT '0',
  `is_online` tinyint(1) DEFAULT '0',
  `position` varchar(255) COLLATE utf8_bin DEFAULT NULL,
  `is_admin` tinyint(1) DEFAULT '0',
  `kills` int(11) DEFAULT '0',
  `deaths` int(11) DEFAULT '0',
  `kd` float DEFAULT NULL,
  `points` int(11) DEFAULT '0',
  `gift_received_1` int(11) DEFAULT '0',
  `gift_received_2` int(11) DEFAULT '0',
  `gift_received_3` int(11) DEFAULT '0',
  `gift_received_4` int(11) DEFAULT '0',
  `gift_received_5` int(11) DEFAULT '0',
  `other_gift_received` int(11) DEFAULT '0',
  `skill_cd_1` bigint(20) DEFAULT '0',
  `skill_cd_2` bigint(20) DEFAULT '0',
  `skill_cd_3` bigint(20) DEFAULT '0',
  `skill_cd_4` bigint(20) DEFAULT '0',
  `skill_cd_5` bigint(20) DEFAULT '0',
  `user_rank` int(11) DEFAULT NULL,
  `country` varchar(255) COLLATE utf8_bin DEFAULT NULL,
  `season_points` int(11) DEFAULT '0',
  `is_vip` tinyint(1) DEFAULT '0',
  `xyz` text COLLATE utf8_bin,
  `last_sign_time` datetime DEFAULT NULL COMMENT '上次签到时间',
  `checkin_streak` int(11) DEFAULT '0' COMMENT '连续签到天数',
  `checkin_days` int(11) DEFAULT '0' COMMENT '总签到天数',
  `vip_level` int(11) DEFAULT '0' COMMENT 'VIP等级，0表示非VIP，1-9表示VIP等级',
  PRIMARY KEY (`steamid`),
  UNIQUE KEY `username` (`username`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8 COLLATE=utf8_bin

================================================================================

表名: vip_levels
--------------------------------------------------------------------------------
字段名                  类型                   可空         键          默认值                  额外                  
level                int(11)              NO         PRI        None                                     
points_required      int(11)              NO                    None                                     
points_bonus_rate    float                NO                    None                                     
discount_rate        float                NO                    None                                     
checkin_bonus        int(11)              NO                    None                                     

建表语句:
CREATE TABLE `vip_levels` (
  `level` int(11) NOT NULL,
  `points_required` int(11) NOT NULL COMMENT '升级所需积分',
  `points_bonus_rate` float NOT NULL COMMENT '积分奖励倍率',
  `discount_rate` float NOT NULL COMMENT '购物折扣率',
  `checkin_bonus` int(11) NOT NULL COMMENT '签到奖励积分',
  PRIMARY KEY (`level`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4

================================================================================

表名: vip_purchase_logs
--------------------------------------------------------------------------------
字段名                  类型                   可空         键          默认值                  额外                  
id                   int(11)              NO         PRI        None                 auto_increment      
steamid              varchar(255)         NO                    None                                     
purchase_time        datetime             NO                    None                                     
vip_level            int(11)              NO                    None                                     
points_spent         int(11)              NO                    None                                     

建表语句:
CREATE TABLE `vip_purchase_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `steamid` varchar(255) NOT NULL,
  `purchase_time` datetime NOT NULL,
  `vip_level` int(11) NOT NULL,
  `points_spent` int(11) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=11 DEFAULT CHARSET=utf8mb4

================================================================================

