{% extends "base.html" %}

{% block title %}物品代码 - 7788商城{% endblock %}

{% block content %}
<div class="items-container">
    <!-- 搜索框 -->
    <div class="items-search-box">
        <form action="/search" method="GET">
            <input type="text" name="q" placeholder="搜索物品..." {% if search_query %}value="{{ search_query }}"{% endif %}>
            <button type="submit"><i class="fas fa-search"></i></button>
        </form>
    </div>

    <!-- 分类导航 -->
    <div class="category-tabs">
        <a href="/category/传送点" class="tab {% if active_category is defined and active_category == '传送点' %}active{% endif %}">
            <i class="fas fa-map-marker-alt"></i> 传送点
        </a>
        <a href="/category/武器" class="tab {% if active_category is defined and active_category == '武器' %}active{% endif %}">
            <i class="fas fa-crosshairs"></i> 武器
        </a>
        {% if categories is defined and categories %}
            {% for category in categories %}
                {% if category != '武器' and category != '传送点' %}
                    <a href="/category/{{ category }}" class="tab {% if active_category is defined and active_category == category %}active{% endif %}">
                        {% if category == '食物' %}
                            <i class="fas fa-hamburger"></i>
                        {% elif category == '衣服' %}
                            <i class="fas fa-tshirt"></i>
                        {% else %}
                            <i class="fas fa-box"></i>
                        {% endif %}
                        {{ category }}
                    </a>
                {% endif %}
            {% endfor %}
        {% endif %}
    </div>

    <!-- 过滤器 -->
    <div class="filter-bar">
        <div class="sort-options">
            <span><i class="fas fa-sort"></i> 排序:</span>
            {% if display_category == 'search' %}
                <a href="?q={{ search_query }}&sort=name" class="{% if sort is defined and sort == 'name' %}active{% endif %}">名称</a>
                <a href="?q={{ search_query }}&sort=points_asc" class="{% if sort is defined and sort == 'points_asc' %}active{% endif %}">价格 <i class="fas fa-arrow-up"></i></a>
                <a href="?q={{ search_query }}&sort=points_desc" class="{% if sort is defined and sort == 'points_desc' %}active{% endif %}">价格 <i class="fas fa-arrow-down"></i></a>
            {% elif active_category is defined %}
                <a href="/category/{{ active_category }}?sort=name" class="{% if sort is defined and sort == 'name' %}active{% endif %}">名称</a>
                <a href="/category/{{ active_category }}?sort=points_asc" class="{% if sort is defined and sort == 'points_asc' %}active{% endif %}">价格 <i class="fas fa-arrow-up"></i></a>
                <a href="/category/{{ active_category }}?sort=points_desc" class="{% if sort is defined and sort == 'points_desc' %}active{% endif %}">价格 <i class="fas fa-arrow-down"></i></a>
            {% else %}
                <a href="?sort=name" class="{% if sort is defined and sort == 'name' %}active{% endif %}">名称</a>
                <a href="?sort=points_asc" class="{% if sort is defined and sort == 'points_asc' %}active{% endif %}">价格 <i class="fas fa-arrow-up"></i></a>
                <a href="?sort=points_desc" class="{% if sort is defined and sort == 'points_desc' %}active{% endif %}">价格 <i class="fas fa-arrow-down"></i></a>
            {% endif %}
        </div>
        <div class="view-options">
            <button class="grid-view active" title="网格视图"><i class="fas fa-th"></i></button>
            <button class="list-view" title="列表视图"><i class="fas fa-list"></i></button>
        </div>
    </div>

    <!-- 商品展示 -->
    <div class="products-container">
        {% if display_category is defined and display_category == '传送点' and current_items is defined and current_items and current_items.coordinates %}
            <section class="product-section">
                <h2 class="section-title"><i class="fas fa-map-marker-alt"></i> 传送点</h2>
                <div class="products-grid coordinate-grid">
                    {% for coordinate in current_items.coordinates %}
                        <div class="product-card coordinate-card" data-name="{{ coordinate.coordinateName }}">
                            <div class="product-info">
                                <div class="card-icon"><i class="fas fa-map-marker-alt"></i></div>
                                <h2 class="product-title">{{ coordinate.coordinateName }}</h2>
                                <p class="product-price">{{ coordinate.points }}</p>
                                {% if coordinate.remark %}
                                    <p class="product-description">{{ coordinate.remark }}</p>
                                {% endif %}
                                <button class="copy-btn" data-name="{{ coordinate.coordinateName }}">
                                    <i class="fas fa-copy"></i> 复制名称
                                </button>
                            </div>
                        </div>
                    {% endfor %}
                </div>
            </section>
        {% endif %}

        {% if display_category is defined and display_category != '传送点' and current_items is defined and current_items and current_items.packages %}
            <section class="product-section">
                <h2 class="section-title">
                    {% if active_category is defined and active_category == '武器' %}
                        <i class="fas fa-crosshairs"></i> 武器
                    {% elif active_category is defined and active_category == '衣服' %}
                        <i class="fas fa-tshirt"></i> 衣服
                    {% elif active_category is defined and active_category == '食物' %}
                        <i class="fas fa-hamburger"></i> 食物
                    {% elif active_category is defined and active_category == 'search' %}
                        <i class="fas fa-search"></i> 搜索结果：{{ search_query|default('') }}
                    {% else %}
                        <i class="fas fa-box"></i> {{ active_category|default('物品') }}
                    {% endif %}
                </h2>
                <div class="products-grid">
                    {% for package in current_items.packages %}
                        {% if active_category is not defined or active_category == package.category or active_category == 'all' or active_category == 'search' %}
                            <div class="product-card" data-name="{{ package.packageName }}">
                                {% if package.imagePath %}
                                    <div class="product-image">
                                        <img src="{{ package.imagePath }}" alt="{{ package.packageName }}" class="item-image">
                                        <div class="image-zoom-icon">
                                            <i class="fas fa-search-plus"></i>
                                        </div>
                                    </div>
                                {% endif %}
                                <div class="product-info">
                                    <div class="title-row">
                                        <h2 class="product-title">{{ package.packageName }}</h2>
                                        <span class="product-category">
                                            {% if package.category == '武器' %}
                                                <i class="fas fa-crosshairs"></i>
                                            {% elif package.category == '衣服' %}
                                                <i class="fas fa-tshirt"></i>
                                            {% elif package.category == '食物' %}
                                                <i class="fas fa-hamburger"></i>
                                            {% else %}
                                                <i class="fas fa-box"></i>
                                            {% endif %}
                                            {{ package.category }}
                                        </span>
                                    </div>
                                    <div class="price-row">
                                        {% if user and user.get('vip_level', 0) > 0 %}
                                            {% set discount_rate = (10 - user.get('vip_level', 0)) / 10 %}
                                            {% set vip_price = (package.points * discount_rate) | round | int %}
                                            <p class="product-price">
                                                <span class="discounted-price">{{ vip_price }}</span>
                                                <span class="original-price">{{ package.points }}</span>
                                            </p>
                                            <span class="vip-discount-badge"><i class="fas fa-crown"></i><span class="vip-level">{{ user.get('vip_level', 0) }}</span></span>
                                        {% elif package.discountRate and package.discountRate < 10 %}
                                            {% set original_price = (package.points / package.discountRate * 10) | round | int %}
                                            <p class="product-price">
                                                <span class="discounted-price">{{ package.points }}</span>
                                                <span class="original-price">{{ original_price }}</span>
                                            </p>
                                            <span class="discount-badge">{{ package.discountRate }}折</span>
                                        {% else %}
                                            <p class="product-price">{{ package.points }}</p>
                                        {% endif %}
                                        {% if package.isVIP %}
                                            <span class="vip-badge"><i class="fas fa-crown"></i> VIP专属</span>
                                        {% endif %}
                                    </div>
                                    {% if package.remark %}
                                        <p class="product-description">{{ package.remark }}</p>
                                    {% endif %}
                                    <div class="product-actions">
                                        <button class="copy-btn" data-name="{{ package.packageName }}">
                                            <i class="fas fa-copy"></i> 复制名称
                                        </button>
                                        {% if user %}
                                            {% if package.isVIP and not user.is_vip %}
                                            <button class="add-to-cart-btn disabled" title="需要VIP权限" disabled>
                                                <i class="fas fa-crown"></i>
                                            </button>
                                            {% else %}
                                                {% if user and user.get('vip_level', 0) > 0 %}
                                                    {% set discount_rate = (10 - user.get('vip_level', 0)) / 10 %}
                                                    {% set vip_price = (package.points * discount_rate) | round | int %}
                                                    <button class="add-to-cart-btn" data-id="{{ package.id }}" data-name="{{ package.packageName }}" data-price="{{ vip_price }}" data-original-price="{{ package.points }}">
                                                        <i class="fas fa-plus"></i>
                                                    </button>
                                                {% else %}
                                                    <button class="add-to-cart-btn" data-id="{{ package.id }}" data-name="{{ package.packageName }}" data-price="{{ package.points }}" data-original-price="{{ package.points }}">
                                                        <i class="fas fa-plus"></i>
                                                    </button>
                                                {% endif %}
                                            {% endif %}
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        {% endif %}
                    {% endfor %}
                </div>
            </section>
        {% endif %}

        {% if (display_category is defined and display_category == '传送点' and (current_items is not defined or not current_items or not current_items.coordinates))
            or (display_category is defined and display_category != '传送点' and (current_items is not defined or not current_items or not current_items.packages))
            or (display_category is defined and display_category == 'search' and (current_items is not defined or not current_items or (not current_items.coordinates and not current_items.packages) or (current_items.coordinates|length == 0 and current_items.packages|length == 0))) %}
            <div class="no-products">
                <h2>该分类暂无商品</h2>
                <p>请查看其他分类或稍后再试</p>
            </div>
        {% endif %}

        <!-- 分页 -->
        {% if total_pages is defined and total_pages > 1 %}
            <div class="pagination">
                {% if current_page is defined and current_page > 1 %}
                    {% if display_category == 'search' %}
                        <a href="?q={{ search_query }}&page={{ current_page - 1 }}{% if sort is defined and sort %}&sort={{ sort }}{% endif %}" class="page-prev"><i class="fas fa-chevron-left"></i></a>
                    {% elif active_category is defined %}
                        <a href="/category/{{ active_category }}?page={{ current_page - 1 }}{% if sort is defined and sort %}&sort={{ sort }}{% endif %}" class="page-prev"><i class="fas fa-chevron-left"></i></a>
                    {% else %}
                        <a href="?page={{ current_page - 1 }}{% if sort is defined and sort %}&sort={{ sort }}{% endif %}" class="page-prev"><i class="fas fa-chevron-left"></i></a>
                    {% endif %}
                {% endif %}

                {% for i in range(1, total_pages + 1) %}
                    {% if current_page is defined and i == current_page %}
                        {% if display_category == 'search' %}
                            <a href="?q={{ search_query }}&page={{ i }}{% if sort is defined and sort %}&sort={{ sort }}{% endif %}" class="page-num active">{{ i }}</a>
                        {% elif active_category is defined %}
                            <a href="/category/{{ active_category }}?page={{ i }}{% if sort is defined and sort %}&sort={{ sort }}{% endif %}" class="page-num active">{{ i }}</a>
                        {% else %}
                            <a href="?page={{ i }}{% if sort is defined and sort %}&sort={{ sort }}{% endif %}" class="page-num active">{{ i }}</a>
                        {% endif %}
                    {% elif current_page is defined and i >= current_page - 2 and i <= current_page + 2 %}
                        {% if display_category == 'search' %}
                            <a href="?q={{ search_query }}&page={{ i }}{% if sort is defined and sort %}&sort={{ sort }}{% endif %}" class="page-num">{{ i }}</a>
                        {% elif active_category is defined %}
                            <a href="/category/{{ active_category }}?page={{ i }}{% if sort is defined and sort %}&sort={{ sort }}{% endif %}" class="page-num">{{ i }}</a>
                        {% else %}
                            <a href="?page={{ i }}{% if sort is defined and sort %}&sort={{ sort }}{% endif %}" class="page-num">{{ i }}</a>
                        {% endif %}
                    {% elif i == 1 or i == total_pages %}
                        {% if display_category == 'search' %}
                            <a href="?q={{ search_query }}&page={{ i }}{% if sort is defined and sort %}&sort={{ sort }}{% endif %}" class="page-num">{{ i }}</a>
                        {% elif active_category is defined %}
                            <a href="/category/{{ active_category }}?page={{ i }}{% if sort is defined and sort %}&sort={{ sort }}{% endif %}" class="page-num">{{ i }}</a>
                        {% else %}
                            <a href="?page={{ i }}{% if sort is defined and sort %}&sort={{ sort }}{% endif %}" class="page-num">{{ i }}</a>
                        {% endif %}
                    {% elif current_page is defined and (i == current_page - 3 or i == current_page + 3) %}
                        <span class="ellipsis">...</span>
                    {% endif %}
                {% endfor %}

                {% if current_page is defined and current_page < total_pages %}
                    {% if display_category == 'search' %}
                        <a href="?q={{ search_query }}&page={{ current_page + 1 }}{% if sort is defined and sort %}&sort={{ sort }}{% endif %}" class="page-next"><i class="fas fa-chevron-right"></i></a>
                    {% elif active_category is defined %}
                        <a href="/category/{{ active_category }}?page={{ current_page + 1 }}{% if sort is defined and sort %}&sort={{ sort }}{% endif %}" class="page-next"><i class="fas fa-chevron-right"></i></a>
                    {% else %}
                        <a href="?page={{ current_page + 1 }}{% if sort is defined and sort %}&sort={{ sort }}{% endif %}" class="page-next"><i class="fas fa-chevron-right"></i></a>
                    {% endif %}
                {% endif %}
            </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // 视图切换
    const gridViewBtn = document.querySelector('.grid-view');
    const listViewBtn = document.querySelector('.list-view');
    const productsGrids = document.querySelectorAll('.products-grid');

    if (gridViewBtn && listViewBtn && productsGrids.length > 0) {
        gridViewBtn.addEventListener('click', function() {
            productsGrids.forEach(grid => grid.classList.remove('list-layout'));
            gridViewBtn.classList.add('active');
            listViewBtn.classList.remove('active');
        });

        listViewBtn.addEventListener('click', function() {
            productsGrids.forEach(grid => grid.classList.add('list-layout'));
            listViewBtn.classList.add('active');
            gridViewBtn.classList.remove('active');
        });
    }

    // 卡片点击也触发复制
    const productCards = document.querySelectorAll('.product-card');

    productCards.forEach(card => {
        card.addEventListener('click', function(e) {
            // 如果点击的是按钮或其子元素，不执行操作（让按钮事件处理）
            if (e.target.closest('.copy-btn') || e.target.closest('.add-to-cart-btn') || e.target.closest('.image-zoom-icon') || e.target.closest('.item-image')) {
                return;
            }

            const textToCopy = this.getAttribute('data-name');

            // 创建临时文本区域
            const textArea = document.createElement('textarea');
            textArea.value = textToCopy;
            document.body.appendChild(textArea);
            textArea.select();

            // 复制文本
            document.execCommand('copy');

            // 移除临时元素
            document.body.removeChild(textArea);

            // 显示提示
            const copyTooltip = document.getElementById('copyTooltip');
            copyTooltip.style.opacity = '1';
            copyTooltip.style.top = (this.getBoundingClientRect().top + 50) + 'px';
            copyTooltip.style.left = (this.getBoundingClientRect().left + this.offsetWidth / 2) + 'px';

            // 2秒后隐藏提示
            setTimeout(function() {
                copyTooltip.style.opacity = '0';
            }, 2000);
        });
    });
});
</script>
{% endblock %}
