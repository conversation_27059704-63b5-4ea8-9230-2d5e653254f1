<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>7788商城</title>
    <link rel="stylesheet" href="/static/css/style.css">
    <link rel="stylesheet" href="{{ url_for('static', filename='vendor/fontawesome/all.min.css') }}">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="main-nav">
        <div class="container nav-container">
            <div class="logo">
                <a href="/">
                    <i class="fas fa-gamepad"></i>
                    <span>7788商城</span>
                </a>
            </div>
            <ul class="nav-menu">
                <li class="nav-item"><a href="/home" class="nav-link "><i class="fas fa-home"></i> 主页</a></li>
                <li class="nav-item"><a href="/items" class="nav-link "><i class="fas fa-cube"></i> 物品代码</a></li>
                <li class="nav-item"><a href="/sponsor" class="nav-link "><i class="fas fa-heart"></i> 赞助</a></li>
                <li class="nav-item"><a href="/entertainment" class="nav-link active"><i class="fas fa-dice"></i> 娱乐</a></li>
                <li class="nav-item"><a href="/profile" class="nav-link "><i class="fas fa-user"></i> 个人信息</a></li>
            </ul>
            <div class="search-box">
                <form action="/search" method="GET">
                    <input type="text" name="q" placeholder="搜索物品..." >
                    <button type="submit"><i class="fas fa-search"></i></button>
                </form>
            </div>
        </div>
    </nav>
    
    <!-- 主内容区 -->
    <main class="container">
        
            <!-- 正在开发页面 -->
            <div class="under-construction-container">
                <div class="construction-content">
                    <div class="construction-icon">
                        <i class="fas fa-tools"></i>
                    </div>
                    <h1>娱乐功能</h1>
                    <div class="construction-message">
                        <p>游戏娱乐功能正在开发中，敬请期待！</p>
                    </div>
                    <div class="construction-progress">
                        <div class="progress">
                            <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 75%"></div>
                        </div>
                        <p class="progress-text">开发进度：75%</p>
                    </div>
                    <a href="/home" class="return-btn"><i class="fas fa-arrow-left"></i> 返回首页</a>
                </div>
            </div>
        
    </main>
    
    <!-- 复制成功提示 -->
    <div class="copy-tooltip" id="copyTooltip">已复制到剪贴板<br>请前往游戏内指令框输入</div>
    
    <!-- 页脚 -->
    <footer class="main-footer">
        <div class="container">
            <div class="footer-bottom">
                <p>&copy; 2025出其东门 | 保留所有权利</p>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script>
    document.addEventListener('DOMContentLoaded', function() {
        // 视图切换
        const gridViewBtn = document.querySelector('.grid-view');
        const listViewBtn = document.querySelector('.list-view');
        const productsGrids = document.querySelectorAll('.products-grid');
        
        if (gridViewBtn && listViewBtn && productsGrids.length > 0) {
            gridViewBtn.addEventListener('click', function() {
                productsGrids.forEach(grid => grid.classList.remove('list-layout'));
                gridViewBtn.classList.add('active');
                listViewBtn.classList.remove('active');
            });
            
            listViewBtn.addEventListener('click', function() {
                productsGrids.forEach(grid => grid.classList.add('list-layout'));
                listViewBtn.classList.add('active');
                gridViewBtn.classList.remove('active');
            });
        }
        
        // 复制功能
        const copyButtons = document.querySelectorAll('.copy-btn');
        const copyTooltip = document.getElementById('copyTooltip');
        
        copyButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                e.stopPropagation();
                const textToCopy = this.getAttribute('data-name');
                
                // 创建临时文本区域
                const textArea = document.createElement('textarea');
                textArea.value = textToCopy;
                document.body.appendChild(textArea);
                textArea.select();
                
                // 复制文本
                document.execCommand('copy');
                
                // 移除临时元素
                document.body.removeChild(textArea);
                
                // 显示提示
                copyTooltip.style.opacity = '1';
                copyTooltip.style.top = (this.getBoundingClientRect().top - 60) + 'px';
                copyTooltip.style.left = (this.getBoundingClientRect().left + this.offsetWidth / 2) + 'px';
                
                // 3秒后隐藏提示
                setTimeout(function() {
                    copyTooltip.style.opacity = '0';
                }, 2000);
            });
        });

        // 卡片点击也触发复制
        const productCards = document.querySelectorAll('.product-card');
        
        productCards.forEach(card => {
            card.addEventListener('click', function(e) {
                // 如果点击的是按钮或其子元素，不执行操作（让按钮事件处理）
                if (e.target.closest('.copy-btn')) {
                    return;
                }
                
                const textToCopy = this.getAttribute('data-name');
                
                // 创建临时文本区域
                const textArea = document.createElement('textarea');
                textArea.value = textToCopy;
                document.body.appendChild(textArea);
                textArea.select();
                
                // 复制文本
                document.execCommand('copy');
                
                // 移除临时元素
                document.body.removeChild(textArea);
                
                // 显示提示
                copyTooltip.style.opacity = '1';
                copyTooltip.style.top = (this.getBoundingClientRect().top + 50) + 'px';
                copyTooltip.style.left = (this.getBoundingClientRect().left + this.offsetWidth / 2) + 'px';
                
                // 2秒后隐藏提示
                setTimeout(function() {
                    copyTooltip.style.opacity = '0';
                }, 2000);
            });
        });
        
        // 个人信息页面标签切换
        const tabBtns = document.querySelectorAll('.tab-btn');
        const tabContents = document.querySelectorAll('.tab-content');
        
        if (tabBtns.length > 0) {
            tabBtns.forEach(btn => {
                btn.addEventListener('click', function() {
                    // 移除所有active类
                    tabBtns.forEach(b => b.classList.remove('active'));
                    tabContents.forEach(c => c.classList.remove('active'));
                    
                    // 添加active类到当前点击的按钮
                    this.classList.add('active');
                    
                    // 显示对应内容
                    const tabId = this.getAttribute('data-tab');
                    document.getElementById(tabId).classList.add('active');
                });
            });
        }
    });
    </script>
</body>
</html> 