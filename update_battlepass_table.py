#!/usr/bin/env python3
"""
为 battlepass_rewards 表添加 image_path 字段
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入数据库连接函数
from app import get_db_connection

def update_battlepass_table():
    """为 battlepass_rewards 表添加 image_path 字段"""
    conn = get_db_connection()
    if not conn:
        print("❌ 数据库连接失败")
        return False
    
    try:
        with conn.cursor() as cursor:
            # 检查字段是否已存在
            cursor.execute("SHOW COLUMNS FROM battlepass_rewards LIKE 'image_path'")
            result = cursor.fetchone()
            
            if not result:
                # 添加 image_path 字段
                sql = """ALTER TABLE battlepass_rewards 
                        ADD COLUMN image_path VARCHAR(500) DEFAULT '' 
                        AFTER reward_type"""
                cursor.execute(sql)
                conn.commit()
                print("✅ 成功添加 image_path 字段到 battlepass_rewards 表")
                return True
            else:
                print("ℹ️  image_path 字段已存在")
                return True
                
    except Exception as e:
        print(f"❌ 更新表结构失败: {e}")
        return False
    finally:
        conn.close()

if __name__ == "__main__":
    update_battlepass_table()
