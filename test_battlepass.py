#!/usr/bin/env python3
"""
通行证系统测试脚本
测试任务完成API接口
"""

import requests
import json
import time

# 配置
BASE_URL = "http://localhost:5000"
TEST_STEAM_ID = "76561198000000000"  # 测试用的Steam ID
TEST_QUEST_NAME = "击杀敌人"

def test_quest_completion():
    """测试任务完成API"""
    url = f"{BASE_URL}/api/quest/complete"
    
    data = {
        'steam_id': TEST_STEAM_ID,
        'quest_name': TEST_QUEST_NAME
    }
    
    headers = {
        'Content-Type': 'application/json'
    }
    
    try:
        print(f"发送任务完成请求...")
        print(f"URL: {url}")
        print(f"数据: {json.dumps(data, indent=2)}")
        
        response = requests.post(url, json=data, headers=headers)
        
        print(f"\n响应状态码: {response.status_code}")
        print(f"响应内容: {response.text}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print("\n✅ 任务完成成功!")
                data = result.get('data', {})
                print(f"获得经验: {data.get('exp_gained', 0)}")
                print(f"总经验: {data.get('total_exp', 0)}")
                print(f"等级: {data.get('old_level', 0)} -> {data.get('new_level', 0)}")
                if data.get('level_up'):
                    print("🎉 升级了!")
                    print(f"积分奖励: {data.get('points_reward', 0)}")
                    rewards = data.get('rewards_given', [])
                    if rewards:
                        print("物品奖励:")
                        for reward in rewards:
                            print(f"  - {reward['item_name']} x{reward['quantity']}")
                print(f"今日任务进度: {data.get('daily_quest_count', 0)}/{data.get('daily_quest_limit', 20)}")
            else:
                print(f"❌ 任务完成失败: {result.get('message', '未知错误')}")
        else:
            print(f"❌ 请求失败: {response.status_code}")
            
    except requests.exceptions.ConnectionError:
        print("❌ 连接失败，请确保服务器正在运行")
    except Exception as e:
        print(f"❌ 发生错误: {e}")

def test_multiple_quests():
    """测试多次任务完成"""
    print("\n" + "="*50)
    print("测试多次任务完成")
    print("="*50)
    
    for i in range(5):
        print(f"\n第 {i+1} 次任务完成:")
        test_quest_completion()
        time.sleep(1)  # 等待1秒

def test_battlepass_info():
    """测试获取通行证信息API"""
    url = f"{BASE_URL}/api/battlepass/info"
    
    try:
        print(f"\n获取通行证信息...")
        print(f"URL: {url}")
        
        response = requests.get(url)
        
        print(f"\n响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print("✅ 获取通行证信息成功!")
                data = result.get('data', {})
                battlepass = data.get('battlepass', {})
                config = data.get('config', {})
                
                print(f"当前等级: {battlepass.get('current_level', 1)}")
                print(f"总经验: {battlepass.get('total_exp', 0)}")
                print(f"今日任务: {battlepass.get('daily_quest_count', 0)}/{config.get('daily_quest_limit', 20)}")
                print(f"系统状态: {'启用' if config.get('enabled') else '禁用'}")
            else:
                print(f"❌ 获取信息失败: {result.get('message', '未知错误')}")
        else:
            print(f"❌ 请求失败: {response.status_code}")
            
    except requests.exceptions.ConnectionError:
        print("❌ 连接失败，请确保服务器正在运行")
    except Exception as e:
        print(f"❌ 发生错误: {e}")

if __name__ == "__main__":
    print("通行证系统测试")
    print("="*50)
    
    # 测试单次任务完成
    test_quest_completion()
    
    # 测试获取通行证信息
    test_battlepass_info()
    
    # 询问是否进行多次测试
    choice = input("\n是否进行多次任务完成测试? (y/n): ").lower().strip()
    if choice == 'y':
        test_multiple_quests()
    
    print("\n测试完成!")
