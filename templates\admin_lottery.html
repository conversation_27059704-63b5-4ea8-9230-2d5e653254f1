{% extends "base.html" %}

{% block title %}抽奖管理 - 7788商城{% endblock %}

{% block head_extra %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/admin.css') }}">
<link rel="stylesheet" href="{{ url_for('static', filename='css/lottery.css') }}">
<style>
    .prize-table {
        width: 100%;
        border-collapse: collapse;
        margin-top: 20px;
    }

    .prize-table th, .prize-table td {
        padding: 12px;
        text-align: left;
        border-bottom: 1px solid #333;
    }

    .prize-table th {
        background-color: #222;
        color: #fff;
    }

    .prize-table tr:hover {
        background-color: #2a2a2a;
    }

    .prize-actions {
        display: flex;
        gap: 8px;
    }

    .prize-actions button {
        padding: 6px 12px;
        border-radius: 4px;
        cursor: pointer;
        font-size: 14px;
    }

    .btn-edit {
        background-color: #2196F3;
        color: white;
        border: none;
    }

    .btn-delete {
        background-color: #F44336;
        color: white;
        border: none;
    }

    .prize-status {
        display: inline-block;
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 12px;
    }

    .status-active {
        background-color: #4CAF50;
        color: white;
    }

    .status-inactive {
        background-color: #9E9E9E;
        color: white;
    }

    .prize-image-preview {
        width: 50px;
        height: 50px;
        object-fit: cover;
        border-radius: 4px;
    }

    .admin-tabs {
        display: flex;
        margin-bottom: 20px;
        border-bottom: 1px solid #333;
    }

    .admin-tab {
        padding: 12px 20px;
        cursor: pointer;
        background-color: #222;
        color: #ccc;
        border: none;
        border-radius: 4px 4px 0 0;
        margin-right: 4px;
    }

    .admin-tab.active {
        background-color: #333;
        color: white;
    }

    .admin-tab-content {
        display: none;
    }

    .admin-tab-content.active {
        display: block;
    }

    .config-form {
        max-width: 600px;
        margin: 0 auto;
    }

    .config-form .form-group {
        margin-bottom: 20px;
    }

    .config-form label {
        display: block;
        margin-bottom: 8px;
        font-weight: bold;
    }

    .config-form input {
        width: 100%;
        padding: 10px;
        border-radius: 4px;
        border: 1px solid #444;
        background-color: #222;
        color: white;
    }

    .config-form button {
        padding: 10px 20px;
        background-color: #4CAF50;
        color: white;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        font-size: 16px;
    }

    .item-selector {
        margin-top: 20px;
    }

    .item-search {
        width: 100%;
        padding: 10px;
        margin-bottom: 15px;
        border-radius: 4px;
        border: 1px solid #444;
        background-color: #222;
        color: white;
    }

    .items-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
        gap: 20px;
        max-height: 600px;
        overflow-y: auto;
        padding: 15px;
        border: 1px solid #444;
        border-radius: 4px;
    }

    .item-card {
        background-color: #2a2a2a;
        border-radius: 8px;
        padding: 15px;
        cursor: pointer;
        transition: all 0.2s;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    }

    .item-card:hover {
        background-color: #333;
    }

    .item-card.selected {
        background-color: #1976D2;
    }

    .item-card img {
        width: 100%;
        height: 150px;
        object-fit: cover;
        border-radius: 6px;
        margin-bottom: 12px;
        border: 1px solid #444;
    }

    .item-card .item-name {
        font-size: 16px;
        text-align: center;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        margin-bottom: 5px;
        font-weight: bold;
    }
</style>
{% endblock %}

{% block content %}
<div class="admin-container">
    <div class="admin-header">
        <h1><i class="fas fa-gift"></i> 抽奖管理</h1>
        <p>管理抽奖奖品和配置</p>
    </div>

    <div class="admin-tabs">
        <button class="admin-tab active" data-tab="prizes-tab">奖品管理</button>
        <button class="admin-tab" data-tab="config-tab">抽奖配置</button>
    </div>

    <!-- 奖品管理标签页 -->
    <div id="prizes-tab" class="admin-tab-content active">
        <div class="admin-panel">
            <div class="admin-panel-header">
                <h2 class="admin-panel-title">奖品列表</h2>
                <button id="addPrizeBtn" class="admin-btn" style="background-color: #4CAF50; color: white; padding: 10px 20px; font-size: 16px;">
                    <i class="fas fa-plus"></i> 添加奖品
                </button>
            </div>
            <div class="admin-notice" style="margin-bottom: 20px; padding: 15px; background-color: #333; border-left: 4px solid #4CAF50; border-radius: 4px;">
                <p><i class="fas fa-info-circle"></i> 点击上方的“添加奖品”按钮，从物品列表中选择物品并设置中奖概率。</p>
            </div>

            <div class="prize-table-container">
                <table class="prize-table" id="prizeTable">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>图片</th>
                            <th>物品名称</th>
                            <th>物品ID</th>
                            <th>概率(%)</th>
                            <th>状态</th>
                            <th>创建时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="prizeTableBody">
                        {% for prize in prizes %}
                        <tr>
                            <td>{{ prize.id }}</td>
                            <td>
                                <img src="{{ prize.image_path or '/static/uploads/default-item.jpg' }}"
                                     alt="{{ prize.item_name }}"
                                     class="prize-image-preview">
                            </td>
                            <td>{{ prize.item_name }}</td>
                            <td>{{ prize.item_id }}</td>
                            <td>{{ "%.2f"|format(prize.probability) }}</td>
                            <td>
                                {% if prize.is_active %}
                                <span class="prize-status status-active">激活</span>
                                {% else %}
                                <span class="prize-status status-inactive">禁用</span>
                                {% endif %}
                            </td>
                            <td>{{ prize.created_at.strftime('%Y-%m-%d %H:%M') }}</td>
                            <td>
                                <div class="prize-actions">
                                    <button class="btn-edit" data-id="{{ prize.id }}">编辑</button>
                                    <button class="btn-delete" data-id="{{ prize.id }}">删除</button>
                                </div>
                            </td>
                        </tr>
                        {% else %}
                        <tr>
                            <td colspan="8" style="text-align: center;">暂无奖品数据</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- 抽奖配置标签页 -->
    <div id="config-tab" class="admin-tab-content">
        <div class="admin-panel">
            <div class="admin-panel-header">
                <h2 class="admin-panel-title">抽奖配置</h2>
            </div>

            <form id="configForm" class="config-form">
                <div class="form-group">
                    <label for="singleCost">单次抽奖花费 (积分)</label>
                    <input type="number" id="singleCost" name="single_cost" value="{{ config.single_cost }}" min="1" required>
                </div>

                <div class="form-group">
                    <label for="tenCost">十连抽花费 (积分)</label>
                    <input type="number" id="tenCost" name="ten_cost" value="{{ config.ten_cost }}" min="1" required>
                </div>

                <div class="form-group">
                    <label for="hundredCost">百连抽花费 (积分)</label>
                    <input type="number" id="hundredCost" name="hundred_cost" value="{{ config.hundred_cost }}" min="1" required>
                </div>

                <button type="submit" class="admin-btn">保存配置</button>
            </form>
        </div>
    </div>
</div>

<!-- 添加/编辑奖品模态框 -->
<div class="admin-modal" id="prizeModal" style="z-index: 1000;">
    <div class="modal-content" style="width: 95%; max-width: 1000px; max-height: 90vh;">
        <div class="modal-header" style="background-color: #4CAF50;">
            <h2 id="modalTitle" style="font-size: 24px;">添加奖品</h2>
            <span class="modal-close" style="font-size: 28px;">&times;</span>
        </div>
        <div class="modal-body" style="padding: 20px;">
            <form id="prizeForm">
                <input type="hidden" id="prizeId" name="id">

                <div style="display: flex; gap: 30px;">
                    <!-- 左侧表单区域 -->
                    <div style="flex: 1; max-width: 400px;">
                        <div style="background-color: #333; padding: 15px; border-radius: 8px; margin-bottom: 20px;">
                            <h3 style="margin-top: 0; color: #4CAF50; margin-bottom: 15px;">奖品信息</h3>

                            <div class="form-group">
                                <label for="itemName">物品名称</label>
                                <input type="text" id="itemName" name="item_name" required readonly style="font-size: 16px; padding: 10px;">
                            </div>

                            <div class="form-group">
                                <label for="itemId">物品ID</label>
                                <input type="text" id="itemId" name="item_id" required readonly style="font-size: 16px; padding: 10px;">
                            </div>

                            <div class="form-group">
                                <label for="probability" style="font-size: 18px; margin-bottom: 10px;">中奖概率 (%)</label>
                                <div style="margin-bottom: 15px;">
                                    <input type="number" id="probability" name="probability" step="0.0001" min="0.0001" max="100" required style="width: 100%; font-size: 24px; padding: 15px; height: 60px; background-color: #222; color: white; border: 2px solid #4CAF50;">
                                </div>
                                <div style="display: flex; flex-wrap: wrap; gap: 10px; margin-bottom: 15px;">
                                    <button type="button" class="quick-prob-btn" data-value="0.1" style="flex: 1; padding: 12px; font-size: 18px; background-color: #333; border: 1px solid #555; border-radius: 5px; cursor: pointer; min-width: 80px;">0.1%</button>
                                    <button type="button" class="quick-prob-btn" data-value="0.5" style="flex: 1; padding: 12px; font-size: 18px; background-color: #333; border: 1px solid #555; border-radius: 5px; cursor: pointer; min-width: 80px;">0.5%</button>
                                    <button type="button" class="quick-prob-btn" data-value="1" style="flex: 1; padding: 12px; font-size: 18px; background-color: #333; border: 1px solid #555; border-radius: 5px; cursor: pointer; min-width: 80px;">1%</button>
                                    <button type="button" class="quick-prob-btn" data-value="5" style="flex: 1; padding: 12px; font-size: 18px; background-color: #333; border: 1px solid #555; border-radius: 5px; cursor: pointer; min-width: 80px;">5%</button>
                                    <button type="button" class="quick-prob-btn" data-value="10" style="flex: 1; padding: 12px; font-size: 18px; background-color: #333; border: 1px solid #555; border-radius: 5px; cursor: pointer; min-width: 80px;">10%</button>
                                    <button type="button" class="quick-prob-btn" data-value="20" style="flex: 1; padding: 12px; font-size: 18px; background-color: #333; border: 1px solid #555; border-radius: 5px; cursor: pointer; min-width: 80px;">20%</button>
                                </div>
                                <div style="background-color: #222; padding: 10px; border-radius: 5px; margin-bottom: 10px;">
                                    <p style="margin: 0; font-size: 16px; color: #aaa;"><i class="fas fa-info-circle"></i> 输入中奖概率，例如：1.5 表示 1.5% 的概率</p>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="imagePath">图片路径</label>
                                <input type="text" id="imagePath" name="image_path" readonly style="font-size: 16px; padding: 10px;">
                            </div>

                            <div class="form-group" id="statusGroup" style="display: none;">
                                <label>状态</label>
                                <div class="radio-group" style="display: flex; gap: 20px;">
                                    <label style="display: flex; align-items: center; cursor: pointer;">
                                        <input type="radio" name="is_active" value="1" checked style="margin-right: 8px;"> 激活
                                    </label>
                                    <label style="display: flex; align-items: center; cursor: pointer;">
                                        <input type="radio" name="is_active" value="0" style="margin-right: 8px;"> 禁用
                                    </label>
                                </div>
                            </div>

                            <div class="form-actions" style="margin-top: 25px; display: flex; gap: 15px;">
                                <button type="submit" class="admin-btn" style="flex: 1; padding: 12px; font-size: 16px; background-color: #4CAF50;">保存奖品</button>
                                <button type="button" class="admin-btn cancel-btn" style="flex: 1; padding: 12px; font-size: 16px;">取消</button>
                            </div>
                        </div>
                    </div>

                    <!-- 右侧物品选择区域 -->
                    <div style="flex: 2;">
                        <div style="background-color: #333; padding: 15px; border-radius: 8px;">
                            <h3 style="margin-top: 0; color: #4CAF50; margin-bottom: 15px;">选择物品</h3>

                            <div style="margin-bottom: 15px; padding: 10px; background-color: #222; border-radius: 6px;">
                                <p style="font-size: 16px; margin: 0;"><i class="fas fa-info-circle"></i> 从下面的物品列表中选择一个物品添加到抽奖奖品池中。点击物品卡片选中物品。</p>
                            </div>

                            <input type="text" id="itemSearch" class="item-search" placeholder="搜索物品..." style="padding: 12px; font-size: 16px; margin-bottom: 15px; width: 100%; border-radius: 6px; border: 1px solid #444; background-color: #222; color: white;">

                            <div class="items-grid" id="itemsGrid" style="max-height: 500px; overflow-y: auto; border: 1px solid #444; padding: 20px; border-radius: 8px; background-color: #222;">
                                <!-- 物品将通过JavaScript动态加载 -->
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- 确认删除模态框 -->
<div class="admin-modal" id="confirmDeleteModal">
    <div class="modal-content">
        <div class="modal-header">
            <h2>确认删除</h2>
            <span class="modal-close">&times;</span>
        </div>
        <div class="modal-body">
            <p>确定要删除这个奖品吗？此操作无法撤销。</p>
            <div class="form-actions">
                <button id="confirmDeleteBtn" class="admin-btn danger-btn">删除</button>
                <button class="admin-btn cancel-btn">取消</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // 标签页切换
        const tabs = document.querySelectorAll('.admin-tab');
        const tabContents = document.querySelectorAll('.admin-tab-content');

        tabs.forEach(tab => {
            tab.addEventListener('click', function() {
                const tabId = this.getAttribute('data-tab');

                // 移除所有active类
                tabs.forEach(t => t.classList.remove('active'));
                tabContents.forEach(c => c.classList.remove('active'));

                // 添加active类到当前标签
                this.classList.add('active');
                document.getElementById(tabId).classList.add('active');
            });
        });

        // 模态框相关
        const prizeModal = document.getElementById('prizeModal');
        const confirmDeleteModal = document.getElementById('confirmDeleteModal');
        const modalCloses = document.querySelectorAll('.modal-close');
        const cancelBtns = document.querySelectorAll('.cancel-btn');

        // 关闭模态框
        function closeModal(modal) {
            modal.style.display = 'none';
        }

        modalCloses.forEach(close => {
            close.addEventListener('click', function() {
                closeModal(this.closest('.admin-modal'));
            });
        });

        cancelBtns.forEach(btn => {
            btn.addEventListener('click', function() {
                closeModal(this.closest('.admin-modal'));
            });
        });

        // 添加奖品按钮
        const addPrizeBtn = document.getElementById('addPrizeBtn');
        addPrizeBtn.addEventListener('click', function() {
            // 重置表单
            document.getElementById('prizeForm').reset();
            document.getElementById('prizeId').value = '';
            document.getElementById('modalTitle').textContent = '添加奖品';
            document.getElementById('statusGroup').style.display = 'none';

            // 显示模态框
            prizeModal.style.display = 'block';

            // 设置默认概率值
            document.getElementById('probability').value = '1';

            // 绑定快速概率按钮事件
            document.querySelectorAll('.quick-prob-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    const value = this.getAttribute('data-value');
                    document.getElementById('probability').value = value;
                });
            });

            // 加载物品列表
            loadItems();
        });

        // 编辑奖品按钮
        const editBtns = document.querySelectorAll('.btn-edit');
        editBtns.forEach(btn => {
            btn.addEventListener('click', function() {
                const prizeId = this.getAttribute('data-id');
                editPrize(prizeId);
            });
        });

        // 删除奖品按钮
        const deleteBtns = document.querySelectorAll('.btn-delete');
        deleteBtns.forEach(btn => {
            btn.addEventListener('click', function() {
                const prizeId = this.getAttribute('data-id');
                confirmDelete(prizeId);
            });
        });

        // 编辑奖品
        function editPrize(prizeId) {
            fetch(`/api/admin/lottery/prize/${prizeId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const prize = data.prize;

                        // 填充表单
                        document.getElementById('prizeId').value = prize.id;
                        document.getElementById('itemName').value = prize.item_name;
                        document.getElementById('itemId').value = prize.item_id;
                        document.getElementById('probability').value = prize.probability;
                        document.getElementById('imagePath').value = prize.image_path || '';

                        // 设置状态
                        const activeRadio = document.querySelector('input[name="is_active"][value="1"]');
                        const inactiveRadio = document.querySelector('input[name="is_active"][value="0"]');
                        if (prize.is_active) {
                            activeRadio.checked = true;
                        } else {
                            inactiveRadio.checked = true;
                        }

                        // 显示状态选项
                        document.getElementById('statusGroup').style.display = 'block';

                        // 设置标题
                        document.getElementById('modalTitle').textContent = '编辑奖品';

                        // 显示模态框
                        prizeModal.style.display = 'block';

                        // 绑定快速概率按钮事件
                        document.querySelectorAll('.quick-prob-btn').forEach(btn => {
                            btn.addEventListener('click', function() {
                                const value = this.getAttribute('data-value');
                                document.getElementById('probability').value = value;
                            });
                        });

                        // 加载物品列表
                        loadItems(prize.item_id);
                    } else {
                        alert('获取奖品信息失败: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('获取奖品信息失败，请稍后再试');
                });
        }

        // 确认删除
        let deleteId = null;

        function confirmDelete(prizeId) {
            deleteId = prizeId;
            confirmDeleteModal.style.display = 'block';
        }

        // 确认删除按钮
        const confirmDeleteBtn = document.getElementById('confirmDeleteBtn');
        confirmDeleteBtn.addEventListener('click', function() {
            if (deleteId) {
                deletePrize(deleteId);
            }
        });

        // 删除奖品
        function deletePrize(prizeId) {
            fetch(`/api/admin/lottery/prize/${prizeId}`, {
                method: 'DELETE'
            })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('奖品删除成功');
                        closeModal(confirmDeleteModal);
                        window.location.reload();
                    } else {
                        alert('删除奖品失败: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('删除奖品失败，请稍后再试');
                });
        }

        // 加载物品列表
        function loadItems(selectedItemId = null) {
            const itemsGrid = document.getElementById('itemsGrid');
            itemsGrid.innerHTML = '<p>加载中...</p>';

            // 获取所有物品数据
            let allItems;
            try {
                allItems = {{ all_items|tojson }};
                console.log('All items data:', allItems);
            } catch (e) {
                console.error('Error parsing all_items JSON:', e);
                itemsGrid.innerHTML = '<p>加载物品数据失败</p>';
                return;
            }

            // 如果没有有效的物品数据，尝试使用模拟数据
            if (!allItems || !allItems.packages || allItems.packages.length === 0) {
                console.warn('No valid items data found in all_items, using sample data');

                // 使用模拟数据代替
                allItems = {
                    'packages': [
                        {
                            'packageId': 'BP_Weapon_AK47',
                            'packageName': 'AK-47 武器',
                            'imagePath': '/static/uploads/weapons/ak47.jpg'
                        },
                        {
                            'packageId': 'BP_Weapon_M16',
                            'packageName': 'M16 武器',
                            'imagePath': '/static/uploads/weapons/m16.jpg'
                        },
                        {
                            'packageId': 'BP_Item_Backpack',
                            'packageName': '背包',
                            'imagePath': '/static/uploads/items/backpack.jpg'
                        },
                        {
                            'packageId': 'BP_Item_Medkit',
                            'packageName': '医疗包',
                            'imagePath': '/static/uploads/items/medkit.jpg'
                        },
                        {
                            'packageId': 'BP_Item_Scope',
                            'packageName': '狙击镜',
                            'imagePath': '/static/uploads/items/scope.jpg'
                        }
                    ]
                };
            }

            // 清空网格
            itemsGrid.innerHTML = '';

            // 填充物品卡片
            allItems.packages.forEach(item => {
                const card = document.createElement('div');
                card.className = 'item-card';
                if (selectedItemId && item.packageId === selectedItemId) {
                    card.classList.add('selected');
                }

                card.innerHTML = `
                    <div style="position: relative;">
                        <img src="${item.imagePath || '/static/uploads/default-item.jpg'}"
                             alt="${item.packageName}">
                        <div class="item-select-overlay" style="display: none; position: absolute; top: 0; left: 0; width: 100%; height: 100%; background-color: rgba(76, 175, 80, 0.7); justify-content: center; align-items: center;">
                            <i class="fas fa-check" style="color: white; font-size: 36px; text-shadow: 0 0 10px rgba(0,0,0,0.5);"></i>
                        </div>
                    </div>
                    <div class="item-name" style="font-size: 16px; font-weight: bold; margin: 10px 0 5px 0; color: #fff;">${item.packageName}</div>
                    <div class="item-id" style="font-size: 12px; color: #aaa; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;">${item.packageId}</div>
                    <button type="button" class="select-item-btn" style="width: 100%; margin-top: 10px; padding: 8px 0; background-color: #4CAF50; color: white; border: none; border-radius: 4px; cursor: pointer; display: none;">选择此物品</button>
                `;

                // 鼠标悬停效果
                card.addEventListener('mouseenter', function() {
                    if (!this.classList.contains('selected')) {
                        this.querySelector('.select-item-btn').style.display = 'block';
                    }
                });

                card.addEventListener('mouseleave', function() {
                    if (!this.classList.contains('selected')) {
                        this.querySelector('.select-item-btn').style.display = 'none';
                    }
                });

                // 点击卡片选中物品
                card.addEventListener('click', function() {
                    selectItem(this, item);
                });

                // 点击选择按钮
                const selectBtn = card.querySelector('.select-item-btn');
                selectBtn.addEventListener('click', function(e) {
                    e.stopPropagation(); // 阻止事件冒泡
                    selectItem(card, item);
                });

                // 选择物品函数
                function selectItem(cardElement, itemData) {
                    // 移除其他卡片的选中状态
                    document.querySelectorAll('.item-card').forEach(c => {
                        c.classList.remove('selected');
                        c.querySelector('.item-select-overlay').style.display = 'none';
                        c.querySelector('.select-item-btn').style.display = 'none';
                    });

                    // 添加选中状态
                    cardElement.classList.add('selected');
                    const overlay = cardElement.querySelector('.item-select-overlay');
                    overlay.style.display = 'flex';
                    cardElement.querySelector('.select-item-btn').style.display = 'none';

                    // 填充表单
                    document.getElementById('itemName').value = itemData.packageName;
                    document.getElementById('itemId').value = itemData.packageId;
                    document.getElementById('imagePath').value = itemData.imagePath || '';

                    // 自动滚动到概率输入框
                    document.getElementById('probability').focus();

                    // 显示选中提示
                    const selectedInfo = document.createElement('div');
                    selectedInfo.className = 'selected-info';
                    selectedInfo.style.position = 'fixed';
                    selectedInfo.style.bottom = '20px';
                    selectedInfo.style.right = '20px';
                    selectedInfo.style.backgroundColor = '#4CAF50';
                    selectedInfo.style.color = 'white';
                    selectedInfo.style.padding = '15px 20px';
                    selectedInfo.style.borderRadius = '8px';
                    selectedInfo.style.boxShadow = '0 4px 8px rgba(0,0,0,0.2)';
                    selectedInfo.style.zIndex = '2000';
                    selectedInfo.style.animation = 'fadeInOut 3s forwards';
                    selectedInfo.innerHTML = `<i class="fas fa-check-circle"></i> 已选择物品: ${itemData.packageName}`;

                    // 添加动画样式
                    const style = document.createElement('style');
                    style.textContent = `
                        @keyframes fadeInOut {
                            0% { opacity: 0; transform: translateY(20px); }
                            10% { opacity: 1; transform: translateY(0); }
                            90% { opacity: 1; transform: translateY(0); }
                            100% { opacity: 0; transform: translateY(-20px); }
                        }
                    `;
                    document.head.appendChild(style);

                    document.body.appendChild(selectedInfo);

                    // 3秒后移除提示
                    setTimeout(() => {
                        document.body.removeChild(selectedInfo);
                    }, 3000);
                }

                itemsGrid.appendChild(card);
            });

            // 物品搜索功能
            const itemSearch = document.getElementById('itemSearch');
            itemSearch.addEventListener('input', function() {
                const searchTerm = this.value.toLowerCase();

                document.querySelectorAll('.item-card').forEach(card => {
                    const itemName = card.querySelector('.item-name').textContent.toLowerCase();

                    if (itemName.includes(searchTerm)) {
                        card.style.display = 'block';
                    } else {
                        card.style.display = 'none';
                    }
                });
            });
        }

        // 提交奖品表单
        const prizeForm = document.getElementById('prizeForm');
        prizeForm.addEventListener('submit', function(e) {
            e.preventDefault();

            const prizeId = document.getElementById('prizeId').value;
            const itemName = document.getElementById('itemName').value;
            const itemId = document.getElementById('itemId').value;
            const probability = document.getElementById('probability').value;
            const imagePath = document.getElementById('imagePath').value;

            if (!itemName || !itemId || !probability) {
                alert('请填写完整的奖品信息');
                return;
            }

            // 构建请求数据
            const formData = {
                item_name: itemName,
                item_id: itemId,
                probability: parseFloat(probability),
                image_path: imagePath
            };

            // 如果是编辑模式，添加状态字段
            if (prizeId) {
                const isActive = document.querySelector('input[name="is_active"]:checked').value === '1';
                formData.is_active = isActive;

                // 发送更新请求
                fetch(`/api/admin/lottery/prize/${prizeId}`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(formData)
                })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            alert('奖品更新成功');
                            closeModal(prizeModal);
                            window.location.reload();
                        } else {
                            alert('更新奖品失败: ' + data.message);
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        alert('更新奖品失败，请稍后再试');
                    });
            } else {
                // 发送添加请求
                fetch('/api/admin/lottery/prize', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(formData)
                })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            alert('奖品添加成功');
                            closeModal(prizeModal);
                            window.location.reload();
                        } else {
                            alert('添加奖品失败: ' + data.message);
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        alert('添加奖品失败，请稍后再试');
                    });
            }
        });

        // 提交配置表单
        const configForm = document.getElementById('configForm');
        configForm.addEventListener('submit', function(e) {
            e.preventDefault();

            const singleCost = parseInt(document.getElementById('singleCost').value);
            const tenCost = parseInt(document.getElementById('tenCost').value);
            const hundredCost = parseInt(document.getElementById('hundredCost').value);

            if (isNaN(singleCost) || isNaN(tenCost) || isNaN(hundredCost) ||
                singleCost < 1 || tenCost < 1 || hundredCost < 1) {
                alert('请输入有效的积分花费');
                return;
            }

            // 构建请求数据
            const formData = {
                single_cost: singleCost,
                ten_cost: tenCost,
                hundred_cost: hundredCost
            };

            // 发送更新请求
            fetch('/api/admin/lottery/config', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(formData)
            })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('配置更新成功');
                    } else {
                        alert('更新配置失败: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('更新配置失败，请稍后再试');
                });
        });
    });
</script>
{% endblock %}
