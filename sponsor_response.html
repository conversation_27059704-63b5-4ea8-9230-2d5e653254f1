<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>7788商城</title>
    <link rel="stylesheet" href="/static/css/style.css">
    <link rel="stylesheet" href="{{ url_for('static', filename='vendor/fontawesome/all.min.css') }}">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="main-nav">
        <div class="container nav-container">
            <div class="logo">
                <a href="/">
                    <i class="fas fa-gamepad"></i>
                    <span>7788商城</span>
                </a>
            </div>
            <ul class="nav-menu">
                <li class="nav-item"><a href="/home" class="nav-link "><i class="fas fa-home"></i> 主页</a></li>
                <li class="nav-item"><a href="/items" class="nav-link "><i class="fas fa-cube"></i> 物品代码</a></li>
                <li class="nav-item"><a href="/sponsor" class="nav-link active"><i class="fas fa-heart"></i> 赞助</a></li>
                <li class="nav-item"><a href="/entertainment" class="nav-link "><i class="fas fa-dice"></i> 娱乐</a></li>
                <li class="nav-item"><a href="/profile" class="nav-link "><i class="fas fa-user"></i> 个人信息</a></li>
            </ul>
            <div class="search-box">
                <form action="/search" method="GET">
                    <input type="text" name="q" placeholder="搜索物品..." >
                    <button type="submit"><i class="fas fa-search"></i></button>
                </form>
            </div>
        </div>
    </nav>
    
    <!-- 主内容区 -->
    <main class="container">
        
            <!-- 赞助页面内容 -->
            <div class="sponsor-container">
                <div class="sponsor-header">
                    <h1>支持我们的发展</h1>
                    <p>您的赞助将帮助我们提供更好的服务和更多的功能</p>
                </div>
                
                <div class="sponsor-options">
                    <div class="sponsor-tier">
                        <div class="tier-header">
                            <h2>普通赞助</h2>
                            <div class="tier-price">¥10</div>
                        </div>
                        <div class="tier-benefits">
                            <ul>
                                <li><i class="fas fa-check"></i> 获得赞助者标识</li>
                                <li><i class="fas fa-check"></i> 优先客服支持</li>
                                <li><i class="fas fa-check"></i> 每日礼包 x1</li>
                            </ul>
                        </div>
                        <a href="#" class="sponsor-btn">立即赞助</a>
                    </div>
                    
                    <div class="sponsor-tier featured">
                        <div class="tier-tag">推荐</div>
                        <div class="tier-header">
                            <h2>高级赞助</h2>
                            <div class="tier-price">¥50</div>
                        </div>
                        <div class="tier-benefits">
                            <ul>
                                <li><i class="fas fa-check"></i> 获得高级赞助者标识</li>
                                <li><i class="fas fa-check"></i> VIP客服支持</li>
                                <li><i class="fas fa-check"></i> 每日礼包 x3</li>
                                <li><i class="fas fa-check"></i> 专属武器皮肤</li>
                                <li><i class="fas fa-check"></i> 游戏内特殊称号</li>
                            </ul>
                        </div>
                        <a href="#" class="sponsor-btn">立即赞助</a>
                    </div>
                    
                    <div class="sponsor-tier">
                        <div class="tier-header">
                            <h2>至尊赞助</h2>
                            <div class="tier-price">¥100</div>
                        </div>
                        <div class="tier-benefits">
                            <ul>
                                <li><i class="fas fa-check"></i> 获得至尊赞助者标识</li>
                                <li><i class="fas fa-check"></i> 24小时专属客服</li>
                                <li><i class="fas fa-check"></i> 每日礼包 x5</li>
                                <li><i class="fas fa-check"></i> 全套专属皮肤</li>
                                <li><i class="fas fa-check"></i> 游戏内特殊称号</li>
                                <li><i class="fas fa-check"></i> 专属服务器特权</li>
                            </ul>
                        </div>
                        <a href="#" class="sponsor-btn">立即赞助</a>
                    </div>
                </div>
                
                <div class="sponsor-qrcode">
                    <h2>扫码赞助</h2>
                    <div class="qrcode-images">
                        <div class="qrcode">
                            <img src="/static/img/wechat-pay.jpg" alt="微信支付">
                            <p>微信支付</p>
                        </div>
                        <div class="qrcode">
                            <img src="/static/img/alipay.jpg" alt="支付宝">
                            <p>支付宝</p>
                        </div>
                    </div>
                    <p class="sponsor-note">赞助后请联系客服QQ: 123456789 获取赞助特权</p>
                </div>
            </div>
        
    </main>
    
    <!-- 复制成功提示 -->
    <div class="copy-tooltip" id="copyTooltip">已复制到剪贴板<br>请前往游戏内指令框输入</div>
    
    <!-- 页脚 -->
    <footer class="main-footer">
        <div class="container">
            <div class="footer-bottom">
                <p>&copy; 2025 出其东门 | 保留所有权利</p>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script>
    document.addEventListener('DOMContentLoaded', function() {
        // 视图切换
        const gridViewBtn = document.querySelector('.grid-view');
        const listViewBtn = document.querySelector('.list-view');
        const productsGrids = document.querySelectorAll('.products-grid');
        
        if (gridViewBtn && listViewBtn && productsGrids.length > 0) {
            gridViewBtn.addEventListener('click', function() {
                productsGrids.forEach(grid => grid.classList.remove('list-layout'));
                gridViewBtn.classList.add('active');
                listViewBtn.classList.remove('active');
            });
            
            listViewBtn.addEventListener('click', function() {
                productsGrids.forEach(grid => grid.classList.add('list-layout'));
                listViewBtn.classList.add('active');
                gridViewBtn.classList.remove('active');
            });
        }
        
        // 复制功能
        const copyButtons = document.querySelectorAll('.copy-btn');
        const copyTooltip = document.getElementById('copyTooltip');
        
        copyButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                e.stopPropagation();
                const textToCopy = this.getAttribute('data-name');
                
                // 创建临时文本区域
                const textArea = document.createElement('textarea');
                textArea.value = textToCopy;
                document.body.appendChild(textArea);
                textArea.select();
                
                // 复制文本
                document.execCommand('copy');
                
                // 移除临时元素
                document.body.removeChild(textArea);
                
                // 显示提示
                copyTooltip.style.opacity = '1';
                copyTooltip.style.top = (this.getBoundingClientRect().top - 60) + 'px';
                copyTooltip.style.left = (this.getBoundingClientRect().left + this.offsetWidth / 2) + 'px';
                
                // 3秒后隐藏提示
                setTimeout(function() {
                    copyTooltip.style.opacity = '0';
                }, 2000);
            });
        });

        // 卡片点击也触发复制
        const productCards = document.querySelectorAll('.product-card');
        
        productCards.forEach(card => {
            card.addEventListener('click', function(e) {
                // 如果点击的是按钮或其子元素，不执行操作（让按钮事件处理）
                if (e.target.closest('.copy-btn')) {
                    return;
                }
                
                const textToCopy = this.getAttribute('data-name');
                
                // 创建临时文本区域
                const textArea = document.createElement('textarea');
                textArea.value = textToCopy;
                document.body.appendChild(textArea);
                textArea.select();
                
                // 复制文本
                document.execCommand('copy');
                
                // 移除临时元素
                document.body.removeChild(textArea);
                
                // 显示提示
                copyTooltip.style.opacity = '1';
                copyTooltip.style.top = (this.getBoundingClientRect().top + 50) + 'px';
                copyTooltip.style.left = (this.getBoundingClientRect().left + this.offsetWidth / 2) + 'px';
                
                // 2秒后隐藏提示
                setTimeout(function() {
                    copyTooltip.style.opacity = '0';
                }, 2000);
            });
        });
        
        // 个人信息页面标签切换
        const tabBtns = document.querySelectorAll('.tab-btn');
        const tabContents = document.querySelectorAll('.tab-content');
        
        if (tabBtns.length > 0) {
            tabBtns.forEach(btn => {
                btn.addEventListener('click', function() {
                    // 移除所有active类
                    tabBtns.forEach(b => b.classList.remove('active'));
                    tabContents.forEach(c => c.classList.remove('active'));
                    
                    // 添加active类到当前点击的按钮
                    this.classList.add('active');
                    
                    // 显示对应内容
                    const tabId = this.getAttribute('data-tab');
                    document.getElementById(tabId).classList.add('active');
                });
            });
        }
    });
    </script>
</body>
</html> 