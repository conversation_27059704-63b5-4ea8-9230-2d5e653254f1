/* 全局样式 */
:root {
    --primary-color: #00a8ff;
    --secondary-color: #e0e0e0;
    --dark-bg: #121212;
    --card-bg: #1e1e1e;
    --card-hover: #2d2d2d;
    --text-primary: #ffffff;
    --text-secondary: #b3b3b3;
    --border-radius: 8px;
    --box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
    --transition: all 0.3s ease;
}

/* 深色主题全局样式 */
html.dark-theme,
body.dark-theme {
    background-color: var(--dark-bg) !important;
    color: var(--text-primary);
}

/* 确保内容区域也使用深色主题 */
.dark-theme .container,
.dark-theme .content,
.dark-theme .main-content,
.dark-theme main,
.dark-theme section,
.dark-theme .wrapper,
.dark-theme .page-wrapper,
.dark-theme .card,
.dark-theme .box,
.dark-theme .panel {
    background-color: var(--dark-bg) !important;
    color: var(--text-primary);
}

/* 确保弹出窗口也使用深色主题 */
.dark-theme .modal,
.dark-theme .popup,
.dark-theme .dropdown-menu {
    background-color: var(--card-bg) !important;
    color: var(--text-primary);
}

/* 添加更多元素选择器以确保深色主题覆盖所有可能有白色背景的元素 */
.dark-theme div,
.dark-theme article,
.dark-theme aside,
.dark-theme header,
.dark-theme footer,
.dark-theme nav,
.dark-theme form,
.dark-theme table,
.dark-theme tbody,
.dark-theme thead,
.dark-theme tr,
.dark-theme td,
.dark-theme th,
.dark-theme ul,
.dark-theme ol,
.dark-theme li,
.dark-theme p,
.dark-theme span,
.dark-theme input:not([type="submit"]):not([type="button"]),
.dark-theme select,
.dark-theme textarea,
.dark-theme button {
    background-color: inherit;
}

/* 确保默认使用深色主题 */
html, body {
    width: 100%;
    height: 100%;
    margin: 0;
    padding: 0;
    background-color: var(--dark-bg) !important;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Microsoft YaHei', 'PingFang SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    line-height: 1.5;
    color: var(--text-primary);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 20px;
    background-color: var(--dark-bg);
}

main {
    background-color: var(--dark-bg);
}

.tab-pane {
    background-color: transparent;
}

.profile-detail-card {
    background-color: #1e1e1e;
}

.col-md-9 {
    background-color: transparent;
}

a {
    color: var(--primary-color);
    text-decoration: none;
    transition: var(--transition);
}

a:hover {
    opacity: 0.8;
}

button {
    cursor: pointer;
    font-family: inherit;
}

ul {
    list-style: none;
}

img {
    max-width: 100%;
    height: auto;
}

/* 导航栏 */
.main-nav {
    padding: 10px 0;
    background: rgba(15, 15, 20, 0.95);
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    position: sticky;
    top: 0;
    z-index: 1000;
}

.nav-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: nowrap;
}

.logo a {
    display: flex;
    align-items: center;
    color: var(--text-primary);
    font-weight: 600;
    font-size: 1.2rem;
    white-space: nowrap;
}

.logo i {
    margin-right: 8px;
    color: var(--primary-color);
    font-size: 1.4rem;
}

.nav-menu {
    display: flex;
    gap: 20px;
    margin-bottom: 0;
    flex-wrap: nowrap;
    flex: 1;
    justify-content: center;
    margin-left: 20px;
    margin-right: 20px;
}

.nav-link {
    display: flex;
    align-items: center;
    color: var(--text-primary);
    font-size: 0.95rem;
    font-weight: 500;
    padding: 8px 10px;
    position: relative;
    border-radius: 4px;
    white-space: nowrap;
}

.nav-link i {
    margin-right: 6px;
    font-size: 1.1rem;
}

.nav-link.active {
    color: var(--primary-color);
    background-color: rgba(0, 168, 255, 0.1);
}

/* 搜索框 */
.search-box {
    position: relative;
    margin-right: 12px;
    flex-shrink: 0;
}

.search-box input {
    background: rgba(30, 30, 35, 0.8);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 4px;
    padding: 6px 30px 6px 10px;
    color: #f5f5f5;
    width: 160px;
    font-size: 0.9rem;
    transition: all 0.2s ease;
}

.search-box input:focus {
    background: rgba(40, 40, 45, 0.9);
    border-color: rgba(0, 168, 255, 0.5);
    width: 200px;
    outline: none;
}

.search-box button {
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
    background: transparent;
    border: none;
    color: #00a8ff;
    font-size: 0.9rem;
    cursor: pointer;
}

/* 物品代码页面搜索框 */
.items-search-box {
    position: relative;
    max-width: 600px;
    margin: 0 auto 20px;
    padding: 15px;
    background-color: #1a1a1a;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    border: 1px solid #333;
}

.items-search-box form {
    display: flex;
    position: relative;
}

.items-search-box input {
    flex: 1;
    background: rgba(30, 30, 35, 0.8);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 25px;
    padding: 10px 20px;
    color: #f5f5f5;
    font-size: 1rem;
    transition: all 0.2s ease;
}

.items-search-box input:focus {
    background: rgba(40, 40, 45, 0.9);
    border-color: rgba(0, 168, 255, 0.5);
    box-shadow: 0 0 0 2px rgba(0, 168, 255, 0.2);
    outline: none;
}

.items-search-box button {
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    background: transparent;
    border: none;
    color: #00a8ff;
    font-size: 1rem;
    cursor: pointer;
}

.items-search-box button:hover {
    color: #0095e0;
}

/* 导航栏右侧样式 */
.nav-right {
    display: flex;
    align-items: center;
    flex-shrink: 0;
    min-width: 280px;
}

/* 导航栏积分显示 */
.nav-points {
    display: flex;
    align-items: center;
    background: linear-gradient(135deg, #e8b923, #ffd700);
    padding: 4px 10px;
    border-radius: 15px;
    margin-right: 12px;
    border: none;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
    transition: all 0.2s ease;
    flex-shrink: 0;
    position: relative;
}

.nav-points:hover {
    box-shadow: 0 3px 8px rgba(232, 185, 35, 0.4);
    transform: translateY(-1px);
}

.nav-points i {
    color: #472e00;
    margin-right: 5px;
    font-size: 0.9rem;
}

.nav-points span {
    color: #472e00;
    font-weight: bold;
    font-size: 0.9rem;
    background: transparent;
}

.auth-links {
    display: flex;
    align-items: center;
    margin-left: 12px;
    flex-shrink: 0;
}

.auth-link {
    color: var(--text-primary);
    padding: 8px 12px;
    font-weight: 500;
    font-size: 14px;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.auth-link:hover, .auth-link.active {
    color: var(--primary-color);
    background-color: rgba(0, 123, 255, 0.1);
    text-decoration: none;
}

/* 登录/注册按钮样式 */
.auth-button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 6px 14px;
    font-weight: 500;
    font-size: 14px;
    border-radius: 4px;
    transition: all 0.3s ease;
    margin-left: 10px;
    text-decoration: none;
}

.auth-button i {
    margin-right: 5px;
}

.login-btn {
    background-color: var(--primary-color);
    color: #000;
    border: none;
}

.login-btn:hover {
    background-color: #0095e0;
    color: #000;
    text-decoration: none;
    box-shadow: 0 2px 5px rgba(0, 168, 255, 0.3);
}

.user-dropdown {
    position: relative;
}

.dropdown-toggle {
    display: flex;
    align-items: center;
    background: rgba(30, 30, 35, 0.8);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 4px;
    padding: 6px 12px;
    color: #f5f5f5;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.2s ease;
}

.dropdown-toggle:hover {
    background: rgba(40, 40, 45, 0.9);
}

.dropdown-toggle i {
    margin-right: 5px;
    font-size: 16px;
}

.dropdown-menu {
    display: none;
    position: absolute;
    top: 100%;
    right: 0;
    background-color: rgba(25, 25, 30, 0.95);
    border-radius: 4px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    z-index: 1000;
    min-width: 150px;
    padding: 8px 0;
    margin-top: 5px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(10px);
    transition: all 0.2s ease;
}

.dropdown-menu.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-menu a {
    display: block;
    padding: 8px 15px;
    color: #f5f5f5;
    text-decoration: none;
    font-size: 0.9rem;
    transition: all 0.15s ease;
}

.dropdown-menu a:hover {
    background-color: rgba(0, 168, 255, 0.1);
    color: #00a8ff;
}

.dropdown-menu a i {
    margin-right: 8px;
    color: #00a8ff;
}

/* 分类选项卡 */
.category-tabs {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin: 20px 0;
    padding: 15px;
    background-color: #1a1a1a;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    border: 1px solid #333;
}

.category-tabs .tab {
    display: flex;
    align-items: center;
    padding: 8px 15px;
    border-radius: 20px;
    font-size: 0.9rem;
    color: var(--text-primary);
    background-color: #2a2a2a;
    transition: var(--transition);
    border: 1px solid #444;
}

.category-tabs .tab i {
    margin-right: 6px;
    font-size: 1rem;
}

.category-tabs .tab.active {
    background-color: var(--primary-color);
    color: #000;
    border-color: var(--primary-color);
    font-weight: 600;
}

.category-tabs .tab:hover:not(.active) {
    background-color: #333;
    border-color: #555;
}

/* 过滤栏 */
.filter-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 15px;
    background-color: #1a1a1a;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    border: 1px solid #333;
}

.sort-options {
    display: flex;
    align-items: center;
    gap: 16px;
}

.sort-options span {
    color: var(--text-secondary);
    font-size: 0.9rem;
    display: flex;
    align-items: center;
}

.sort-options span i {
    margin-right: 5px;
}

.sort-options a {
    color: var(--text-primary);
    font-size: 0.9rem;
    padding: 6px 12px;
    border-radius: 14px;
    background: transparent;
    transition: var(--transition);
    display: flex;
    align-items: center;
}

.sort-options a i {
    margin-left: 4px;
    font-size: 0.8rem;
}

.sort-options a.active, .sort-options a:hover {
    background-color: #2a2a2a;
    color: var(--primary-color);
}

.view-options {
    display: flex;
    gap: 8px;
}

.view-options button {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    background: #2a2a2a;
    border: 1px solid #444;
    color: var(--text-secondary);
    font-size: 1rem;
    transition: var(--transition);
}

.view-options button.active, .view-options button:hover {
    background-color: var(--primary-color);
    color: #000;
    border-color: var(--primary-color);
}

/* 产品部分 */
.product-section {
    margin-bottom: 30px;
}

.section-title {
    font-size: 1.4rem;
    font-weight: 600;
    margin-bottom: 20px;
    color: var(--text-primary);
    position: relative;
    display: inline-flex;
    align-items: center;
    background-color: #1a1a1a;
    padding: 10px 20px;
    border-radius: 20px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
    border: 1px solid #333;
}

.section-title i {
    margin-right: 8px;
    font-size: 1.1rem;
    color: var(--primary-color);
}

/* 商品网格 */
.products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 15px;
}

.coordinate-grid {
    grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
}

.products-grid.list-layout {
    grid-template-columns: 1fr;
}

.products-grid.list-layout .product-card {
    display: flex;
    align-items: center;
}

.products-grid.list-layout .product-image {
    width: 120px;
    min-width: 120px;
    height: 120px;
}

.products-grid.list-layout .product-info {
    padding: 10px 15px;
    flex-grow: 1;
}

.products-grid.list-layout .title-row {
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
}

.products-grid.list-layout .product-title {
    margin-bottom: 0;
}

/* 商品卡片 */
.product-card {
    background-color: var(--card-bg);
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    position: relative;
    cursor: pointer;
    border: 1px solid #333;
}

.product-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.4);
    border-color: var(--primary-color);
    background-color: var(--card-hover);
}

.product-card:active {
    transform: translateY(0);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
}

/* 传送点卡片特殊样式 */
.coordinate-card {
    background-color: rgba(0, 168, 255, 0.1);
    border-left: 3px solid var(--primary-color);
}

.coordinate-card .card-icon {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: var(--primary-color);
    color: #000;
    border-radius: 50%;
    margin-bottom: 10px;
}

.coordinate-card .card-icon i {
    font-size: 1.2rem;
}

/* 商品图片 */
.product-image {
    position: relative;
    overflow: hidden;
    border-radius: 8px 8px 0 0;
    height: 120px !important; /* 强制设置高度并使用!important确保优先级 */
    background-color: #2a2a2a;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
}

.product-image img {
    max-width: 90%;
    max-height: 90%;
    transition: transform 0.3s ease;
    object-fit: contain;
}

.product-image:hover img {
    transform: scale(1.05);
}

.image-zoom-icon {
    position: absolute;
    bottom: 10px;
    right: 10px;
    background-color: rgba(0, 0, 0, 0.6);
    color: white;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s;
    z-index: 2;
}

.product-image:hover .image-zoom-icon {
    opacity: 1;
}

/* 商品信息 */
.product-info {
    padding: 12px;
}

.title-row {
    display: flex;
    flex-direction: column;
    margin-bottom: 8px;
}

.product-title {
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 5px;
    color: var(--text-primary);
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.product-category {
    display: inline-flex;
    align-items: center;
    font-size: 0.75rem;
    font-weight: 500;
    color: var(--text-secondary);
    background-color: rgba(255, 255, 255, 0.05);
    padding: 3px 8px;
    border-radius: 10px;
    margin-top: 5px;
    border: 1px solid #444;
}

.product-category i {
    margin-right: 4px;
    font-size: 0.8rem;
}

.product-price {
    font-size: 1.2rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 8px;
    display: flex;
    align-items: center;
}

.product-price:after {
    content: '点';
    font-size: 0.8rem;
    font-weight: normal;
    margin-left: 2px;
    opacity: 0.8;
}

.product-description {
    color: var(--text-secondary);
    font-size: 0.85rem;
    line-height: 1.4;
    margin-bottom: 10px;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.copy-btn {
    background-color: var(--primary-color);
    color: #000;
    box-shadow: 0 2px 4px rgba(0, 168, 255, 0.3);
    padding: 8px 12px;
    letter-spacing: 0.5px;
    text-transform: uppercase;
    font-size: 0.85rem;
}

.copy-btn:hover {
    background-color: #0095df;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 168, 255, 0.4);
}

.use-btn {
    background-color: #4caf50;
    color: white;
    font-size: 0.85rem;
}

/* 复制提示 */
.copy-tooltip {
    position: fixed;
    background-color: rgba(0, 0, 0, 0.9);
    color: white;
    padding: 12px 20px;
    border-radius: 8px;
    font-size: 0.95rem;
    z-index: 1100;
    pointer-events: none;
    transform: translateX(-50%);
    opacity: 0;
    transition: opacity 0.3s ease;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
    border: 1px solid var(--primary-color);
    max-width: 300px;
    text-align: center;
    line-height: 1.4;
}

.copy-tooltip::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    border-width: 10px 10px 0;
    border-style: solid;
    border-color: rgba(0, 0, 0, 0.9) transparent transparent;
}

/* 分页 */
.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 8px;
    margin: 30px 0;
}

.page-prev, .page-next {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 40px;
    height: 40px;
    font-size: 1rem;
    color: var(--text-primary);
    background-color: #252525;
    border-radius: 50%;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
    border: 1px solid #444;
}

.page-num {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 40px;
    height: 40px;
    font-size: 0.9rem;
    font-weight: 500;
    color: var(--text-primary);
    background-color: #252525;
    border-radius: 50%;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
    border: 1px solid #444;
}

.page-num.active {
    background-color: var(--primary-color);
    color: #000;
    border-color: var(--primary-color);
}

.ellipsis {
    color: var(--text-secondary);
    font-size: 1.2rem;
}

/* 错误信息 */
.error-message, .no-products {
    text-align: center;
    padding: 40px 0;
    max-width: 600px;
    margin: 0 auto;
    background-color: #1a1a1a;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    border: 1px solid #333;
}

.error-message h2, .no-products h2 {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 16px;
}

.error-message p, .no-products p {
    color: var(--text-secondary);
    font-size: 1rem;
}

/* 页脚 */
.main-footer {
    background-color: #101010;
    padding: 20px 0;
    margin-top: 40px;
    border-top: 1px solid #222;
    text-align: center;
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
}

.footer-bottom p {
    color: var(--text-secondary);
    font-size: 0.8rem;
    margin-bottom: 0;
}

/* 响应式设计 */
@media (max-width: 1200px) {
    .nav-link {
        padding: 8px 8px;
        font-size: 0.9rem;
    }

    .nav-link i {
        margin-right: 4px;
    }

    .nav-menu {
        gap: 10px;
    }

    .search-box input {
        width: 140px;
    }

    .search-box input:focus {
        width: 180px;
    }
}

@media (max-width: 992px) {
    .hero-section {
        flex-direction: column;
    }

    .hero-content {
        padding: 30px;
    }

    .hero-image {
        height: 300px;
        width: 100%;
    }

    .guide-steps {
        flex-direction: column;
        gap: 30px;
    }

    .guide-step:not(:last-child):after {
        display: none;
    }

    .sponsor-options {
        flex-direction: column;
        align-items: center;
    }

    .sponsor-tier {
        width: 100%;
        max-width: 350px;
    }

    .sponsor-tier.featured {
        transform: none;
    }

    .sponsor-tier.featured:hover {
        transform: translateY(-8px);
    }

    .nav-container {
        flex-wrap: wrap;
    }

    .logo {
        flex: 1;
    }

    .nav-right {
        flex: 1;
        justify-content: flex-end;
    }

    .nav-menu {
        order: 3;
        width: 100%;
        margin-top: 10px;
        justify-content: flex-start;
        overflow-x: auto;
        padding-bottom: 5px;
    }
}

@media (max-width: 768px) {
    .hero-content h1 {
        font-size: 2rem;
    }

    .nav-container {
        flex-wrap: wrap;
        height: auto;
        padding: 10px 0;
    }

    .logo {
        margin-bottom: 10px;
    }

    .nav-menu {
        order: 3;
        width: 100%;
        overflow-x: auto;
        padding: 10px 0;
        flex-wrap: nowrap;
        justify-content: flex-start;
        margin-top: 10px;
        border-top: 1px solid #333;
    }

    .nav-menu::-webkit-scrollbar {
        display: none;
    }

    .nav-right {
        flex-grow: 1;
        justify-content: flex-end;
    }

    .auth-links {
        margin-left: 10px;
    }

    .search-box input {
        width: 150px;
    }

    .search-box input:focus {
        width: 180px;
    }

    .auth-button {
        padding: 6px 12px;
        font-size: 13px;
    }

    .user-dropdown {
        position: static;
    }

    .dropdown-menu {
        position: absolute;
        top: 60px;
        right: 20px;
        left: auto;
    }

    .profile-header {
        flex-direction: column;
        text-align: center;
    }

    .user-stats {
        justify-content: center;
    }

    .profile-actions {
        flex-direction: row;
        margin-top: 20px;
    }

    .qrcode-images {
        flex-direction: column;
        align-items: center;
        gap: 30px;
    }

    .qrcode img {
        width: 250px;
        height: 250px;
    }

    .sponsor-qrcode {
        padding: 30px 20px;
    }

    .buy-modal-content {
        width: 95%;
        padding: 15px;
    }

    .buy-modal-buttons button {
        padding: 8px 15px;
    }

    .modal-image {
        max-width: 95%;
    }
}

@media (max-width: 576px) {
    .nav-container {
        padding: 10px 15px;
    }

    .auth-button {
        padding: 5px 10px;
        font-size: 12px;
        margin-left: 5px;
    }

    .auth-button i {
        margin-right: 3px;
    }

    .search-box input {
        width: 120px;
        padding: 6px 12px;
        padding-right: 30px;
    }

    .search-box input:focus {
        width: 150px;
    }

    .search-box button {
        right: 8px;
    }

    .features-grid {
        grid-template-columns: 1fr;
    }

    .hero-buttons {
        flex-direction: column;
        gap: 10px;
    }

    .qrcode img {
        width: 200px;
        height: 200px;
    }

    .products-grid {
        grid-template-columns: 1fr;
    }

    .coordinate-grid {
        grid-template-columns: 1fr;
    }

    .products-grid.list-layout .product-card {
        flex-direction: column;
    }

    .products-grid.list-layout .product-image {
        width: 100%;
        height: 120px !important;
    }

    .category-tabs {
        overflow-x: auto;
        white-space: nowrap;
        padding: 10px;
    }

    .category-tabs::-webkit-scrollbar {
        display: none;
    }
}

@media (max-width: 480px) {
    .nav-container {
        justify-content: center;
    }

    .logo {
        width: 100%;
        justify-content: center;
        margin-bottom: 8px;
    }

    .nav-right {
        width: 100%;
        justify-content: space-between;
        margin-bottom: 8px;
    }

    .auth-links {
        margin-left: 0;
        flex: 1;
        justify-content: flex-end;
    }

    .search-box {
        flex: 2;
    }

    .auth-button {
        border-radius: 4px;
    }

    .login-btn, .register-btn {
        min-width: 70px;
        text-align: center;
    }

    .register-btn {
        margin-left: 5px;
    }

    .nav-menu {
        border-top: 1px solid #333;
        justify-content: flex-start;
        gap: 10px;
    }

    .nav-link {
        font-size: 0.85rem;
        padding: 6px 10px;
    }

    .nav-link i {
        margin-right: 4px;
    }
}

/* 首页样式 */
.home-container {
    margin-top: 30px;
}

.hero-section {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 40px;
    background-color: #1a1a1a;
    border-radius: var(--border-radius);
    overflow: hidden;
    border: 1px solid #333;
    box-shadow: var(--box-shadow);
}

.hero-content {
    flex: 1;
    padding: 40px;
}

.hero-content h1 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 16px;
    color: var(--text-primary);
}

.hero-content p {
    font-size: 1.2rem;
    color: var(--text-secondary);
    margin-bottom: 24px;
}

.hero-buttons {
    display: flex;
    gap: 15px;
}

.primary-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 12px 24px;
    background-color: var(--primary-color);
    color: #000;
    border-radius: 30px;
    font-weight: 600;
    font-size: 1rem;
    transition: var(--transition);
    border: none;
}

.primary-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 15px rgba(0, 168, 255, 0.3);
    opacity: 1;
}

.primary-btn i {
    margin-right: 8px;
}

.secondary-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 12px 24px;
    background-color: transparent;
    color: var(--text-primary);
    border-radius: 30px;
    font-weight: 600;
    font-size: 1rem;
    transition: var(--transition);
    border: 1px solid #444;
}

.secondary-btn:hover {
    background-color: rgba(255, 255, 255, 0.1);
    transform: translateY(-3px);
    opacity: 1;
}

.secondary-btn i {
    margin-right: 8px;
}

.hero-image {
    flex: 1;
    height: 400px;
    overflow: hidden;
}

.hero-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

/* 功能特性部分 */
.section-heading {
    font-size: 1.8rem;
    font-weight: 600;
    margin-bottom: 24px;
    color: var(--text-primary);
    position: relative;
    display: inline-block;
    padding-bottom: 10px;
}

.section-heading:after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 60px;
    height: 3px;
    background-color: var(--primary-color);
    border-radius: 3px;
}

.features-section {
    margin-bottom: 40px;
    padding: 30px;
    background-color: #1a1a1a;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    border: 1px solid #333;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
    gap: 25px;
    margin-top: 20px;
}

.feature-card {
    background-color: var(--card-bg);
    border-radius: var(--border-radius);
    padding: 24px;
    transition: var(--transition);
    border: 1px solid #333;
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
    border-color: var(--primary-color);
}

.feature-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background-color: rgba(0, 168, 255, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 16px;
}

.feature-icon i {
    font-size: 1.4rem;
    color: var(--primary-color);
}

.feature-card h3 {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 12px;
    color: var(--text-primary);
}

.feature-card p {
    color: var(--text-secondary);
    font-size: 0.95rem;
    line-height: 1.5;
}

/* 使用指南部分 */
.guide-section {
    margin-bottom: 40px;
    padding: 30px;
    background-color: #1a1a1a;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    border: 1px solid #333;
}

.guide-steps {
    display: flex;
    justify-content: space-between;
    margin-top: 20px;
}

.guide-step {
    flex: 1;
    text-align: center;
    padding: 20px;
    position: relative;
}

.guide-step:not(:last-child):after {
    content: '';
    position: absolute;
    top: 55px;
    right: -30px;
    width: 60px;
    height: 2px;
    background-color: #333;
}

.step-number {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background-color: var(--primary-color);
    color: #000;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.4rem;
    font-weight: 700;
    margin: 0 auto 20px;
}

.guide-step h3 {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 12px;
    color: var(--text-primary);
}

.guide-step p {
    color: var(--text-secondary);
    font-size: 0.95rem;
}

/* 赞助页面样式 */
.sponsor-container {
    margin-top: 30px;
}

.sponsor-header {
    text-align: center;
    margin-bottom: 40px;
}

.sponsor-header h1 {
    font-size: 2.2rem;
    font-weight: 700;
    margin-bottom: 16px;
    color: var(--text-primary);
}

.sponsor-header p {
    font-size: 1.1rem;
    color: var(--text-secondary);
    max-width: 700px;
    margin: 0 auto;
}

.sponsor-options {
    display: flex;
    justify-content: center;
    gap: 25px;
    margin-bottom: 40px;
}

.sponsor-tier {
    background-color: var(--card-bg);
    border-radius: var(--border-radius);
    border: 1px solid #333;
    width: 300px;
    padding-bottom: 24px;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.sponsor-tier:hover {
    transform: translateY(-8px);
    box-shadow: 0 12px 30px rgba(0, 0, 0, 0.4);
    border-color: var(--primary-color);
}

.sponsor-tier.featured {
    border-color: var(--primary-color);
    transform: scale(1.05);
    box-shadow: 0 12px 30px rgba(0, 0, 0, 0.4);
    z-index: 1;
}

.sponsor-tier.featured:hover {
    transform: scale(1.05) translateY(-8px);
}

.tier-tag {
    position: absolute;
    top: 15px;
    right: -30px;
    background-color: var(--primary-color);
    color: #000;
    font-size: 0.8rem;
    font-weight: 600;
    padding: 5px 30px;
    transform: rotate(45deg);
}

.tier-header {
    padding: 24px;
    border-bottom: 1px solid #333;
    text-align: center;
}

.tier-header h2 {
    font-size: 1.4rem;
    font-weight: 600;
    margin-bottom: 12px;
    color: var(--text-primary);
}

.tier-price {
    font-size: 2rem;
    font-weight: 700;
    color: var(--primary-color);
}

.tier-benefits {
    padding: 24px;
}

.tier-benefits ul {
    list-style: none;
}

.tier-benefits li {
    margin-bottom: 12px;
    color: var(--text-secondary);
    font-size: 0.95rem;
    display: flex;
    align-items: center;
}

.tier-benefits li i {
    color: var(--primary-color);
    margin-right: 10px;
    font-size: 1rem;
}

.sponsor-btn {
    display: block;
    width: 80%;
    margin: 0 auto;
    padding: 12px;
    background-color: var(--primary-color);
    color: #000;
    border-radius: 30px;
    font-weight: 600;
    font-size: 1rem;
    text-align: center;
    transition: var(--transition);
}

.sponsor-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 15px rgba(0, 168, 255, 0.3);
    opacity: 1;
}

.sponsor-qrcode {
    text-align: center;
    margin-top: 40px;
    background-color: var(--card-bg);
    border-radius: var(--border-radius);
    padding: 40px;
    border: 1px solid #333;
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.25);
}

.sponsor-qrcode h2 {
    font-size: 1.6rem;
    font-weight: 600;
    margin-bottom: 30px;
    color: var(--text-primary);
}

.qrcode-images {
    display: flex;
    justify-content: center;
    gap: 60px;
    margin-bottom: 30px;
}

.qrcode {
    transition: transform 0.3s ease;
}

.qrcode:hover {
    transform: scale(1.05);
}

.qrcode img {
    width: 220px;
    height: 220px;
    border-radius: 12px;
    border: 2px solid #444;
    margin-bottom: 15px;
    background-color: #fff;
    padding: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.qrcode p {
    color: var(--text-secondary);
    font-size: 1.1rem;
    font-weight: 500;
}

.sponsor-note {
    color: var(--text-secondary);
    font-size: 1rem;
    margin-top: 20px;
    background-color: rgba(255, 255, 255, 0.1);
    padding: 12px 20px;
    border-radius: 8px;
    display: inline-block;
}

/* 个人信息页面样式 - 新设计 */
.profile-container {
    margin-top: 20px;
    margin-bottom: 20px;
}

.profile-header {
    background-color: #1e1e1e;
    text-align: center;
    padding: 20px;
    margin-bottom: 20px;
    border-radius: 4px;
    border: 1px solid #333;
}

.profile-header h1 {
    font-size: 24px;
    margin-bottom: 5px;
    color: #fff;
}

.profile-header p {
    color: #999;
    font-size: 14px;
}

/* 左侧信息样式 */
.profile-sidebar-info {
    background-color: #1e1e1e;
    border-radius: 4px;
    padding: 20px;
    margin-bottom: 15px;
    text-align: center;
}

.user-basic-info {
    margin-bottom: 15px;
    padding-bottom: 15px;
    border-bottom: 1px solid #333;
}

.profile-username {
    font-size: 20px;
    margin-bottom: 5px;
    color: #fff;
    font-weight: normal;
}

.profile-joined {
    font-size: 12px;
    color: #999;
}

.profile-stats {
    display: flex;
    justify-content: space-between;
}

.profile-stat {
    text-align: center;
    flex: 1;
}

.stat-number {
    display: block;
    font-size: 18px;
    color: #00a8ff;
    margin-bottom: 5px;
}

.stat-label {
    font-size: 12px;
    color: #999;
}

/* 左侧导航菜单 */
.profile-nav-container {
    background-color: #1e1e1e;
    border-radius: 4px;
    overflow: hidden;
}

.profile-nav {
    list-style: none;
    padding: 0;
    margin: 0;
}

.profile-nav li {
    border-bottom: 1px solid #333;
}

.profile-nav li:last-child {
    border-bottom: none;
}

.profile-nav li a {
    display: block;
    padding: 12px 15px;
    color: #999;
    text-decoration: none;
    transition: all 0.2s;
}

.profile-nav li a i {
    margin-right: 10px;
    width: 16px;
    text-align: center;
}

.profile-nav li.active a {
    background-color: #00a8ff;
    color: #000;
}

.profile-nav li:not(.active) a:hover {
    background-color: #252525;
    color: #fff;
}

/* 右侧内容区域 */
.profile-detail-card {
    background-color: #1e1e1e;
    border-radius: 4px;
    padding: 20px;
}

.tab-title {
    font-size: 18px;
    margin-bottom: 20px;
    color: #00a8ff;
    font-weight: normal;
    padding-bottom: 10px;
    border-bottom: 1px solid #333;
}

/* 表格式布局 */
.profile-info-table {
    width: 100%;
}

.info-row {
    display: flex;
    border-bottom: 1px solid #333;
    padding: 12px 0;
}

.info-row:last-child {
    border-bottom: none;
}

.info-label {
    width: 120px;
    color: #999;
    font-size: 14px;
}

.info-value {
    flex: 1;
    color: #fff;
    font-size: 14px;
}

/* 保存按钮 */
.action-buttons {
    margin-top: 20px;
    text-align: center;
}

.save-btn {
    background-color: #00a8ff;
    color: #000;
    border: none;
    padding: 8px 20px;
    border-radius: 4px;
    font-size: 14px;
    transition: all 0.3s;
}

.save-btn:hover {
    background-color: #0095e0;
    box-shadow: 0 2px 5px rgba(0, 168, 255, 0.3);
}

/* 重定向页面样式 */
.redirect-container {
    padding: 100px 0;
    max-width: 600px;
    margin: 0 auto;
}

.redirect-message {
    background-color: #f8f9fa;
    border-radius: 10px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    padding: 40px 30px;
}

.redirect-message h2 {
    color: #343a40;
    font-size: 24px;
    margin-bottom: 20px;
}

.redirect-message p {
    color: #6c757d;
    font-size: 16px;
}

.spinner-border {
    color: #007bff;
    width: 3rem;
    height: 3rem;
}

/* 正在开发页面样式 */
.under-construction-container {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 60px 0;
}

.construction-content {
    text-align: center;
    max-width: 600px;
    margin: 0 auto;
    padding: 40px;
    background-color: var(--card-bg);
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    border: 1px solid #333;
}

.construction-icon {
    font-size: 60px;
    color: var(--primary-color);
    margin-bottom: 20px;
    animation: spin 10s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.construction-content h1 {
    font-size: 28px;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 20px;
}

.construction-message {
    margin-bottom: 30px;
}

.construction-message p {
    font-size: 18px;
    color: var(--text-secondary);
    line-height: 1.5;
}

.construction-progress {
    margin-bottom: 30px;
}

.progress {
    height: 20px;
    background-color: #1a1a1a;
    border-radius: 10px;
    overflow: hidden;
    margin-bottom: 10px;
}

.progress-bar {
    background-color: var(--primary-color);
    height: 100%;
}

.progress-bar-striped {
    background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
    background-size: 1rem 1rem;
}

.progress-bar-animated {
    animation: progress-bar-stripes 1s linear infinite;
}

@keyframes progress-bar-stripes {
    from { background-position: 1rem 0; }
    to { background-position: 0 0; }
}

.progress-text {
    color: var(--text-secondary);
    font-size: 14px;
}

.return-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 10px 20px;
    background-color: #333;
    color: var(--text-primary);
    border: 1px solid #444;
    border-radius: 25px;
    font-size: 16px;
    transition: var(--transition);
}

.return-btn:hover {
    background-color: var(--primary-color);
    color: #000;
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0, 168, 255, 0.3);
    opacity: 1;
}

.return-btn i {
    margin-right: 10px;
}

/* 登录和注册页面样式 */
.auth-container {
    margin-top: 40px;
    margin-bottom: 40px;
}

.auth-card {
    background-color: #1e1e1e;
    border-radius: 4px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    padding: 30px;
    margin-bottom: 20px;
    border: 1px solid #333;
}

.auth-header {
    text-align: center;
    margin-bottom: 30px;
}

.auth-header h2 {
    font-size: 24px;
    margin-bottom: 10px;
    color: var(--text-primary);
}

.auth-header p {
    color: var(--text-secondary);
    font-size: 14px;
}

.auth-form .form-group {
    margin-bottom: 20px;
}

.auth-form label {
    font-weight: 500;
    margin-bottom: 8px;
    display: block;
    color: var(--text-primary);
}

.auth-form .form-control {
    height: 40px;
    border-radius: 4px;
    border: 1px solid #333;
    background-color: #282828;
    color: var(--text-primary);
    font-size: 14px;
}

.auth-form .form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    background-color: #282828;
}

.auth-form .form-text {
    color: #666;
    font-size: 12px;
}

.auth-form .btn-primary {
    height: 40px;
    font-size: 16px;
    font-weight: 500;
    margin-top: 10px;
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    border-radius: 4px;
}

.auth-form .btn-primary:hover {
    background-color: #0095e0;
    border-color: #0095e0;
    box-shadow: 0 2px 5px rgba(0, 168, 255, 0.3);
}

.auth-form .remember-me {
    display: flex;
    justify-content: flex-start;
    align-items: center;
}

.auth-form .custom-control-label {
    color: var(--text-secondary);
    font-size: 14px;
    font-weight: normal;
}

.auth-footer {
    text-align: center;
    margin-top: 20px;
    font-size: 14px;
    color: var(--text-secondary);
}

.auth-footer a {
    color: var(--primary-color);
    font-weight: 500;
}

/* 模态框样式 */
.modal-content {
    background-color: #1e1e1e;
    border: 1px solid #333;
    color: var(--text-primary);
}

.modal-header {
    border-bottom: 1px solid #333;
}

.modal-header .close {
    color: var(--text-primary);
}

.modal-footer {
    border-top: 1px solid #333;
}

/* VIP徽章样式 */
.profile-vip-badge {
    display: inline-flex;
    align-items: center;
    background: linear-gradient(135deg, #ffd700, #e8b923);
    color: #472e00;
    font-weight: bold;
    padding: 5px 10px;
    border-radius: 20px;
    font-size: 12px;
    margin-top: 10px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.profile-vip-badge i {
    margin-right: 5px;
    color: #472e00;
}

/* 个人信息页面样式增强 */
.profile-form input[readonly] {
    background-color: rgba(0, 0, 0, 0.03);
    cursor: not-allowed;
}

/* 全局元素的背景色修复 */
html {
    background-color: var(--dark-bg);
}

body {
    background-color: var(--dark-bg) !important;
}

.row {
    background-color: transparent;
}

.tab-content {
    background-color: transparent;
}

.fade.show {
    background-color: transparent;
}

.col-md-3 {
    background-color: transparent;
}

/* 重置bootstrap可能的默认白色背景 */
.card, .modal-content, .alert, .form-control {
    background-color: #1e1e1e !important;
    color: var(--text-primary) !important;
}

/* 确保form表单元素的背景色 */
input, textarea, select {
    background-color: #282828 !important;
    color: var(--text-primary) !important;
    border: 1px solid #333 !important;
}

/* 修复可能的白色背景问题 */
.modal-body, .modal-footer, .modal-header {
    background-color: #1e1e1e !important;
}

/* 模态框和弹出层的深色主题 */
.dark-theme .modal,
.dark-theme .popup,
.dark-theme .dialog,
.dark-theme .dropdown,
.dark-theme .dropdown-menu,
.dark-theme .popover,
.dark-theme .tooltip {
    background-color: var(--dark-bg) !important;
    color: var(--text-primary);
    border-color: var(--border-color);
}

/* 确保页面背景是深色的 */
.dark-theme {
    background-color: var(--dark-bg) !important;
}

html.dark-theme,
body.dark-theme {
    background-color: var(--dark-bg) !important;
}

/* 表单元素深色主题样式 */
.dark-theme input,
.dark-theme textarea,
.dark-theme select,
.dark-theme .form-control,
.dark-theme .input-group-text {
    background-color: #282828 !important;
    color: var(--text-primary) !important;
    border-color: #333 !important;
}

.dark-theme input:focus,
.dark-theme textarea:focus,
.dark-theme select:focus,
.dark-theme .form-control:focus {
    background-color: #2d2d2d !important;
    border-color: var(--primary-color) !important;
    box-shadow: 0 0 0 0.2rem rgba(0, 168, 255, 0.25) !important;
}

/* 按钮深色主题样式 */
.dark-theme .btn:not(.btn-primary):not(.btn-secondary):not(.btn-success):not(.btn-danger):not(.btn-warning):not(.btn-info) {
    background-color: #2a2a2a;
    color: var(--text-primary);
    border-color: #444;
}

.dark-theme .btn:not(.btn-primary):not(.btn-secondary):not(.btn-success):not(.btn-danger):not(.btn-warning):not(.btn-info):hover {
    background-color: #333;
    border-color: #555;
}

/* 滚动条样式 */
.dark-theme ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
    background-color: #1a1a1a;
}

.dark-theme ::-webkit-scrollbar-thumb {
    background-color: #444;
    border-radius: 4px;
}

.dark-theme ::-webkit-scrollbar-thumb:hover {
    background-color: #555;
}

/* 表格深色主题 */
.dark-theme table,
.dark-theme .table {
    color: var(--text-primary) !important;
}

.dark-theme .table th,
.dark-theme .table td {
    border-color: #333 !important;
}

.dark-theme .table-striped tbody tr:nth-of-type(odd) {
    background-color: rgba(255, 255, 255, 0.05) !important;
}

.dark-theme .table-hover tbody tr:hover {
    background-color: rgba(255, 255, 255, 0.075) !important;
}

/* 个人资料页突出显示积分 */
.profile-points-highlight {
    margin-top: 15px;
    display: inline-flex;
    align-items: center;
    background: linear-gradient(135deg, #0066cc, #00a8ff);
    padding: 8px 20px;
    border-radius: 30px;
    box-shadow: 0 3px 10px rgba(0, 168, 255, 0.3);
}

.points-value {
    font-size: 24px;
    font-weight: bold;
    color: #fff;
    margin-right: 8px;
}

.points-label {
    font-size: 14px;
    color: rgba(255, 255, 255, 0.9);
}

/* 修复导航栏对齐问题 */
.nav-item {
    display: flex;
    align-items: center;
}

.main-nav ul {
    padding-left: 0;
}

/* 用户卡片式个人信息 */
.user-info-card {
    width: 320px;
    height: 400px;
    background: #121212;
    position: relative;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    overflow: hidden;
    border-radius: 20px;
    margin: 0 auto;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
}

.user-info-card-content {
    z-index: 1;
    color: white;
    padding: 20px;
    width: 100%;
    text-align: center;
}

.user-info-card::before {
    content: '';
    position: absolute;
    width: 150px;
    background-image: linear-gradient(180deg, rgb(0, 183, 255), rgb(255, 48, 255));
    height: 130%;
    animation: rotBGimg 8s linear infinite;
    transition: all 0.2s linear;
}

@keyframes rotBGimg {
    from {
        transform: rotate(0deg);
    }

    to {
        transform: rotate(360deg);
    }
}

.user-info-card::after {
    content: '';
    position: absolute;
    background: #121212;
    inset: 5px;
    border-radius: 15px;
}

.user-info-card-username {
    font-size: 24px;
    margin-bottom: 10px;
    font-weight: bold;
}

.user-info-card-stats {
    margin-top: 15px;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
}

.user-info-card-stat {
    background-color: rgba(255, 255, 255, 0.1);
    padding: 10px;
    border-radius: 10px;
}

.user-info-card-stat-label {
    font-size: 12px;
    color: #999;
}

.user-info-card-stat-value {
    font-size: 20px;
    font-weight: bold;
    color: #00a8ff;
    text-shadow: 0 0 5px rgba(0, 168, 255, 0.7);
    background-color: rgba(0, 0, 0, 0.3);
    padding: 5px 10px;
    border-radius: 8px;
    display: inline-block;
    min-width: 60px;
}

.user-info-card-vip {
    margin-top: 15px;
    background: linear-gradient(135deg, #ffd700, #e8b923);
    color: #472e00;
    padding: 5px 15px;
    border-radius: 20px;
    font-weight: bold;
    display: inline-block;
}

.user-info-card-coordinate {
    margin-top: 15px;
    background-color: rgba(255, 255, 255, 0.1);
    padding: 8px 15px;
    border-radius: 10px;
    font-size: 14px;
}

/* 导航栏积分显示 */
.nav-points {
    display: flex;
    align-items: center;
    background: linear-gradient(135deg, #e8b923, #ffd700);
    color: #472e00;
    padding: 4px 10px;
    border-radius: 15px;
    font-weight: bold;
    font-size: 14px;
    margin-right: 12px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
    flex-shrink: 0;
    border: none;
}

.nav-points i {
    color: #472e00;
    margin-right: 5px;
}

.nav-points span {
    color: #472e00;
    background: transparent;
}

/* 表单元素 - 暗色主题样式 */
input[type="text"],
input[type="email"],
input[type="password"],
input[type="number"],
textarea,
select {
    background: rgba(30, 30, 35, 0.8);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 4px;
    color: #f5f5f5;
    padding: 8px 12px;
    font-size: 0.95rem;
    transition: all 0.2s ease;
    width: 100%;
}

input[type="text"]:focus,
input[type="email"]:focus,
input[type="password"]:focus,
input[type="number"]:focus,
textarea:focus,
select:focus {
    background: rgba(40, 40, 45, 0.9);
    border-color: rgba(0, 168, 255, 0.5);
    box-shadow: 0 0 0 2px rgba(0, 168, 255, 0.2);
    outline: none;
}

/* 按钮样式 */
.btn-primary {
    background: #00a8ff;
    border: none;
    border-radius: 4px;
    padding: 8px 16px;
    color: #ffffff;
    font-weight: 500;
    transition: all 0.2s ease;
}

.btn-primary:hover {
    background: #0090df;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.btn-secondary {
    background: rgba(60, 60, 70, 0.8);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 4px;
    padding: 8px 16px;
    color: #f5f5f5;
    font-weight: 500;
    transition: all 0.2s ease;
}

.btn-secondary:hover {
    background: rgba(70, 70, 80, 0.9);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.btn-danger {
    background: #ff3b30;
    border: none;
    border-radius: 4px;
    padding: 8px 16px;
    color: #ffffff;
    font-weight: 500;
    transition: all 0.2s ease;
}

.btn-danger:hover {
    background: #e02e24;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

/* 身份验证按钮 */
.auth-button {
    display: inline-block;
    padding: 6px 14px;
    border-radius: 4px;
    font-weight: 500;
    font-size: 0.9rem;
    text-decoration: none;
    transition: all 0.2s ease;
}

.login-btn {
    background: #00a8ff;
    color: #ffffff;
}

.login-btn:hover {
    background: #0090df;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    text-decoration: none;
    color: #ffffff;
}

/* 用户下拉菜单 */
.user-dropdown {
    position: relative;
}

.dropdown-toggle {
    background: rgba(30, 30, 35, 0.8);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 4px;
    padding: 6px 12px;
    color: #f5f5f5;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.2s ease;
}

.dropdown-toggle:hover {
    background: rgba(40, 40, 45, 0.9);
}

.dropdown-toggle i {
    margin-right: 5px;
    font-size: 16px;
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    right: 0;
    background: rgba(25, 25, 30, 0.95);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 4px;
    width: 160px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    margin-top: 5px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(10px);
    transition: all 0.2s ease;
    z-index: 1000;
}

.dropdown-menu.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-menu a {
    display: block;
    padding: 8px 15px;
    color: #f5f5f5;
    text-decoration: none;
    font-size: 0.9rem;
    transition: all 0.15s ease;
}

.dropdown-menu a:hover {
    background-color: rgba(0, 168, 255, 0.1);
    color: #00a8ff;
}

.dropdown-menu a i {
    margin-right: 8px;
    color: #00a8ff;
}

/* 自定义滚动条 */
::-webkit-scrollbar {
    width: 10px;
    height: 10px;
}

::-webkit-scrollbar-track {
    background: rgba(30, 30, 35, 0.5);
}

::-webkit-scrollbar-thumb {
    background: rgba(100, 100, 120, 0.5);
    border-radius: 5px;
}

::-webkit-scrollbar-thumb:hover {
    background: rgba(120, 120, 140, 0.7);
}

/* 图片放大模态框 */
.image-modal {
    display: none;
    position: fixed;
    z-index: 1000;
    padding: 20px;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.9);
    align-items: center;
    justify-content: center;
    flex-direction: column;
}

.modal-image {
    max-width: 90%;
    max-height: 80vh;
    margin-bottom: 20px;
    border: 2px solid #333;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.5);
}

.modal-caption {
    color: white;
    text-align: center;
    font-size: 1.2rem;
    padding: 10px;
    max-width: 80%;
}

.modal-close {
    position: absolute;
    top: 20px;
    right: 30px;
    color: white;
    font-size: 40px;
    font-weight: bold;
    cursor: pointer;
    transition: 0.3s;
}

.modal-close:hover {
    color: var(--primary-color);
}

/* 购买按钮 */
.product-actions {
    display: flex;
    gap: 10px;
    margin-top: 10px;
}

.add-to-cart-btn {
    background-color: #ff4500;
    color: white;
    border: none;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    cursor: pointer;
    transition: all 0.3s;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
    box-shadow: 0 3px 8px rgba(255, 69, 0, 0.4);
    position: relative;
    overflow: hidden;
}

.add-to-cart-btn:before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(255,255,255,0.2) 0%, rgba(255,255,255,0) 50%);
}

.add-to-cart-btn:hover {
    background-color: #ff5722;
    transform: translateY(-3px) scale(1.05);
    box-shadow: 0 5px 15px rgba(255, 69, 0, 0.6);
}

.add-to-cart-btn.added {
    animation: pulse 0.5s;
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.2);
        background-color: #28a745;
    }
    100% {
        transform: scale(1);
    }
}

/* 购买确认模态框 */
.buy-modal {
    display: none;
    position: fixed;
    z-index: 1000;
    padding: 20px;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    align-items: center;
    justify-content: center;
}

.buy-modal-content {
    background-color: #2a2a2a;
    border-radius: 10px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.5);
    padding: 25px;
    width: 90%;
    max-width: 450px;
    position: relative;
    border: 1px solid #444;
}

.buy-modal-close {
    position: absolute;
    top: 15px;
    right: 15px;
    font-size: 25px;
    font-weight: bold;
    color: #999;
    cursor: pointer;
}

.buy-modal-content h2 {
    color: var(--primary-color);
    border-bottom: 1px solid #444;
    padding-bottom: 10px;
    margin-bottom: 20px;
    font-size: 1.5rem;
}

.buy-item-info {
    margin-bottom: 20px;
}

.buy-item-info p {
    margin: 8px 0;
    color: #ddd;
    font-size: 1.1rem;
}

.buy-quantity {
    margin-bottom: 20px;
}

.buy-quantity label {
    display: block;
    margin-bottom: 8px;
    color: #bbb;
}

.quantity-control {
    display: flex;
    align-items: center;
    gap: 10px;
}

.quantity-control button {
    width: 35px;
    height: 35px;
    border: none;
    background-color: #444;
    color: white;
    font-weight: bold;
    border-radius: 5px;
    cursor: pointer;
    font-size: 1.2rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

.quantity-control button:hover {
    background-color: #555;
}

.quantity-control input {
    width: 60px;
    height: 35px;
    text-align: center;
    border: 1px solid #444;
    background-color: #333;
    color: white;
    border-radius: 5px;
    font-size: 1.1rem;
}

.buy-total {
    margin: 20px 0;
    padding: 10px;
    background-color: #333;
    border-radius: 5px;
}

.buy-total p {
    font-size: 1.2rem;
    color: var(--primary-color);
    font-weight: bold;
}

.buy-modal-buttons {
    display: flex;
    justify-content: space-between;
    margin-top: 20px;
}

.buy-modal-buttons button {
    padding: 10px 20px;
    border: none;
    border-radius: 5px;
    font-weight: bold;
    cursor: pointer;
    transition: background-color 0.3s;
}

#cancelBuy {
    background-color: #555;
    color: white;
}

#cancelBuy:hover {
    background-color: #777;
}

#confirmBuy {
    background-color: var(--primary-color);
    color: white;
}

#confirmBuy:hover {
    filter: brightness(1.1);
}

/* 响应式调整 */
@media (max-width: 768px) {
    .buy-modal-content {
        width: 95%;
        padding: 15px;
    }

    .buy-modal-buttons button {
        padding: 8px 15px;
    }

    .modal-image {
        max-width: 95%;
    }
}

/* 用户仓库样式 */
.page-header {
    text-align: center;
    margin-bottom: 30px;
    color: var(--text-primary);
}

.page-header h1 {
    font-size: 2rem;
    margin-bottom: 10px;
    color: var(--primary-color);
}

.page-header p {
    font-size: 1.1rem;
    color: var(--text-secondary);
}

/* 标签页样式 */
.inventory-tabs {
    display: flex;
    justify-content: center;
    margin-bottom: 30px;
    border-bottom: 1px solid #333;
}

.tab-btn {
    padding: 12px 25px;
    background: none;
    border: none;
    color: var(--text-secondary);
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s;
    position: relative;
}

.tab-btn.active {
    color: var(--primary-color);
}

.tab-btn:after {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 0;
    width: 100%;
    height: 3px;
    background-color: var(--primary-color);
    transform: scaleX(0);
    transition: transform 0.3s;
}

.tab-btn.active:after {
    transform: scaleX(1);
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
    animation: fadeIn 0.5s;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

/* 物品管理工具栏 */
.inventory-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 20px 0 15px 0;
    padding: 12px 18px;
    background: rgba(30, 30, 35, 0.8);
    border-radius: 8px;
    border: 1px solid #3a3a3a;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

/* 操作提示 */
.inventory-tips {
    margin: 0 0 20px 0;
    padding: 8px 15px;
    background: rgba(0, 168, 255, 0.1);
    border-radius: 6px;
    border-left: 3px solid var(--primary-color);
}

.inventory-tips p {
    margin: 0;
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.inventory-tips i {
    color: var(--primary-color);
    margin-right: 5px;
}

.toolbar-left, .toolbar-right {
    display: flex;
    align-items: center;
}

.batch-action-btn {
    background-color: #ff3e30;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 10px 18px;
    font-size: 1rem;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s;
    display: flex;
    align-items: center;
    margin-right: 15px;
    box-shadow: 0 4px 8px rgba(255, 62, 48, 0.3);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.batch-action-btn:not([disabled]):hover {
    background-color: #ff2010;
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(255, 62, 48, 0.4);
}

.batch-action-btn[disabled] {
    background-color: #999;
    cursor: not-allowed;
    opacity: 0.7;
    box-shadow: none;
}

.batch-action-btn i {
    margin-right: 5px;
}

.selected-count {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

/* 开关切换样式 */
.toggle-container {
    display: flex;
    align-items: center;
}

.toggle-switch {
    position: relative;
    display: inline-block;
    width: 40px;
    height: 20px;
    margin-right: 8px;
}

.toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.toggle-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #444;
    transition: .4s;
    border-radius: 20px;
}

.toggle-slider:before {
    position: absolute;
    content: "";
    height: 16px;
    width: 16px;
    left: 2px;
    bottom: 2px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
}

input:checked + .toggle-slider {
    background-color: var(--primary-color);
}

input:checked + .toggle-slider:before {
    transform: translateX(20px);
}

.toggle-label {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

/* 仓库物品网格 */
.inventory-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
    gap: 12px;
    margin-bottom: 30px;
}

/* 仓库物品卡片 */
.inventory-item {
    background: rgba(25, 25, 30, 0.8);
    border-radius: 8px;
    overflow: hidden;
    border: 1px solid #3a3a3a;
    transition: transform 0.3s, box-shadow 0.3s;
    display: flex;
    flex-direction: column;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    height: 100%;
    width: 100%;
}

.inventory-item:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.3);
    border-color: var(--primary-color);
}

.item-image {
    height: 140px;
    position: relative;
    overflow: hidden;
    border-bottom: 1px solid #3a3a3a;
    background-color: #1a1a1a;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 5px;
}

.item-checkbox {
    position: absolute;
    top: 8px;
    left: 8px;
    z-index: 2;
}

.item-checkbox input[type="checkbox"] {
    width: 18px;
    height: 18px;
    cursor: pointer;
    opacity: 0.8;
}

.item-checkbox input[type="checkbox"]:hover {
    opacity: 1;
}

.item-image img {
    width: auto;
    height: auto;
    max-width: 75%;
    max-height: 75%;
    object-fit: contain;
    transition: transform 0.3s;
    margin: 0 auto;
}

.inventory-item:hover .item-image img {
    transform: scale(1.05);
}

.item-content {
    padding: 12px;
    flex: 1;
    display: flex;
    flex-direction: column;
}

.item-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    padding-bottom: 8px;
}

.item-name {
    font-size: 1rem;
    margin: 0;
    color: var(--text-primary);
    font-weight: 600;
    line-height: 1.3;
    max-width: 80%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.item-quantity {
    background: var(--primary-color);
    color: #ffffff;
    padding: 3px 8px;
    border-radius: 12px;
    font-size: 0.85rem;
    font-weight: bold;
    min-width: 24px;
    box-shadow: 0 2px 4px rgba(0, 168, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    text-align: center;
}

.item-details {
    margin-bottom: 10px;
    flex: 1;
}

.item-date {
    color: var(--text-secondary);
    font-size: 0.85rem;
    margin: 3px 0;
}

.item-date i {
    margin-right: 5px;
    color: var(--primary-color);
}

.item-status {
    margin-top: 5px;
}

.status-used {
    color: #e74c3c;
}

.status-available {
    color: #2ecc71;
}

.status-partial {
    color: #f39c12;
}

.item-actions {
    display: flex;
    gap: 8px;
}

.copy-btn, .use-btn, .ship-btn {
    flex: 1;
    padding: 6px 8px;
    border: none;
    border-radius: 4px;
    font-size: 0.85rem;
    font-weight: 600;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s;
}

.copy-btn {
    background-color: #0078d4;
    color: white;
    box-shadow: 0 2px 4px rgba(0, 120, 212, 0.3);
}

.copy-btn:hover {
    background-color: #0067b8;
    transform: translateY(-2px);
    box-shadow: 0 4px 6px rgba(0, 120, 212, 0.4);
}

.use-btn {
    background-color: #28a745;
    color: white;
    box-shadow: 0 2px 4px rgba(40, 167, 69, 0.3);
}

.use-btn:hover {
    background-color: #218838;
    transform: translateY(-2px);
    box-shadow: 0 4px 6px rgba(40, 167, 69, 0.4);
}

.ship-btn {
    background-color: #e74c3c;
    color: white;
    box-shadow: 0 2px 4px rgba(231, 76, 60, 0.3);
}

.ship-btn:hover {
    background-color: #c0392b;
    transform: translateY(-2px);
    box-shadow: 0 4px 6px rgba(231, 76, 60, 0.4);
}

.shipped-status {
    display: flex;
    align-items: center;
    justify-content: center;
    color: #28a745;
    font-weight: 600;
    font-size: 0.9rem;
    padding: 6px 8px;
}

.shipped-status i {
    margin-right: 4px;
}

.copy-btn i, .use-btn i, .ship-btn i {
    margin-right: 4px;
    font-size: 0.9rem;
}

@media (max-width: 768px) {
    .inventory-grid {
        grid-template-columns: repeat(auto-fill, minmax(230px, 1fr));
    }
}

.inventory-item.used {
    opacity: 0.8;
    background: rgba(30, 30, 35, 0.6);
}

.inventory-item:hover .item-image img {
    transform: scale(1.05);
}

.used-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
}

.used-overlay span {
    background-color: rgba(255, 0, 0, 0.7);
    color: white;
    padding: 5px 15px;
    border-radius: 20px;
    font-weight: bold;
    font-size: 1rem;
    transform: rotate(-15deg);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
}

.status-available {
    color: #4caf50;
    font-size: 0.85rem;
    display: inline-flex;
    align-items: center;
    font-weight: 500;
}

.status-used {
    color: #f44336;
    font-size: 0.85rem;
    display: inline-flex;
    align-items: center;
    font-weight: 500;
}

.item-status i {
    margin-right: 5px;
}

/* 购买历史表格样式 */
.purchase-history {
    background: rgba(30, 30, 35, 0.5);
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.history-table {
    width: 100%;
    border-collapse: collapse;
}

.history-table th, .history-table td {
    padding: 12px 15px;
    text-align: left;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.history-table th {
    color: var(--primary-color);
    font-weight: 600;
    font-size: 0.95rem;
}

.history-table td {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.history-table tr:last-child td {
    border-bottom: none;
}

.history-table tr:hover td {
    background-color: rgba(255, 255, 255, 0.05);
}

/* 空仓库和空历史记录样式 */
.empty-inventory, .empty-history {
    text-align: center;
    padding: 40px 20px;
    background: rgba(30, 30, 35, 0.5);
    border-radius: 10px;
    margin-bottom: 30px;
}

.empty-icon {
    font-size: 3rem;
    color: var(--primary-color);
    margin-bottom: 20px;
    opacity: 0.8;
}

.empty-inventory h2, .empty-history h2 {
    font-size: 1.5rem;
    margin-bottom: 10px;
    color: var(--text-primary);
}

.empty-inventory p, .empty-history p {
    color: var(--text-secondary);
    margin-bottom: 20px;
}

/* 复制工具提示 */
.copy-tooltip {
    position: fixed;
    background-color: #333;
    color: white;
    padding: 10px 15px;
    border-radius: 5px;
    font-size: 14px;
    opacity: 0;
    transition: opacity 0.3s;
    z-index: 1000;
    pointer-events: none;
    transform: translateX(-50%);
    text-align: center;
}

.copy-tooltip:after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 50%;
    margin-left: -8px;
    border-width: 8px 8px 0;
    border-style: solid;
    border-color: #333 transparent transparent;
}

/* 购物车样式 */
.floating-cart {
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 1000;
    transition: all 0.3s ease;
}

.cart-collapsed {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background-color: #ff5722; /* 更亮的橙色，与添加按钮一致 */
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: 0 4px 15px rgba(255, 87, 34, 0.5);
    position: relative;
    border: 2px solid #fff;
    animation: pulse-cart 2s infinite;
}

@keyframes pulse-cart {
    0% {
        box-shadow: 0 4px 15px rgba(255, 87, 34, 0.5);
    }
    50% {
        box-shadow: 0 4px 25px rgba(255, 87, 34, 0.8);
    }
    100% {
        box-shadow: 0 4px 15px rgba(255, 87, 34, 0.5);
    }
}

.cart-collapsed:hover {
    transform: translateY(-3px) scale(1.05);
    box-shadow: 0 6px 20px rgba(255, 87, 34, 0.7);
}

.cart-icon {
    font-size: 24px;
    color: #fff; /* 白色图标更醒目 */
}

.cart-badge {
    position: absolute;
    top: -8px;
    right: -8px;
    background-color: #ff3b30;
    color: white;
    border-radius: 50%;
    width: 28px;
    height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    font-weight: bold;
    box-shadow: 0 3px 8px rgba(255, 59, 48, 0.5);
    border: 2px solid #fff;
    animation: bounce 0.5s ease infinite alternate;
}

@keyframes bounce {
    from { transform: scale(1); }
    to { transform: scale(1.1); }
}

.cart-expanded {
    width: 350px;
    max-height: 500px;
    background-color: #2a2a2a;
    border-radius: 10px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.5);
    display: none;
    flex-direction: column;
    overflow: hidden;
    border: 1px solid #444;
}

.cart-header {
    padding: 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #444;
    background-color: #222;
}

.cart-title {
    color: var(--primary-color);
    font-size: 1.2rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
}

.cart-close {
    color: #999;
    font-size: 20px;
    cursor: pointer;
    transition: color 0.2s;
}

.cart-close:hover {
    color: #fff;
}

.cart-items {
    max-height: 300px;
    overflow-y: auto;
    padding: 10px;
}

.cart-empty {
    padding: 30px 20px;
    text-align: center;
    color: #999;
}

.cart-empty i {
    font-size: 40px;
    margin-bottom: 10px;
    color: #555;
}

.cart-item {
    display: flex;
    padding: 12px;
    border-bottom: 1px solid #444;
    position: relative;
    transition: all 0.2s;
    background-color: #2a2a2a;
    border-radius: 6px;
    margin-bottom: 8px;
}

.cart-item:hover {
    background-color: #333;
    transform: translateY(-2px);
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
}

.cart-item:last-child {
    border-bottom: none;
}

.cart-item-info {
    flex: 1;
    padding-right: 10px;
}

.cart-item-name {
    font-size: 0.95rem;
    color: #fff;
    margin-bottom: 5px;
    font-weight: 500;
    display: flex;
    align-items: center;
}

.cart-item-name:before {
    content: '\f07a'; /* 购物车图标 */
    font-family: 'Font Awesome 5 Free';
    font-weight: 900;
    margin-right: 8px;
    color: var(--primary-color);
    font-size: 0.85rem;
}

.cart-item-price {
    font-size: 0.9rem;
    color: #ff4500;
    font-weight: 600;
}

.cart-item-controls {
    display: flex;
    align-items: center;
    gap: 8px;
}

.cart-quantity-btn {
    width: 28px;
    height: 28px;
    border-radius: 4px;
    border: none;
    background-color: #444;
    color: #fff;
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.cart-quantity-btn:hover {
    background-color: #555;
    transform: translateY(-1px);
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.3);
}

.cart-quantity {
    width: 35px;
    height: 28px;
    text-align: center;
    border: 1px solid #444;
    background-color: #333;
    color: #fff;
    border-radius: 4px;
    font-size: 14px;
    font-weight: bold;
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.2);
}

.cart-item-remove {
    position: absolute;
    top: 10px;
    right: 10px;
    color: #999;
    font-size: 14px;
    cursor: pointer;
    transition: color 0.2s;
}

.cart-item-remove:hover {
    color: #ff3b30;
}

.cart-footer {
    padding: 15px;
    border-top: 1px solid #444;
    background-color: #222;
    border-radius: 0 0 10px 10px;
}

.cart-total {
    display: flex;
    justify-content: space-between;
    margin-bottom: 15px;
    font-size: 1.1rem;
    color: #fff;
    background-color: #333;
    padding: 10px 15px;
    border-radius: 6px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.cart-total-price {
    color: #ff4500;
    font-weight: 600;
    font-size: 1.2rem;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.cart-actions {
    display: flex;
    gap: 10px;
}

.cart-clear {
    flex: 1;
    padding: 12px;
    border: none;
    border-radius: 6px;
    background-color: #444;
    color: #fff;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.cart-clear:hover {
    background-color: #555;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.cart-checkout {
    flex: 2;
    padding: 12px;
    border: none;
    border-radius: 6px;
    background-color: #ff4500;
    color: #fff;
    font-weight: 600;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.2s;
    box-shadow: 0 2px 5px rgba(255, 69, 0, 0.3);
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.cart-checkout:hover {
    background-color: #ff5722;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(255, 69, 0, 0.5);
}

@media (max-width: 576px) {
    .cart-expanded {
        width: 300px;
        max-height: 450px;
        bottom: 80px;
        right: 10px;
    }

    .cart-collapsed {
        width: 50px;
        height: 50px;
        bottom: 15px;
        right: 15px;
    }

    .cart-icon {
        font-size: 20px;
    }
}

@media (max-width: 576px) {
    .inventory-grid {
        grid-template-columns: 1fr;
    }

    .history-table th, .history-table td {
        padding: 8px 10px;
        font-size: 0.85rem;
    }
}

/* 自定义对话框 */
.custom-dialog {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    z-index: 1000;
    justify-content: center;
    align-items: center;
    backdrop-filter: blur(3px);
}

.dialog-content {
    background-color: #222;
    background-image: linear-gradient(to bottom, #2a2a2a, #222);
    border-radius: 12px;
    width: 420px;
    max-width: 90%;
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.5), 0 0 0 1px rgba(255, 255, 255, 0.1);
    animation: dialogFadeIn 0.3s;
    border: 1px solid rgba(255, 255, 255, 0.1);
    overflow: hidden;
}

@keyframes dialogFadeIn {
    from { opacity: 0; transform: translateY(-20px) scale(0.98); }
    to { opacity: 1; transform: translateY(0) scale(1); }
}

.dialog-header {
    padding: 18px 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: rgba(0, 0, 0, 0.2);
}

.dialog-title {
    font-size: 1.15rem;
    font-weight: 600;
    color: #fff;
    display: flex;
    align-items: center;
    gap: 10px;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.dialog-title i {
    font-size: 1.3rem;
    color: var(--primary-color);
}

.dialog-close {
    font-size: 1.5rem;
    cursor: pointer;
    color: rgba(255, 255, 255, 0.5);
    transition: all 0.2s;
    line-height: 1;
    width: 28px;
    height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
}

.dialog-close:hover {
    color: #fff;
    background-color: rgba(255, 255, 255, 0.1);
}

.dialog-body {
    padding: 24px 20px;
    color: rgba(255, 255, 255, 0.9);
    font-size: 1.05rem;
    line-height: 1.5;
}

.dialog-footer {
    padding: 16px 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    background-color: rgba(0, 0, 0, 0.1);
}

.btn-cancel {
    background-color: rgba(255, 255, 255, 0.1);
    color: rgba(255, 255, 255, 0.9);
    border: 1px solid rgba(255, 255, 255, 0.2);
    padding: 10px 18px;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s;
    font-size: 0.95rem;
    font-weight: 500;
}

.btn-confirm {
    background-color: var(--primary-color);
    color: white;
    border: none;
    padding: 10px 18px;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s;
    font-size: 0.95rem;
    font-weight: 600;
    box-shadow: 0 2px 10px rgba(0, 168, 255, 0.3);
}

.btn-cancel:hover {
    background-color: rgba(255, 255, 255, 0.2);
    transform: translateY(-1px);
}

.btn-confirm:hover {
    background-color: #0095e8;
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(0, 168, 255, 0.4);
}