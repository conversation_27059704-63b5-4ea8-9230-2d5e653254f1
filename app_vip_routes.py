from flask import jsonify, request, session, redirect, url_for, flash
from vip_functions import get_all_vip_levels, upgrade_vip_level, calculate_discounted_price

# 添加到app.py中的导入部分
# from vip_functions import get_all_vip_levels, upgrade_vip_level, calculate_discounted_price, calculate_checkin_bonus, calculate_points_bonus

# 添加到app.py中的路由部分
@app.route('/vip_levels', methods=['GET'])
def vip_levels():
    """获取所有VIP等级信息"""
    vip_levels = get_all_vip_levels()
    return jsonify(vip_levels)

@app.route('/upgrade_vip', methods=['POST'])
def upgrade_vip():
    """升级VIP等级"""
    if 'steamid' not in session:
        flash('请先登录')
        return redirect(url_for('login'))
    
    steamid = session.get('steamid')
    data = request.get_json()
    target_level = data.get('level')
    
    if not target_level or not isinstance(target_level, int) or target_level < 1 or target_level > 9:
        return jsonify({
            'success': False,
            'message': '无效的VIP等级'
        }), 400
    
    success, message = upgrade_vip_level(steamid, target_level)
    
    if success:
        flash(message)
        return jsonify({
            'success': True,
            'message': message
        })
    else:
        flash(message)
        return jsonify({
            'success': False,
            'message': message
        }), 400

# 修改sign_in函数，考虑VIP等级的签到奖励
# 在app.py中找到sign_in函数，修改签到奖励计算部分
"""
# 原代码
reward_points = 1000  # 默认签到奖励

# 修改后的代码
base_reward = 1000  # 默认签到奖励
vip_level = user.get('vip_level', 0)
reward_points = calculate_checkin_bonus(base_reward, vip_level)
"""

# 修改购买物品的函数，考虑VIP等级的折扣
# 在app.py中找到purchase_item函数，修改价格计算部分
"""
# 原代码
item_price = item.get('points', 0)

# 修改后的代码
original_price = item.get('points', 0)
vip_level = user.get('vip_level', 0)
item_price = calculate_discounted_price(original_price, vip_level)
"""
