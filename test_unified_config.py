#!/usr/bin/env python3
"""
测试统一的等级奖励配置系统
"""

import requests
import json

BASE_URL = "http://127.0.0.1"

def test_level_reward_config():
    """测试等级奖励配置"""
    
    # 测试数据：配置5级奖励（积分+物品）
    test_data = {
        "level": 5,
        "points_reward": 2000,
        "item_id": "weapon_ak47",
        "item_name": "AK47突击步枪",
        "quantity": 1,
        "image_path": "/static/images/weapons/ak47.png"
    }
    
    print("🔧 测试统一等级奖励配置...")
    print(f"配置数据: {json.dumps(test_data, indent=2, ensure_ascii=False)}")
    
    try:
        # 需要登录session，这里简化测试
        session = requests.Session()
        
        # 发送配置请求
        response = session.post(
            f"{BASE_URL}/admin/battlepass/level-reward",
            json=test_data,
            headers={'Content-Type': 'application/json'}
        )
        
        print(f"\n状态码: {response.status_code}")
        print(f"响应: {response.text}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print("✅ 等级奖励配置成功!")
                print(f"消息: {result.get('message')}")
            else:
                print(f"❌ 配置失败: {result.get('message')}")
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 异常: {e}")

def test_get_level_config():
    """测试获取等级配置"""
    level = 5
    
    print(f"\n🔍 测试获取等级 {level} 配置...")
    
    try:
        session = requests.Session()
        
        response = session.get(f"{BASE_URL}/admin/battlepass/level/{level}")
        
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.text}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print("✅ 获取配置成功!")
                config = result.get('data', {})
                print(f"等级: {config.get('level')}")
                print(f"经验需求: {config.get('exp_required')}")
                print(f"积分奖励: {config.get('points_reward')}")
                if config.get('item_reward'):
                    item = config['item_reward']
                    print(f"物品奖励: {item.get('item_name')} x{item.get('quantity')}")
            else:
                print(f"❌ 获取失败: {result.get('message')}")
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 异常: {e}")

def main():
    print("🚀 统一等级奖励配置系统测试")
    print("=" * 50)
    
    # 1. 测试配置等级奖励
    test_level_reward_config()
    
    print("\n" + "-" * 30)
    
    # 2. 测试获取等级配置
    test_get_level_config()
    
    print("\n🎊 测试完成!")
    print("\n💡 提示:")
    print("- 现在只需要一个界面就能配置积分和物品奖励")
    print("- 经验需求自动计算（等级 × 1000）")
    print("- 支持编辑和更新现有配置")
    print("- 界面更加简洁和统一")

if __name__ == "__main__":
    main()
