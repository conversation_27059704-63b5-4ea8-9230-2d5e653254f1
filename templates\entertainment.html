{% extends "base.html" %}

{% block title %}娱乐中心 - 7788商城{% endblock %}

{% block content %}
<div class="entertainment-container">
    <div class="entertainment-header">
        <h1><i class="fas fa-dice"></i> 娱乐中心</h1>
        <p>在这里您可以参与各种有趣的活动，赢取积分奖励</p>
    </div>



    <!-- 游戏部分 -->
    <div class="games-section">
        <div class="section-header">
            <h2><i class="fas fa-gamepad"></i> 小游戏</h2>
            <p>参与有趣的小游戏，赢取积分奖励</p>
        </div>
        <div class="games-grid">
            <div class="game-card">
                <div class="game-icon">
                    <i class="fas fa-dice"></i>
                </div>
                <div class="game-info">
                    <h3>掷骰子</h3>
                    <p>掷出6点获得双倍积分，掷出3点返还部分积分</p>
                    <div class="game-cost">
                        <span>花费: 2000 积分</span>
                    </div>
                </div>
                <div class="game-action">
                    <div class="dice-buttons">
                        <button class="play-btn" id="dice-game-btn" data-game="dice" data-cost="2000" data-times="1">
                            <i class="fas fa-play"></i> 开始游戏
                        </button>
                        <button class="play-btn" id="dice-game-btn-10" data-game="dice" data-cost="20000" data-times="10">
                            <i class="fas fa-dice"></i> 摇 10 次
                        </button>
                        <button class="play-btn" id="dice-game-btn-100" data-game="dice" data-cost="200000" data-times="100">
                            <i class="fas fa-dice-d20"></i> 摇 100 次
                        </button>
                    </div>
                </div>
            </div>
            <div class="game-card">
                <div class="game-icon">
                    <i class="fas fa-gift"></i>
                </div>
                <div class="game-info">
                    <h3>抽奖</h3>
                    <p>试试你的运气，抽取稀有物品</p>
                    <div class="game-cost">
                        <span>花费固定积分获取随机物品</span>
                    </div>
                </div>
                <div class="game-action">
                    <a href="/lottery" class="play-btn">
                        <i class="fas fa-gift"></i> 进入抽奖
                    </a>
                </div>
            </div>
            <!-- 更多游戏卡片可以在这里添加 -->
        </div>
    </div>

    <!-- 游戏结果模态框 -->
    <div class="game-result-modal" id="gameResultModal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 id="gameResultTitle">游戏结果</h2>
                <span class="modal-close">&times;</span>
            </div>
            <div class="modal-body">
                <!-- 移除骰子动画 -->
                <div class="result-message" id="gameResultMessage">
                    <!-- 结果消息将通过JavaScript动态添加 -->
                </div>
            </div>
            <div class="modal-footer">
                <button class="play-again-btn" id="playAgainBtn">再玩一次</button>
                <button class="close-btn" id="closeResultBtn">关闭</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // 签到按钮
    const signInBtn = document.getElementById('sign-in-btn');
    if (signInBtn) {
        signInBtn.addEventListener('click', function() {
            // 发送AJAX请求进行签到
            fetch('/sign_in', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // 更新签到状态
                    const checkinStatus = document.querySelector('.checkin-status');
                    if (checkinStatus) {
                        checkinStatus.innerHTML = `
                            <div class="status-signed">
                                <i class="fas fa-check-circle"></i>
                                <span>今日已签到</span>
                            </div>
                            <div class="next-checkin">
                                <p>下次签到时间: ${new Date(new Date().setDate(new Date().getDate() + 1)).toISOString().split('T')[0]} 00:00</p>
                            </div>
                        `;
                    }

                    // 更新签到按钮
                    signInBtn.disabled = true;
                    signInBtn.innerHTML = '<i class="fas fa-check"></i> 已签到';
                    signInBtn.classList.add('signed');

                    // 更新连续签到天数
                    const streakDays = document.querySelector('.streak-days');
                    if (streakDays && data.checkin_streak) {
                        streakDays.textContent = data.checkin_streak;
                    }

                    // 更新用户积分显示
                    const pointsDisplay = document.querySelector('.nav-points span');
                    if (pointsDisplay && data.points) {
                        pointsDisplay.textContent = data.points;
                    }

                    // 显示成功消息
                    customAlert(data.message);
                } else {
                    // 显示错误消息
                    customAlert(data.message || '签到失败，请稍后再试');
                }
            })
            .catch(error => {
                console.error('签到请求失败:', error);
                customAlert('网络错误，请稍后重试');
            });
        });
    }

    // 骰子游戏按钮
    const diceGameBtn = document.getElementById('dice-game-btn');
    const diceGameBtn10 = document.getElementById('dice-game-btn-10');
    const diceGameBtn100 = document.getElementById('dice-game-btn-100');
    const diceButtons = [diceGameBtn, diceGameBtn10, diceGameBtn100];

    // 结果模态框元素
    const gameResultModal = document.getElementById('gameResultModal');
    const gameResultTitle = document.getElementById('gameResultTitle');
    const gameResultMessage = document.getElementById('gameResultMessage');
    const playAgainBtn = document.getElementById('playAgainBtn');
    const closeResultBtn = document.getElementById('closeResultBtn');
    const modalClose = document.querySelector('.modal-close');

    // 游戏状态变量
    let currentGame = null;
    let currentCost = 0;
    let currentTimes = 1;
    let lastClickedButton = null;

    // 显示游戏结果模态框
    function showGameResult(game, result, times) {
        gameResultTitle.textContent = game === 'dice' ? '掷骰子结果' : '游戏结果';

        if (game === 'dice') {
            // 根据次数显示不同的结果
            if (times === 1) {
                // 单次结果显示
                let resultMessage = '';
                if (result.success) {
                    resultMessage = `<div class="result-success">
                        <i class="fas fa-trophy"></i>
                        <p>恭喜！您掷出了 ${result.dice_result} 点，获得 ${result.reward} 积分！</p>
                        <p class="points-change">+${result.net_points} 积分</p>
                        <p class="current-points">当前积分: ${result.points_after}</p>
                    </div>`;
                } else {
                    resultMessage = `<div class="result-failure">
                        <i class="fas fa-times-circle"></i>
                        <p>很遗憾，您掷出了 ${result.dice_result} 点，失去 ${result.cost} 积分。</p>
                        <p class="points-change">-${Math.abs(result.net_points)} 积分</p>
                        <p class="current-points">当前积分: ${result.points_after}</p>
                    </div>`;
                }
                gameResultMessage.innerHTML = resultMessage;
            } else {
                // 批量结果显示
                const results = result.results;
                const summary = result.summary;

                // 创建结果表格
                let tableHtml = `
                <div class="dice-result-table-container">
                    <table class="dice-result-table">
                        <thead>
                            <tr>
                                <th>序号</th>
                                <th>点数</th>
                                <th>结果</th>
                                <th>积分变化</th>
                            </tr>
                        </thead>
                        <tbody>
                `;

                // 最多显示前10条结果
                const displayCount = Math.min(results.length, 10);
                for (let i = 0; i < displayCount; i++) {
                    const r = results[i];
                    const isWin = r.net_points > 0;
                    tableHtml += `
                        <tr>
                            <td>${i + 1}</td>
                            <td>${r.dice_result}</td>
                            <td class="${isWin ? 'win' : 'lose'}">${isWin ? '赢' : '输'}</td>
                            <td class="${isWin ? 'win' : 'lose'}">${isWin ? '+' : ''}${r.net_points}</td>
                        </tr>
                    `;
                }

                if (results.length > 10) {
                    tableHtml += `
                        <tr>
                            <td colspan="4">... 仅显示前10条结果 ...</td>
                        </tr>
                    `;
                }

                tableHtml += `
                        </tbody>
                    </table>
                </div>
                `;

                // 创建结果摘要
                const netResult = summary.total_win - summary.total_lose;
                const isPositive = netResult >= 0;

                let summaryHtml = `
                <div class="dice-result-summary">
                    <p>总共摇骰子: <strong>${times}</strong> 次</p>
                    <p>获胜次数: <strong>${summary.win_count}</strong> 次</p>
                    <p>失败次数: <strong>${summary.lose_count}</strong> 次</p>
                    <p>总赢取积分: <span class="total-win">+${summary.total_win}</span></p>
                    <p>总损失积分: <span class="total-lose">-${summary.total_lose}</span></p>
                    <p class="net-result ${isPositive ? 'positive' : 'negative'}">
                        净收益: ${isPositive ? '+' : ''}${netResult} 积分
                    </p>
                    <p>当前积分: ${summary.points_after}</p>
                </div>
                `;

                gameResultMessage.innerHTML = tableHtml + summaryHtml;
            }
        }

        // 更新用户积分显示
        const pointsDisplay = document.querySelector('.nav-points span');
        if (pointsDisplay) {
            pointsDisplay.textContent = times === 1 ? result.points_after : result.summary.points_after;
        }

        gameResultModal.style.display = 'flex';
    }

    // 关闭游戏结果模态框
    function closeGameResult() {
        gameResultModal.style.display = 'none';
    }

    // 处理骰子游戏按钮点击
    function handleDiceGameClick(e) {
        const button = e.currentTarget;
        currentGame = button.getAttribute('data-game');
        currentCost = parseInt(button.getAttribute('data-cost'));
        currentTimes = parseInt(button.getAttribute('data-times'));
        lastClickedButton = button;

        // 确认是否开始游戏
        let confirmMessage = `确定要花费 ${currentCost} 积分`;
        if (currentTimes > 1) {
            confirmMessage += ` 摇 ${currentTimes} 次骰子`;
        } else {
            confirmMessage += ` 玩掷骰子游戏`;
        }
        confirmMessage += `吗？`;

        customConfirm(confirmMessage, (confirmed) => {
            if (confirmed) {
                // 发送游戏请求
                // 注意：这里需要后端支持批量摇骰子功能
                // 如果后端还没有实现，这里会发送单次请求
                fetch('/dice_game', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: JSON.stringify({
                        game: currentGame,
                        cost: currentCost / currentTimes,  // 单次成本
                        times: currentTimes
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.message || (data.results && data.summary)) {
                        // 显示游戏结果
                        showGameResult(currentGame, data, currentTimes);
                    } else {
                        customAlert('游戏处理出错，请稍后再试');
                    }
                })
                .catch(error => {
                    console.error('游戏请求失败:', error);
                    customAlert('网络错误，请稍后重试');
                });
            }
        });
    }

    // 为所有骰子按钮添加点击事件
    diceButtons.forEach(button => {
        if (button) {
            button.addEventListener('click', handleDiceGameClick);
        }
    });

    // 再玩一次按钮点击事件
    if (playAgainBtn) {
        playAgainBtn.addEventListener('click', function() {
            closeGameResult();
            if (lastClickedButton) {
                lastClickedButton.click();
            }
        });
    }

    // 关闭按钮点击事件
    if (closeResultBtn) {
        closeResultBtn.addEventListener('click', closeGameResult);
    }

    // 模态框关闭按钮点击事件
    if (modalClose) {
        modalClose.addEventListener('click', closeGameResult);
    }

    // 点击模态框背景关闭
    gameResultModal.addEventListener('click', function(e) {
        if (e.target === this) {
            closeGameResult();
        }
    });

    // ESC键关闭模态框
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape' && gameResultModal.style.display === 'flex') {
            closeGameResult();
        }
    });
});
</script>
{% endblock %}
