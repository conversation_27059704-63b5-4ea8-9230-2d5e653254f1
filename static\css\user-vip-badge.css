/* 用户VIP徽章样式 */
.user-vip-badge {
    display: inline-flex;
    align-items: center;
    background: linear-gradient(135deg, #ffd700, #ffaa00);
    color: #000;
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 0.75rem;
    font-weight: bold;
    margin-left: 8px;
    box-shadow: 0 2px 5px rgba(255, 215, 0, 0.4);
    border: 1px solid rgba(255, 255, 255, 0.3);
    text-shadow: 0 1px 1px rgba(255, 255, 255, 0.5);
    vertical-align: middle;
}

.user-vip-badge i {
    margin-right: 3px;
    color: #ff6d00;
    font-size: 0.7rem;
}

/* 导航栏中的VIP图标 */
.nav-link .fa-crown {
    color: #ffd700;
    text-shadow: 0 0 5px rgba(255, 215, 0, 0.5);
}

/* 下拉菜单中的VIP选项 */
.dropdown-menu a .fa-crown {
    color: #ffd700;
    text-shadow: 0 0 3px rgba(255, 215, 0, 0.5);
}

/* 响应式调整 */
@media (max-width: 768px) {
    .user-vip-badge {
        font-size: 0.7rem;
        padding: 1px 4px;
    }
}
