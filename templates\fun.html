{% extends "layout.html" %}
{% block title %}娱乐功能 - SCUM物品代码网站{% endblock %}
{% block content %}
<div class="container mt-5">
    <div class="row justify-content-center mb-5">
        <div class="col-md-10 text-center">
            <h1 class="entertainment-title">游戏娱乐中心</h1>
            <p class="text-muted">在这里享受更多SCUM游戏乐趣</p>
        </div>
    </div>

    <div class="row mb-5">
        <div class="col-lg-6 mb-4">
            <div class="game-card shadow">
                <div class="game-card-header">
                    <i class="fas fa-dice"></i> 骰子游戏
                </div>
                <div class="game-card-body">
                    <div class="text-center mb-4">
                        <div class="dice-container" id="diceContainer">
                            <div class="dice" id="dice">
                                <div class="dice-face front">1</div>
                                <div class="dice-face back">6</div>
                                <div class="dice-face top">2</div>
                                <div class="dice-face bottom">5</div>
                                <div class="dice-face left">3</div>
                                <div class="dice-face right">4</div>
                            </div>
                        </div>
                        <div class="dice-result mt-3" id="diceResult">点击掷骰子开始游戏</div>
                    </div>
                    <div class="dice-controls">
                        <button class="btn btn-primary btn-block" id="rollDiceBtn" 
                            data-logged-in="{{ 'true' if user else 'false' }}" 
                            data-user-points="{{ user.points|default(0) if user else 0 }}"
                            data-login-url="{{ url_for('login') }}">
                            <i class="fas fa-dice"></i> 掷骰子
                        </button>
                    </div>
                    <div class="game-rules mt-3">
                        <h5>游戏规则:</h5>
                        <ul>
                            <li>点击"掷骰子"按钮开始游戏</li>
                            <li>每次游戏消耗2000积分</li>
                            <li>点数3: 奖励2500积分 (净收益500积分)</li>
                            <li>点数6: 奖励4000积分 (净收益2000积分)</li>
                            <li>其他点数: 无奖励 (损失2000积分)</li>
                        </ul>
                        <small class="text-muted">请确保您有足够的积分</small>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-6 mb-4">
            <div class="game-card shadow">
                <div class="game-card-header">
                    <i class="fas fa-gift"></i> 每日签到
                </div>
                <div class="game-card-body">
                    <div class="text-center mb-4">
                        <div class="checkin-status" id="checkinStatus">
                            {% if has_signed_today %}
                            <div class="checkin-complete">
                                <i class="fas fa-check-circle"></i>
                                <p>今日已签到</p>
                                <div class="checkin-reward">获得 <span>1000</span> 积分</div>
                            </div>
                            {% else %}
                            <div class="checkin-available">
                                <i class="fas fa-calendar-check"></i>
                                <p>今日未签到</p>
                                <div class="checkin-reward">可获得 <span>1000</span> 积分</div>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                    <div class="checkin-controls">
                        <button class="btn btn-success btn-block" id="checkinBtn" 
                            {% if has_signed_today %}disabled{% endif %}>
                            <i class="fas fa-calendar-check"></i> 
                            {% if has_signed_today %}已签到{% else %}立即签到{% endif %}
                        </button>
                    </div>
                    <div class="checkin-calendar mt-4">
                        <h5>签到日历:</h5>
                        <div class="calendar-grid">
                            {% for i in range(1, 31) %}
                            <div class="calendar-day {% if i <= user.checkin_days|default(0) %}checked{% endif %}">
                                {{ i }}
                            </div>
                            {% endfor %}
                        </div>
                        <div class="checkin-streak mt-2">
                            <span>连续签到: <strong>{{ user.checkin_streak|default(0) }}</strong> 天</span>
                        </div>
                        <div class="checkin-rules mt-3">
                            <h6>签到规则:</h6>
                            <ul class="small">
                                <li>每日签到可获得1000积分</li>
                                <li>连续签到10天可额外获得1000积分</li>
                                <li>中断签到后连续天数将重置</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row mb-5">
        <div class="col-12">
            <div class="game-card shadow">
                <div class="game-card-header">
                    <i class="fas fa-trophy"></i> 积分排行榜
                </div>
                <div class="game-card-body">
                    <div class="leaderboard">
                        <div class="leaderboard-header">
                            <div class="leaderboard-rank">排名</div>
                            <div class="leaderboard-user">玩家</div>
                            <div class="leaderboard-points">积分</div>
                            <div class="leaderboard-kills">击杀数</div>
                        </div>
                        <div class="leaderboard-body">
                            <!-- 排行榜数据 -->
                            {% for i in range(1, 11) %}
                            <div class="leaderboard-row {% if user and user.username == top_users[i-1].username %}current-user{% endif %}">
                                <div class="leaderboard-rank">
                                    {% if i <= 3 %}
                                    <div class="top-rank rank-{{ i }}">{{ i }}</div>
                                    {% else %}
                                    <div class="normal-rank">{{ i }}</div>
                                    {% endif %}
                                </div>
                                <div class="leaderboard-user">
                                    {% if user and user.username == top_users[i-1].username %}
                                    <span class="current-user-name">{{ top_users[i-1].username if top_users|length >= i else '玩家' ~ i }}</span>
                                    <span class="current-user-tag">您</span>
                                    {% else %}
                                    {{ top_users[i-1].username if top_users|length >= i else '玩家' ~ i }}
                                    {% endif %}
                                    
                                    {% if top_users[i-1].is_vip|default(false) %}
                                    <span class="vip-badge"><i class="fas fa-crown"></i> VIP</span>
                                    {% endif %}
                                </div>
                                <div class="leaderboard-points">
                                    <span class="points-value">{{ top_users[i-1].points|default(1000 - i * 50)|format_number }}</span>
                                    <div class="points-bar" style="width: {% if top_users[0].points|default(1000) > 0 %}{{ ((top_users[i-1].points|default(1000 - i * 50))/(top_users[0].points|default(1000))*100)|round }}{% else %}0{% endif %}%"></div>
                                </div>
                                <div class="leaderboard-kills">
                                    <div class="kills-value">{{ top_users[i-1].kills|default(100 - i * 8) }}</div>
                                    <div class="kills-bar" style="height: {% if top_users[0].kills|default(100) > 0 %}{{ ((top_users[i-1].kills|default(100 - i * 8))/(top_users[0].kills|default(100))*100)|round }}{% else %}0{% endif %}%"></div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row mb-5">
        <div class="col-md-6 mb-4">
            <div class="game-card shadow">
                <div class="game-card-header">
                    <i class="fas fa-bullseye"></i> 即将推出
                </div>
                <div class="game-card-body">
                    <div class="coming-soon text-center py-4">
                        <i class="fas fa-hourglass-half mb-3"></i>
                        <h4>猜数字游戏</h4>
                        <p>即将上线，敬请期待！</p>
                        <div class="progress mt-3">
                            <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 70%" aria-valuenow="70" aria-valuemin="0" aria-valuemax="100"></div>
                        </div>
                        <small class="text-muted mt-2 d-block">开发进度: 70%</small>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-6 mb-4">
            <div class="game-card shadow">
                <div class="game-card-header">
                    <i class="fas fa-bullseye"></i> 即将推出
                </div>
                <div class="game-card-body">
                    <div class="coming-soon text-center py-4">
                        <i class="fas fa-hourglass-half mb-3"></i>
                        <h4>幸运转盘</h4>
                        <p>即将上线，敬请期待！</p>
                        <div class="progress mt-3">
                            <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 40%" aria-valuenow="40" aria-valuemin="0" aria-valuemax="100"></div>
                        </div>
                        <small class="text-muted mt-2 d-block">开发进度: 40%</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // 骰子游戏逻辑
    const diceContainer = document.getElementById('diceContainer');
    const dice = document.getElementById('dice');
    const diceResult = document.getElementById('diceResult');
    const rollDiceBtn = document.getElementById('rollDiceBtn');
    
    // 是否正在掷骰子
    let isRolling = false;
    
    rollDiceBtn.addEventListener('click', function() {
        if (isRolling) return;
        
        // 检查用户是否登录
        const isUserLoggedIn = this.dataset.loggedIn === 'true';
        const userPoints = parseInt(this.dataset.userPoints || 0);
        const loginUrl = this.dataset.loginUrl;
        
        if (!isUserLoggedIn) {
            // 使用自定义弹窗代替alert
            showNotificationPopup('请先登录', '请先登录再进行游戏', 'warning', '<a href="' + loginUrl + '" class="notification-login-btn">立即登录</a>');
            return;
        }
        
        // 检查积分是否足够
        if (userPoints < 2000) {
            // 不再使用alert，而是显示漂亮的自定义弹窗
            showNotificationPopup('积分不足', '您的积分不足，无法进行游戏', 'error', '当前积分: ' + userPoints + '<br>需要积分: 2000');
            return;
        }
        
        isRolling = true;
        rollDiceBtn.disabled = true;
        diceResult.textContent = '骰子正在转动...';
        diceResult.className = 'dice-result mt-3';
        
        // 添加动画类
        dice.classList.add('rolling');
        
        // 发送掷骰子请求到服务器
        fetch('/dice_game', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            credentials: 'same-origin'
        })
        .then(response => response.json())
        .then(data => {
            // 动画持续一段时间后显示结果
            setTimeout(function() {
                // 移除动画类
                dice.classList.remove('rolling');
                
                // 设置骰子的最终朝向，让对应点数的面朝上
                let finalRotation = "";
                switch(data.dice_result) {
                    case 1: finalRotation = "rotateX(0deg) rotateY(0deg)"; break;     // 前面朝上 (1)
                    case 6: finalRotation = "rotateX(180deg) rotateY(0deg)"; break;   // 后面朝上 (6)
                    case 2: finalRotation = "rotateX(90deg) rotateY(0deg)"; break;    // 顶面朝上 (2)
                    case 5: finalRotation = "rotateX(-90deg) rotateY(0deg)"; break;   // 底面朝上 (5)  
                    case 3: finalRotation = "rotateY(-90deg) rotateX(0deg)"; break;   // 左面朝上 (3)
                    case 4: finalRotation = "rotateY(90deg) rotateX(0deg)"; break;    // 右面朝上 (4)
                }
                dice.style.transform = finalRotation;
                
                // 更新骰子上的数字显示
                document.querySelectorAll('.dice-face').forEach(face => {
                    if (face.classList.contains('front')) face.textContent = '1';
                    if (face.classList.contains('back')) face.textContent = '6';
                    if (face.classList.contains('top')) face.textContent = '2';
                    if (face.classList.contains('bottom')) face.textContent = '5';
                    if (face.classList.contains('left')) face.textContent = '3';
                    if (face.classList.contains('right')) face.textContent = '4';
                });
                
                // 更新结果显示
                if (data.success) {
                    diceResult.innerHTML = `恭喜！掷出了 <strong>${data.dice_result}</strong> 点，获得 <strong>${data.reward}</strong> 积分！`;
                    diceResult.classList.add('success');
                } else {
                    diceResult.innerHTML = `很遗憾，掷出了 <strong>${data.dice_result}</strong> 点，损失了 <strong>${-data.net_points}</strong> 积分`;
                    diceResult.classList.add('failure');
                }
                
                // 显示结果弹窗
                showResultPopup(data);
                
                // 更新用户积分显示（如果页面上有显示）
                const pointsDisplay = document.querySelector('.nav-points span');
                if (pointsDisplay) {
                    // 使用后端返回的精确积分数据
                    const newPoints = data.points_after;
                    
                    // 更新显示
                    pointsDisplay.textContent = newPoints.toLocaleString();
                    
                    // 同时更新按钮的data-user-points属性，确保下次游戏使用更新后的积分值
                    rollDiceBtn.dataset.userPoints = newPoints;
                    
                    // 调试日志
                    console.log(`积分更新: ${data.points_before} → ${data.points_after} (${data.net_points > 0 ? '+' : ''}${data.net_points})`);
                }
                
                // 重置状态
                setTimeout(function() {
                    isRolling = false;
                    rollDiceBtn.disabled = false;
                }, 500);
            }, 2000);
        })
        .catch(error => {
            console.error('骰子游戏出错:', error);
            dice.classList.remove('rolling');
            diceResult.innerHTML = `出错了，请重试`;
            diceResult.classList.add('failure');
            isRolling = false;
            rollDiceBtn.disabled = false;
        });
    });
    
    // 签到功能
    const checkinBtn = document.getElementById('checkinBtn');
    
    if (checkinBtn) {
        checkinBtn.addEventListener('click', function() {
            // 发送签到请求
            checkinBtn.disabled = true;
            checkinBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 处理中...';
            
            // 发送实际的签到请求
            fetch('/sign_in', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                credentials: 'same-origin'
            })
            .then(response => {
                // 操作完成后刷新页面显示结果
                window.location.reload();
            })
            .catch(error => {
                console.error('签到请求出错:', error);
                checkinBtn.disabled = false;
                checkinBtn.innerHTML = '<i class="fas fa-calendar-check"></i> 立即签到';
                alert('签到失败，请稍后再试');
            });
        });
    }
});

// 显示结果弹窗
function showResultPopup(data) {
    // 检查是否已有弹窗，如果有则移除
    let existingPopup = document.querySelector('.result-popup');
    if (existingPopup) {
        document.body.removeChild(existingPopup);
    }
    
    // 使用后端返回的精确积分数据
    const updatedPoints = data.points_after;
    
    // 创建新弹窗
    const popup = document.createElement('div');
    popup.className = 'result-popup';
    
    // 设置弹窗内容
    popup.innerHTML = `
        <div class="dice-number">${data.dice_result}</div>
        <div class="result-message">${data.success ? '恭喜中奖！' : '很遗憾，未中奖'}</div>
        <div class="points-change">
            ${data.success 
                ? `奖励: <strong style="color:#28a745">+${data.reward}</strong> 积分<br>净收益: <strong style="color:#28a745">+${data.net_points}</strong> 积分` 
                : `消耗: <strong style="color:#dc3545">-${data.cost}</strong> 积分`}
        </div>
        <div class="current-points">
            当前积分: <strong>${updatedPoints}</strong>
        </div>
        <button class="close-btn">确定</button>
    `;
    
    // 添加到页面
    document.body.appendChild(popup);
    
    // 显示弹窗
    setTimeout(() => {
        popup.classList.add('show');
    }, 100);
    
    // 关闭按钮事件
    popup.querySelector('.close-btn').addEventListener('click', function() {
        popup.classList.remove('show');
        setTimeout(() => {
            document.body.removeChild(popup);
        }, 300);
    });
    
    // 点击外部也可关闭
    popup.addEventListener('click', function(e) {
        if (e.target === popup) {
            popup.classList.remove('show');
            setTimeout(() => {
                document.body.removeChild(popup);
            }, 300);
        }
    });
}

// 显示通知弹窗（可用于积分不足、未登录等情况）
function showNotificationPopup(title, message, type, detail = '') {
    // 检查是否已有弹窗，如果有则移除
    let existingPopup = document.querySelector('.notification-popup');
    if (existingPopup) {
        document.body.removeChild(existingPopup);
    }
    
    // 创建新弹窗
    const popup = document.createElement('div');
    popup.className = 'notification-popup ' + type;
    
    // 设置图标
    let icon = 'fa-exclamation-circle';
    if (type === 'success') icon = 'fa-check-circle';
    if (type === 'error') icon = 'fa-times-circle';
    if (type === 'warning') icon = 'fa-exclamation-triangle';
    
    // 设置弹窗内容
    popup.innerHTML = `
        <div class="notification-icon">
            <i class="fas ${icon}"></i>
        </div>
        <div class="notification-title">${title}</div>
        <div class="notification-message">${message}</div>
        ${detail ? `<div class="notification-detail">${detail}</div>` : ''}
        <button class="notification-close-btn">确定</button>
    `;
    
    // 添加到页面
    document.body.appendChild(popup);
    
    // 显示弹窗
    setTimeout(() => {
        popup.classList.add('show');
    }, 100);
    
    // 关闭按钮事件
    popup.querySelector('.notification-close-btn').addEventListener('click', function() {
        popup.classList.remove('show');
        setTimeout(() => {
            document.body.removeChild(popup);
        }, 300);
    });
}
</script>

<style>
/* 娱乐页面样式 */
.entertainment-title {
    color: var(--primary-color);
    font-weight: 700;
    margin-bottom: 1rem;
}

.game-card {
    background: #1e1e1e;
    border-radius: 10px;
    overflow: hidden;
    height: 100%;
}

.game-card-header {
    background: linear-gradient(135deg, #2a2a2a, #1a1a1a);
    color: var(--primary-color);
    padding: 15px;
    font-size: 1.2rem;
    font-weight: 600;
    border-bottom: 1px solid #333;
}

.game-card-header i {
    margin-right: 8px;
}

.game-card-body {
    padding: 20px;
}

/* 骰子游戏样式 */
.dice-container {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 150px;
    perspective: 800px;
}

.dice {
    width: 90px; 
    height: 90px;
    position: relative;
    transform-style: preserve-3d;
    transition: transform 1s ease;
    margin: 0 auto;
}

.dice.rolling {
    animation: rollDice 0.8s linear infinite;
}

@keyframes rollDice {
    0% { transform: rotateX(0deg) rotateY(0deg) rotateZ(0deg); }
    16.6% { transform: rotateX(60deg) rotateY(45deg) rotateZ(45deg); }
    33.2% { transform: rotateX(120deg) rotateY(90deg) rotateZ(90deg); }
    49.8% { transform: rotateX(180deg) rotateY(135deg) rotateZ(135deg); }
    66.4% { transform: rotateX(240deg) rotateY(180deg) rotateZ(180deg); }
    83% { transform: rotateX(300deg) rotateY(225deg) rotateZ(225deg); }
    100% { transform: rotateX(360deg) rotateY(270deg) rotateZ(270deg); }
}

.dice-face {
    position: absolute;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #2a72ef, #1f57c3);
    color: white;
    font-size: 2.5rem;
    font-weight: bold;
    border-radius: 10px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.3);
    border: 2px solid rgba(255, 255, 255, 0.2);
    backface-visibility: hidden;
}

.dice-face.front { transform: translateZ(40px); }
.dice-face.back { transform: rotateY(180deg) translateZ(40px); }
.dice-face.top { transform: rotateX(90deg) translateZ(40px); }
.dice-face.bottom { transform: rotateX(-90deg) translateZ(40px); }
.dice-face.left { transform: rotateY(-90deg) translateZ(40px); }
.dice-face.right { transform: rotateY(90deg) translateZ(40px); }

.dice-result {
    font-size: 1.1rem;
    color: var(--text-primary);
    min-height: 60px;
    padding: 8px;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.dice-result.success {
    background-color: rgba(40, 167, 69, 0.15);
    color: #28a745;
    font-weight: bold;
    border: 1px solid rgba(40, 167, 69, 0.3);
}

.dice-result.failure {
    background-color: rgba(220, 53, 69, 0.15);
    color: #dc3545;
    font-weight: bold;
    border: 1px solid rgba(220, 53, 69, 0.3);
}

/* 结果弹窗 */
.result-popup {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%) scale(0.9);
    background: linear-gradient(to bottom, #2a2a2a, #212121);
    padding: 30px;
    border-radius: 15px;
    box-shadow: 0 0 40px rgba(0, 0, 0, 0.4), 0 0 80px rgba(42, 114, 239, 0.2);
    border: 1px solid #333;
    z-index: 1000;
    text-align: center;
    color: #f1f1f1;
    min-width: 320px;
    opacity: 0;
    visibility: hidden;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.result-popup.show {
    opacity: 1;
    visibility: visible;
    transform: translate(-50%, -50%) scale(1);
}

.result-popup .dice-number {
    font-size: 6rem;
    font-weight: bold;
    margin: 15px 0;
    color: #2a72ef;
    text-shadow: 0 0 15px rgba(42, 114, 239, 0.5);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { text-shadow: 0 0 15px rgba(42, 114, 239, 0.3); }
    50% { text-shadow: 0 0 25px rgba(42, 114, 239, 0.5); }
    100% { text-shadow: 0 0 15px rgba(42, 114, 239, 0.3); }
}

.result-popup .result-message {
    font-size: 1.8rem;
    margin: 20px 0;
    font-weight: bold;
    background: -webkit-linear-gradient(#ffffff, #a0a0a0);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.result-popup .points-change {
    font-size: 1.3rem;
    margin-bottom: 15px;
    line-height: 1.6;
}

.result-popup .current-points {
    font-size: 1.3rem;
    margin: 20px 0;
    padding: 10px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 10px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.result-popup .close-btn {
    background: linear-gradient(to bottom, #2a72ef, #1f57c3);
    color: white;
    border: none;
    padding: 10px 28px;
    border-radius: 8px;
    cursor: pointer;
    font-weight: bold;
    font-size: 1.1rem;
    transition: all 0.3s ease;
    margin-top: 15px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.result-popup .close-btn:hover {
    background: linear-gradient(to bottom, #3a82ff, #2a67d3);
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.4);
}

/* 签到样式 */
.checkin-status {
    min-height: 150px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.checkin-available i,
.checkin-complete i {
    font-size: 3rem;
    margin-bottom: 10px;
}

.checkin-available i {
    color: #ffc107;
}

.checkin-complete i {
    color: #28a745;
}

.checkin-reward {
    margin-top: 10px;
    font-size: 0.9rem;
    color: var(--text-secondary);
}

.checkin-reward span {
    color: var(--primary-color);
    font-weight: bold;
    font-size: 1.2rem;
}

.calendar-grid {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 5px;
    margin-top: 10px;
}

.calendar-day {
    width: 100%;
    aspect-ratio: 1/1;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #2a2a2a;
    border-radius: 5px;
    font-size: 0.85rem;
}

.calendar-day.checked {
    background-color: rgba(0, 168, 255, 0.2);
    color: var(--primary-color);
    font-weight: bold;
    border: 1px solid rgba(0, 168, 255, 0.5);
}

.checkin-streak {
    text-align: right;
    font-size: 0.9rem;
    color: var(--text-secondary);
}

/* 排行榜样式 */
.leaderboard {
    width: 100%;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 0 10px rgba(0,0,0,0.1);
}

.leaderboard-header,
.leaderboard-row {
    display: grid;
    grid-template-columns: 80px 1fr 200px 150px;
    padding: 15px;
    align-items: center;
}

.leaderboard-header {
    background: #5a5a5a;
    font-weight: 600;
    color: #e0e0e0;
    border-bottom: 1px solid #444;
    letter-spacing: 0.5px;
    font-size: 0.9rem;
    text-transform: uppercase;
}

.leaderboard-row {
    background-color: #262626;
    margin-bottom: 8px;
    transition: all 0.3s ease;
    border-radius: 8px;
    border-left: 4px solid transparent;
    position: relative;
    overflow: hidden;
}

.leaderboard-row:hover {
    transform: translateY(-2px);
    background-color: #2d2d2d;
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

.leaderboard-row.current-user {
    background: linear-gradient(90deg, rgba(42, 114, 239, 0.1), rgba(42, 114, 239, 0.05));
    border-left: 4px solid #2a72ef;
    box-shadow: 0 5px 15px rgba(42, 114, 239, 0.2);
}

.normal-rank {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #383838;
    font-weight: 600;
    color: #bbb;
}

.top-rank {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    box-shadow: 0 4px 10px rgba(0,0,0,0.2);
    font-size: 1.1rem;
}

.rank-1 {
    background: linear-gradient(135deg, #ffd700, #e6c200);
    color: #7d6800;
    animation: pulse-gold 2s infinite;
}

.rank-2 {
    background: linear-gradient(135deg, #e0e0e0, #b5b5b5);
    color: #666;
}

.rank-3 {
    background: linear-gradient(135deg, #cd7f32, #a56628);
    color: #7d4e20;
}

@keyframes pulse-gold {
    0% { box-shadow: 0 0 0 0 rgba(255, 215, 0, 0.6); }
    70% { box-shadow: 0 0 0 10px rgba(255, 215, 0, 0); }
    100% { box-shadow: 0 0 0 0 rgba(255, 215, 0, 0); }
}

.leaderboard-user {
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 10px;
}

.current-user-name {
    font-weight: 700;
    color: #2a72ef;
}

.current-user-tag {
    background-color: #2a72ef;
    color: white;
    padding: 2px 8px;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 700;
    letter-spacing: 0.5px;
}

.vip-badge {
    display: inline-flex;
    align-items: center;
    gap: 4px;
    padding: 3px 8px;
    background: linear-gradient(135deg, #ffd700, #e6c200);
    color: #7d6800;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 700;
}

.vip-badge i {
    font-size: 0.85rem;
}

.leaderboard-points {
    position: relative;
    display: flex;
    align-items: center;
}

.points-value {
    position: relative;
    z-index: 2;
    font-weight: 600;
    background: linear-gradient(90deg, #ffffff, #bbbbbb);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    padding-right: 10px;
}

.points-bar {
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    background: linear-gradient(90deg, rgba(42, 114, 239, 0.2), rgba(42, 114, 239, 0.05));
    z-index: 1;
    border-radius: 0 4px 4px 0;
    max-width: 100%;
}

.leaderboard-kills {
    display: flex;
    align-items: flex-end;
    justify-content: center;
    height: 40px;
    position: relative;
}

.kills-value {
    font-weight: 600;
    color: #aaa;
    margin-right: 10px;
}

.kills-bar {
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 6px;
    background: linear-gradient(to top, #dc3545, #ff6b80);
    border-radius: 3px 3px 0 0;
}

/* 即将推出游戏样式 */
.coming-soon i {
    font-size: 2.5rem;
    color: var(--text-secondary);
    opacity: 0.7;
}

.coming-soon h4 {
    color: var(--primary-color);
}

/* 通知弹窗样式 */
.notification-popup {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%) scale(0.9);
    background: linear-gradient(to bottom, #2a2a2a, #212121);
    padding: 30px;
    border-radius: 15px;
    border: 1px solid #333;
    box-shadow: 0 0 40px rgba(0, 0, 0, 0.4);
    z-index: 1000;
    text-align: center;
    color: #f1f1f1;
    min-width: 320px;
    opacity: 0;
    visibility: hidden;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.notification-popup.show {
    opacity: 1;
    visibility: visible;
    transform: translate(-50%, -50%) scale(1);
}

.notification-popup.error {
    box-shadow: 0 0 40px rgba(0, 0, 0, 0.4), 0 0 60px rgba(220, 53, 69, 0.2);
    border-top: 4px solid #dc3545;
}

.notification-popup.success {
    box-shadow: 0 0 40px rgba(0, 0, 0, 0.4), 0 0 60px rgba(40, 167, 69, 0.2);
    border-top: 4px solid #28a745;
}

.notification-popup.warning {
    box-shadow: 0 0 40px rgba(0, 0, 0, 0.4), 0 0 60px rgba(255, 193, 7, 0.2);
    border-top: 4px solid #ffc107;
}

.notification-icon {
    font-size: 3rem;
    margin-bottom: 15px;
}

.notification-popup.error .notification-icon {
    color: #dc3545;
}

.notification-popup.success .notification-icon {
    color: #28a745;
}

.notification-popup.warning .notification-icon {
    color: #ffc107;
}

.notification-title {
    font-size: 1.8rem;
    font-weight: bold;
    margin-bottom: 10px;
}

.notification-message {
    font-size: 1.2rem;
    margin-bottom: 20px;
}

.notification-detail {
    background: rgba(255, 255, 255, 0.05);
    padding: 12px;
    border-radius: 8px;
    margin-bottom: 20px;
    font-size: 1.1rem;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.notification-close-btn {
    background: linear-gradient(to bottom, #2a72ef, #1f57c3);
    color: white;
    border: none;
    padding: 10px 28px;
    border-radius: 8px;
    cursor: pointer;
    font-weight: bold;
    font-size: 1.1rem;
    transition: all 0.3s ease;
    margin-top: 15px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.notification-close-btn:hover {
    background: linear-gradient(to bottom, #3a82ff, #2a67d3);
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.4);
}

.notification-login-btn {
    display: inline-block;
    background: linear-gradient(to bottom, #28a745, #218838);
    color: white;
    padding: 8px 20px;
    border-radius: 5px;
    text-decoration: none;
    font-weight: bold;
    transition: all 0.3s ease;
    margin-top: 10px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.notification-login-btn:hover {
    background: linear-gradient(to bottom, #2dbc4e, #27953d);
    transform: translateY(-2px);
    color: white;
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.3);
}
</style>
{% endblock %} 