#!/usr/bin/env python3
import pymysql

conn = pymysql.connect(
    host='localhost', 
    user='root', 
    password='123456', 
    database='scum', 
    charset='utf8mb4', 
    cursorclass=pymysql.cursors.DictCursor
)

try:
    with conn.cursor() as cursor:
        # 检查领取记录
        cursor.execute(
            'SELECT * FROM battlepass_reward_claims WHERE steamid=%s ORDER BY claimed_at DESC LIMIT 10', 
            ('76561199149715992',)
        )
        results = cursor.fetchall()
        print(f'Found {len(results)} claim records:')
        for r in results:
            print(f'Level: {r["level"]}, Item: {r["item_name"]}, Time: {r["claimed_at"]}')
        
        print("\n" + "="*50)
        
        # 检查用户通行证进度
        cursor.execute(
            'SELECT * FROM user_battlepass WHERE steamid=%s', 
            ('76561199149715992',)
        )
        battlepass = cursor.fetchone()
        if battlepass:
            print(f'User Level: {battlepass["current_level"]}, Total EXP: {battlepass["total_exp"]}')
        else:
            print('No battlepass record found')
            
finally:
    conn.close()
