/* VIP购买卡片样式 */
.vip-purchase-section {
    margin-top: 30px;
    background-color: rgba(0, 0, 0, 0.05);
    padding: 20px;
    border-radius: 8px;
}

.vip-purchase-section h4 {
    font-size: 1.2rem;
    margin-bottom: 15px;
    color: var(--text-primary);
    text-align: center;
}

.vip-purchase-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
    gap: 15px;
}

.vip-purchase-card {
    background-color: var(--card-bg);
    border-radius: 8px;
    overflow: hidden;
    box-shadow: var(--box-shadow);
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
}

.vip-purchase-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
}

.vip-purchase-card.owned {
    border: 2px solid #4caf50;
}

.vip-purchase-card.next-level {
    border: 2px solid var(--primary-color);
}

.vip-level-header {
    background: linear-gradient(135deg, #333, #222);
    padding: 10px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.vip-level-header h5 {
    font-size: 1.1rem;
    color: white;
    margin: 0;
}

.level-badge {
    font-size: 0.7rem;
    padding: 3px 6px;
    border-radius: 20px;
    display: flex;
    align-items: center;
}

.level-badge i {
    margin-right: 3px;
    font-size: 0.7rem;
}

.level-badge.owned {
    background-color: #4caf50;
    color: white;
}

.level-badge.next {
    background-color: var(--primary-color);
    color: white;
}

.vip-level-price {
    padding: 10px;
    text-align: center;
    font-size: 1.1rem;
    font-weight: 700;
    color: #ff5722;
    background-color: rgba(0, 0, 0, 0.05);
}

.vip-level-benefits {
    padding: 10px;
    display: flex;
    flex-direction: column;
    gap: 5px;
    font-size: 0.9rem;
    color: var(--text-secondary);
}

.vip-buy-btn {
    margin-top: auto;
    padding: 8px 0;
    border: none;
    font-size: 0.9rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.vip-buy-btn.upgrade {
    background: linear-gradient(135deg, #ff9800, #ff5722);
    color: white;
}

.vip-buy-btn.upgrade:hover {
    background: linear-gradient(135deg, #ff5722, #ff9800);
}

.vip-buy-btn.owned {
    background-color: #4caf50;
    color: white;
    cursor: default;
}

.vip-buy-btn.locked {
    background-color: #9e9e9e;
    color: white;
    cursor: not-allowed;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .vip-purchase-grid {
        grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
    }
    
    .vip-level-header h5 {
        font-size: 1rem;
    }
    
    .level-badge {
        font-size: 0.6rem;
        padding: 2px 4px;
    }
    
    .vip-level-price {
        font-size: 1rem;
    }
    
    .vip-level-benefits {
        font-size: 0.8rem;
    }
    
    .vip-buy-btn {
        font-size: 0.8rem;
        padding: 6px 0;
    }
}
