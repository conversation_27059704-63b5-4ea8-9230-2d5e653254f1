{% extends "base.html" %}

{% block title %}通行证管理 - 7788商城{% endblock %}

{% block head_extra %}
<style>
    .admin-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 20px;
    }

    .admin-header {
        background: var(--card-bg);
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 30px;
        border: 1px solid var(--border-color);
    }

    .admin-section {
        background: var(--card-bg);
        border-radius: 10px;
        padding: 25px;
        margin-bottom: 30px;
        border: 1px solid var(--border-color);
    }

    .section-title {
        font-size: 1.5rem;
        font-weight: bold;
        margin-bottom: 20px;
        color: var(--text-primary);
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .form-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 20px;
        margin-bottom: 20px;
    }

    .form-group {
        display: flex;
        flex-direction: column;
        gap: 5px;
    }

    .form-group label {
        font-weight: bold;
        color: var(--text-primary);
    }

    .form-group input,
    .form-group select {
        padding: 10px;
        border: 1px solid var(--border-color);
        border-radius: 5px;
        background: var(--bg-primary);
        color: var(--text-primary);
    }

    .btn {
        padding: 10px 20px;
        border: none;
        border-radius: 5px;
        cursor: pointer;
        font-weight: bold;
        transition: all 0.3s ease;
    }

    .btn-primary {
        background: var(--accent-color);
        color: white;
    }

    .btn-primary:hover {
        background: #5a6fd8;
    }

    .btn-success {
        background: var(--success-color);
        color: white;
    }

    .btn-danger {
        background: var(--error-color);
        color: white;
    }

    .table-container {
        overflow-x: auto;
        margin-top: 20px;
    }

    .admin-table {
        width: 100%;
        border-collapse: collapse;
        background: var(--bg-primary);
    }

    .admin-table th,
    .admin-table td {
        padding: 12px;
        text-align: left;
        border-bottom: 1px solid var(--border-color);
    }

    .admin-table th {
        background: var(--bg-secondary);
        font-weight: bold;
        color: var(--text-primary);
    }

    .admin-table td {
        color: var(--text-secondary);
    }

    .status-enabled {
        color: var(--success-color);
        font-weight: bold;
    }

    .status-disabled {
        color: var(--error-color);
        font-weight: bold;
    }

    .reward-item {
        background: var(--bg-secondary);
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 10px;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .reward-info {
        flex: 1;
    }

    .reward-actions {
        display: flex;
        gap: 10px;
    }

    .alert {
        padding: 15px;
        border-radius: 5px;
        margin-bottom: 20px;
        display: none;
    }

    .alert-success {
        background: rgba(76, 175, 80, 0.1);
        border: 1px solid var(--success-color);
        color: var(--success-color);
    }

    .alert-error {
        background: rgba(244, 67, 54, 0.1);
        border: 1px solid var(--error-color);
        color: var(--error-color);
    }

    @media (max-width: 768px) {
        .admin-container {
            padding: 15px;
        }

        .form-grid {
            grid-template-columns: 1fr;
        }

        .reward-item {
            flex-direction: column;
            gap: 15px;
            text-align: center;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="admin-container">
    <!-- 页面标题 -->
    <div class="admin-header">
        <h1><i class="fas fa-trophy"></i> 通行证系统管理</h1>
        <p>管理通行证等级、奖励和系统配置</p>
    </div>

    <!-- 消息提示 -->
    <div id="alert-success" class="alert alert-success">
        <span id="success-message"></span>
    </div>
    <div id="alert-error" class="alert alert-error">
        <span id="error-message"></span>
    </div>

    <!-- 系统配置 -->
    <div class="admin-section">
        <h2 class="section-title">
            <i class="fas fa-cog"></i>
            系统配置
        </h2>
        
        <form id="config-form">
            <div class="form-grid">
                <div class="form-group">
                    <label for="max_level">最高等级</label>
                    <input type="number" id="max_level" name="max_level" value="{{ config.max_level }}" min="1" max="100">
                </div>
                <div class="form-group">
                    <label for="daily_quest_limit">每日任务限制</label>
                    <input type="number" id="daily_quest_limit" name="daily_quest_limit" value="{{ config.daily_quest_limit }}" min="1" max="100">
                </div>
                <div class="form-group">
                    <label for="quest_exp">单次任务经验</label>
                    <input type="number" id="quest_exp" name="quest_exp" value="{{ config.quest_exp }}" min="1" step="100">
                </div>
                <div class="form-group">
                    <label for="enabled">系统状态</label>
                    <select id="enabled" name="enabled">
                        <option value="true" {% if config.enabled %}selected{% endif %}>启用</option>
                        <option value="false" {% if not config.enabled %}selected{% endif %}>禁用</option>
                    </select>
                </div>
            </div>
            <button type="submit" class="btn btn-primary">
                <i class="fas fa-save"></i> 保存配置
            </button>
        </form>
    </div>

    <!-- 等级配置 -->
    <div class="admin-section">
        <h2 class="section-title">
            <i class="fas fa-layer-group"></i>
            等级配置
        </h2>
        
        <form id="level-form">
            <div class="form-grid">
                <div class="form-group">
                    <label for="level">等级</label>
                    <input type="number" id="level" name="level" min="1" max="100" required>
                </div>
                <div class="form-group">
                    <label for="exp_required">所需总经验</label>
                    <input type="number" id="exp_required" name="exp_required" min="0" step="100" required>
                </div>
                <div class="form-group">
                    <label for="points_reward">积分奖励</label>
                    <input type="number" id="points_reward" name="points_reward" min="0" step="100" value="1000">
                </div>
                <div class="form-group">
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-plus"></i> 添加/更新等级
                    </button>
                </div>
            </div>
        </form>

        <div class="table-container">
            <table class="admin-table">
                <thead>
                    <tr>
                        <th>等级</th>
                        <th>所需总经验</th>
                        <th>积分奖励</th>
                        <th>创建时间</th>
                    </tr>
                </thead>
                <tbody>
                    {% for level in levels %}
                    <tr>
                        <td>{{ level.level }}</td>
                        <td>{{ level.exp_required | format_number }}</td>
                        <td>{{ level.points_reward | format_number }}</td>
                        <td>{{ level.created_at.strftime('%Y-%m-%d %H:%M') if level.created_at else '-' }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>

    <!-- 奖励配置 -->
    <div class="admin-section">
        <h2 class="section-title">
            <i class="fas fa-gift"></i>
            奖励配置
        </h2>
        
        <form id="reward-form">
            <div class="form-grid">
                <div class="form-group">
                    <label for="reward_level">等级</label>
                    <input type="number" id="reward_level" name="level" min="1" max="100" required>
                </div>
                <div class="form-group">
                    <label for="item_select">选择物品</label>
                    <select id="item_select" name="item_select">
                        <option value="">请选择物品</option>
                        {% for item in shop_items %}
                        <option value="{{ item.id }}" data-name="{{ item.name }}" data-image="{{ item.image_path }}">
                            {{ item.name }} ({{ item.category }})
                        </option>
                        {% endfor %}
                    </select>
                </div>
                <div class="form-group">
                    <label for="item_name">物品名称</label>
                    <input type="text" id="item_name" name="item_name" required>
                </div>
                <div class="form-group">
                    <label for="quantity">数量</label>
                    <input type="number" id="quantity" name="quantity" min="1" value="1" required>
                </div>
            </div>
            <button type="submit" class="btn btn-success">
                <i class="fas fa-plus"></i> 添加奖励
            </button>
        </form>

        <div id="rewards-list">
            {% for reward in rewards %}
            <div class="reward-item" data-id="{{ reward.id }}">
                <div class="reward-info">
                    <div style="display: flex; align-items: center; gap: 10px;">
                        <div style="width: 40px; height: 40px; display: flex; align-items: center; justify-content: center; background: var(--bg-secondary); border-radius: 6px;">
                            {% if reward.item_id %}
                                <img src="/static/images/items/{{ reward.item_id }}.png"
                                     alt="{{ reward.item_name }}"
                                     style="width: 32px; height: 32px; object-fit: cover; border-radius: 4px;"
                                     onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
                                <i class="fas fa-cube" style="color: #4CAF50; display: none;"></i>
                            {% else %}
                                <i class="fas fa-cube" style="color: #4CAF50;"></i>
                            {% endif %}
                        </div>
                        <div>
                            <strong>等级 {{ reward.level }}</strong> -
                            {{ reward.item_name }} x{{ reward.quantity }}
                            {% if reward.item_id %}
                            <br><small style="color: var(--text-secondary);">物品ID: {{ reward.item_id }}</small>
                            {% endif %}
                        </div>
                    </div>
                </div>
                <div class="reward-actions">
                    <button class="btn btn-danger btn-sm" onclick="deleteReward({{ reward.id }})">
                        <i class="fas fa-trash"></i> 删除
                    </button>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // 物品选择联动
    const itemSelect = document.getElementById('item_select');
    const itemNameInput = document.getElementById('item_name');
    
    itemSelect.addEventListener('change', function() {
        const selectedOption = this.options[this.selectedIndex];
        if (selectedOption.value) {
            itemNameInput.value = selectedOption.dataset.name || selectedOption.value;
        } else {
            itemNameInput.value = '';
        }
    });

    // 配置表单提交
    document.getElementById('config-form').addEventListener('submit', function(e) {
        e.preventDefault();
        
        const formData = new FormData(this);
        const data = {
            max_level: parseInt(formData.get('max_level')),
            daily_quest_limit: parseInt(formData.get('daily_quest_limit')),
            quest_exp: parseInt(formData.get('quest_exp')),
            enabled: formData.get('enabled') === 'true'
        };
        
        fetch('/admin/battlepass/config', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(data)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert('success', data.message);
            } else {
                showAlert('error', data.message);
            }
        })
        .catch(error => {
            showAlert('error', '请求失败: ' + error.message);
        });
    });

    // 等级表单提交
    document.getElementById('level-form').addEventListener('submit', function(e) {
        e.preventDefault();
        
        const formData = new FormData(this);
        const data = {
            level: parseInt(formData.get('level')),
            exp_required: parseInt(formData.get('exp_required')),
            points_reward: parseInt(formData.get('points_reward'))
        };
        
        fetch('/admin/battlepass/level', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(data)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert('success', data.message);
                setTimeout(() => location.reload(), 1500);
            } else {
                showAlert('error', data.message);
            }
        })
        .catch(error => {
            showAlert('error', '请求失败: ' + error.message);
        });
    });

    // 奖励表单提交
    document.getElementById('reward-form').addEventListener('submit', function(e) {
        e.preventDefault();
        
        const formData = new FormData(this);
        const data = {
            level: parseInt(formData.get('level')),
            item_id: document.getElementById('item_select').value,
            item_name: formData.get('item_name'),
            quantity: parseInt(formData.get('quantity')),
            reward_type: 'item'
        };
        
        fetch('/admin/battlepass/reward', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(data)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert('success', data.message);
                setTimeout(() => location.reload(), 1500);
            } else {
                showAlert('error', data.message);
            }
        })
        .catch(error => {
            showAlert('error', '请求失败: ' + error.message);
        });
    });
});

function showAlert(type, message) {
    const alertElement = document.getElementById('alert-' + type);
    const messageElement = document.getElementById(type + '-message');
    
    messageElement.textContent = message;
    alertElement.style.display = 'block';
    
    setTimeout(() => {
        alertElement.style.display = 'none';
    }, 5000);
}

function deleteReward(rewardId) {
    if (!confirm('确定要删除这个奖励吗？')) {
        return;
    }
    
    fetch(`/admin/battlepass/reward/${rewardId}`, {
        method: 'DELETE'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('success', data.message);
            document.querySelector(`[data-id="${rewardId}"]`).remove();
        } else {
            showAlert('error', data.message);
        }
    })
    .catch(error => {
        showAlert('error', '请求失败: ' + error.message);
    });
}
</script>
{% endblock %}
