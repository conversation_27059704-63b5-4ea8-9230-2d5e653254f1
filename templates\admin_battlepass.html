{% extends "base.html" %}

{% block title %}通行证管理 - 7788商城{% endblock %}

{% block head_extra %}
<style>
    .admin-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 20px;
    }

    .admin-header {
        background: var(--card-bg);
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 30px;
        border: 1px solid var(--border-color);
    }

    .admin-section {
        background: var(--card-bg);
        border-radius: 10px;
        padding: 25px;
        margin-bottom: 30px;
        border: 1px solid var(--border-color);
    }

    .section-title {
        font-size: 1.5rem;
        font-weight: bold;
        margin-bottom: 20px;
        color: var(--text-primary);
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .form-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 20px;
        margin-bottom: 20px;
    }

    .form-group {
        display: flex;
        flex-direction: column;
        gap: 5px;
    }

    .form-group label {
        font-weight: bold;
        color: var(--text-primary);
    }

    .form-group input,
    .form-group select {
        padding: 10px;
        border: 1px solid var(--border-color);
        border-radius: 5px;
        background: var(--bg-primary);
        color: var(--text-primary);
    }

    .btn {
        padding: 10px 20px;
        border: none;
        border-radius: 5px;
        cursor: pointer;
        font-weight: bold;
        transition: all 0.3s ease;
    }

    .btn-primary {
        background: var(--accent-color);
        color: white;
    }

    .btn-primary:hover {
        background: #5a6fd8;
    }

    .btn-success {
        background: var(--success-color);
        color: white;
    }

    .btn-danger {
        background: var(--error-color);
        color: white;
    }

    .table-container {
        overflow-x: auto;
        margin-top: 20px;
    }

    .admin-table {
        width: 100%;
        border-collapse: collapse;
        background: var(--bg-primary);
    }

    .admin-table th,
    .admin-table td {
        padding: 12px;
        text-align: left;
        border-bottom: 1px solid var(--border-color);
    }

    .admin-table th {
        background: var(--bg-secondary);
        font-weight: bold;
        color: var(--text-primary);
    }

    .admin-table td {
        color: var(--text-secondary);
    }

    .status-enabled {
        color: var(--success-color);
        font-weight: bold;
    }

    .status-disabled {
        color: var(--error-color);
        font-weight: bold;
    }

    .reward-item {
        background: var(--bg-secondary);
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 10px;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .reward-info {
        flex: 1;
    }

    .reward-actions {
        display: flex;
        gap: 10px;
    }

    .alert {
        padding: 15px;
        border-radius: 5px;
        margin-bottom: 20px;
        display: none;
    }

    .alert-success {
        background: rgba(76, 175, 80, 0.1);
        border: 1px solid var(--success-color);
        color: var(--success-color);
    }

    .alert-error {
        background: rgba(244, 67, 54, 0.1);
        border: 1px solid var(--error-color);
        color: var(--error-color);
    }

    @media (max-width: 768px) {
        .admin-container {
            padding: 15px;
        }

        .form-grid {
            grid-template-columns: 1fr;
        }

        .reward-item {
            flex-direction: column;
            gap: 15px;
            text-align: center;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="admin-container">
    <!-- 页面标题 -->
    <div class="admin-header">
        <h1><i class="fas fa-trophy"></i> 通行证系统管理</h1>
        <p>管理通行证等级、奖励和系统配置</p>
    </div>

    <!-- 消息提示 -->
    <div id="alert-success" class="alert alert-success">
        <span id="success-message"></span>
    </div>
    <div id="alert-error" class="alert alert-error">
        <span id="error-message"></span>
    </div>

    <!-- 系统配置 -->
    <div class="admin-section">
        <h2 class="section-title">
            <i class="fas fa-cog"></i>
            系统配置
        </h2>
        
        <form id="config-form">
            <div class="form-grid">
                <div class="form-group">
                    <label for="max_level">最高等级</label>
                    <input type="number" id="max_level" name="max_level" value="{{ config.max_level }}" min="1" max="100">
                </div>
                <div class="form-group">
                    <label for="daily_quest_limit">每日任务限制</label>
                    <input type="number" id="daily_quest_limit" name="daily_quest_limit" value="{{ config.daily_quest_limit }}" min="1" max="100">
                </div>
                <div class="form-group">
                    <label for="quest_exp">单次任务经验</label>
                    <input type="number" id="quest_exp" name="quest_exp" value="{{ config.quest_exp }}" min="1" step="100">
                </div>
                <div class="form-group">
                    <label for="enabled">系统状态</label>
                    <select id="enabled" name="enabled">
                        <option value="true" {% if config.enabled %}selected{% endif %}>启用</option>
                        <option value="false" {% if not config.enabled %}selected{% endif %}>禁用</option>
                    </select>
                </div>
            </div>
            <button type="submit" class="btn btn-primary">
                <i class="fas fa-save"></i> 保存配置
            </button>
        </form>
    </div>

    <!-- 等级配置 -->
    <div class="admin-section">
        <h2 class="section-title">
            <i class="fas fa-layer-group"></i>
            等级配置
        </h2>
        
        <form id="level-form">
            <div class="form-grid">
                <div class="form-group">
                    <label for="level">等级</label>
                    <input type="number" id="level" name="level" min="1" max="100" required>
                </div>
                <div class="form-group">
                    <label for="exp_required">所需总经验</label>
                    <input type="number" id="exp_required" name="exp_required" min="0" step="100" required>
                </div>
                <div class="form-group">
                    <label for="points_reward">积分奖励</label>
                    <input type="number" id="points_reward" name="points_reward" min="0" step="100" value="1000">
                </div>
                <div class="form-group">
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-plus"></i> 添加/更新等级
                    </button>
                </div>
            </div>
        </form>

        <div class="table-container">
            <table class="admin-table">
                <thead>
                    <tr>
                        <th>等级</th>
                        <th>所需总经验</th>
                        <th>积分奖励</th>
                        <th>创建时间</th>
                    </tr>
                </thead>
                <tbody>
                    {% for level in levels %}
                    <tr>
                        <td>{{ level.level }}</td>
                        <td>{{ level.exp_required | format_number }}</td>
                        <td>{{ level.points_reward | format_number }}</td>
                        <td>{{ level.created_at.strftime('%Y-%m-%d %H:%M') if level.created_at else '-' }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>

    <!-- 奖励配置 -->
    <div class="admin-section">
        <h2 class="section-title">
            <i class="fas fa-gift"></i>
            奖励配置
        </h2>
        
        <form id="reward-form">
            <div class="form-grid">
                <div class="form-group">
                    <label for="reward_level">等级</label>
                    <input type="number" id="reward_level" name="level" min="1" max="100" required>
                </div>
                <div class="form-group">
                    <label for="item_select">选择物品</label>
                    <button type="button" id="selectItemBtn" class="btn btn-primary" style="width: 100%; padding: 12px;">
                        <i class="fas fa-search"></i> 从物品库选择
                    </button>
                    <input type="hidden" id="selected_item_id" name="item_id">
                    <input type="hidden" id="selected_image_path" name="image_path">
                    <div id="selectedItemDisplay" style="margin-top: 10px; display: none; padding: 10px; background: var(--bg-secondary); border-radius: 6px;">
                        <div style="display: flex; align-items: center; gap: 10px;">
                            <img id="selectedItemImage" src="" style="width: 40px; height: 40px; object-fit: cover; border-radius: 4px;">
                            <div>
                                <div id="selectedItemName" style="font-weight: bold;"></div>
                                <div id="selectedItemId" style="font-size: 0.8rem; color: var(--text-secondary);"></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="form-group">
                    <label for="item_name">物品名称</label>
                    <input type="text" id="item_name" name="item_name" required>
                </div>
                <div class="form-group">
                    <label for="quantity">数量</label>
                    <input type="number" id="quantity" name="quantity" min="1" value="1" required>
                </div>
            </div>
            <button type="submit" class="btn btn-success">
                <i class="fas fa-plus"></i> 添加奖励
            </button>
        </form>

        <div id="rewards-list">
            {% for reward in rewards %}
            <div class="reward-item" data-id="{{ reward.id }}">
                <div class="reward-info">
                    <div style="display: flex; align-items: center; gap: 10px;">
                        <div style="width: 40px; height: 40px; display: flex; align-items: center; justify-content: center; background: var(--bg-secondary); border-radius: 6px;">
                            {% if reward.image_path %}
                                <img src="{{ reward.image_path }}"
                                     alt="{{ reward.item_name }}"
                                     style="width: 32px; height: 32px; object-fit: cover; border-radius: 4px;"
                                     onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
                                <i class="fas fa-cube" style="color: #4CAF50; display: none;"></i>
                            {% else %}
                                <i class="fas fa-cube" style="color: #4CAF50;"></i>
                            {% endif %}
                        </div>
                        <div>
                            <strong>等级 {{ reward.level }}</strong> -
                            {{ reward.item_name }} x{{ reward.quantity }}
                            {% if reward.item_id %}
                            <br><small style="color: var(--text-secondary);">物品ID: {{ reward.item_id }}</small>
                            {% endif %}
                        </div>
                    </div>
                </div>
                <div class="reward-actions">
                    <button class="btn btn-danger btn-sm" onclick="deleteReward({{ reward.id }})">
                        <i class="fas fa-trash"></i> 删除
                    </button>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
</div>

<!-- 物品选择弹窗 -->
<div id="itemSelectModal" class="modal" style="display: none;">
    <div class="modal-content" style="max-width: 1000px; max-height: 80vh; overflow-y: auto;">
        <div class="modal-header">
            <h3><i class="fas fa-search"></i> 选择物品</h3>
            <span class="close" onclick="closeItemSelectModal()">&times;</span>
        </div>
        <div class="modal-body">
            <div style="margin-bottom: 20px;">
                <input type="text" id="itemSearchInput" placeholder="搜索物品..."
                       style="width: 100%; padding: 10px; border: 1px solid var(--border-color); border-radius: 6px; background: var(--bg-secondary); color: var(--text-primary);">
            </div>
            <div id="itemsGrid" style="display: grid; grid-template-columns: repeat(auto-fill, minmax(200px, 1fr)); gap: 15px; max-height: 400px; overflow-y: auto;">
                <!-- 物品卡片将在这里动态生成 -->
            </div>
        </div>
        <div class="modal-footer">
            <button type="button" class="btn btn-secondary" onclick="closeItemSelectModal()">取消</button>
        </div>
    </div>
</div>

<style>
.modal {
    position: fixed;
    z-index: 9999;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.8);
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-content {
    background-color: var(--card-bg);
    border-radius: 12px;
    border: 1px solid var(--border-color);
    width: 90%;
    max-width: 1000px;
}

.modal-header {
    padding: 20px;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    margin: 0;
    color: var(--text-primary);
}

.modal-body {
    padding: 20px;
}

.modal-footer {
    padding: 15px 20px;
    border-top: 1px solid var(--border-color);
    text-align: right;
}

.close {
    color: var(--text-secondary);
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    line-height: 1;
}

.close:hover {
    color: var(--text-primary);
}

.item-card {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 15px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
}

.item-card:hover {
    border-color: var(--accent-color);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.item-card.selected {
    border-color: var(--accent-color);
    background: rgba(102, 126, 234, 0.1);
}

.item-card img {
    width: 100%;
    height: 120px;
    object-fit: cover;
    border-radius: 6px;
    margin-bottom: 10px;
}

.item-card .item-name {
    font-weight: bold;
    color: var(--text-primary);
    margin-bottom: 5px;
    font-size: 0.9rem;
}

.item-card .item-id {
    color: var(--text-secondary);
    font-size: 0.8rem;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // 物品选择按钮事件
    document.getElementById('selectItemBtn').addEventListener('click', function() {
        openItemSelectModal();
    });

    // 配置表单提交
    document.getElementById('config-form').addEventListener('submit', function(e) {
        e.preventDefault();
        
        const formData = new FormData(this);
        const data = {
            max_level: parseInt(formData.get('max_level')),
            daily_quest_limit: parseInt(formData.get('daily_quest_limit')),
            quest_exp: parseInt(formData.get('quest_exp')),
            enabled: formData.get('enabled') === 'true'
        };
        
        fetch('/admin/battlepass/config', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(data)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert('success', data.message);
            } else {
                showAlert('error', data.message);
            }
        })
        .catch(error => {
            showAlert('error', '请求失败: ' + error.message);
        });
    });

    // 等级表单提交
    document.getElementById('level-form').addEventListener('submit', function(e) {
        e.preventDefault();
        
        const formData = new FormData(this);
        const data = {
            level: parseInt(formData.get('level')),
            exp_required: parseInt(formData.get('exp_required')),
            points_reward: parseInt(formData.get('points_reward'))
        };
        
        fetch('/admin/battlepass/level', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(data)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert('success', data.message);
                setTimeout(() => location.reload(), 1500);
            } else {
                showAlert('error', data.message);
            }
        })
        .catch(error => {
            showAlert('error', '请求失败: ' + error.message);
        });
    });

    // 奖励表单提交
    document.getElementById('reward-form').addEventListener('submit', function(e) {
        e.preventDefault();
        
        const formData = new FormData(this);
        const data = {
            level: parseInt(formData.get('level')),
            item_id: document.getElementById('selected_item_id').value,
            item_name: formData.get('item_name'),
            quantity: parseInt(formData.get('quantity')),
            reward_type: 'item',
            image_path: document.getElementById('selected_image_path').value
        };
        
        fetch('/admin/battlepass/reward', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(data)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert('success', data.message);
                setTimeout(() => location.reload(), 1500);
            } else {
                showAlert('error', data.message);
            }
        })
        .catch(error => {
            showAlert('error', '请求失败: ' + error.message);
        });
    });
});

function showAlert(type, message) {
    const alertElement = document.getElementById('alert-' + type);
    const messageElement = document.getElementById(type + '-message');
    
    messageElement.textContent = message;
    alertElement.style.display = 'block';
    
    setTimeout(() => {
        alertElement.style.display = 'none';
    }, 5000);
}

function deleteReward(rewardId) {
    if (!confirm('确定要删除这个奖励吗？')) {
        return;
    }
    
    fetch(`/admin/battlepass/reward/${rewardId}`, {
        method: 'DELETE'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('success', data.message);
            document.querySelector(`[data-id="${rewardId}"]`).remove();
        } else {
            showAlert('error', data.message);
        }
    })
    .catch(error => {
        showAlert('error', '请求失败: ' + error.message);
    });
}

// 物品选择相关函数
let allShopItems = [];

function openItemSelectModal() {
    // 获取商城物品数据
    if (allShopItems.length === 0) {
        // 从后端获取物品数据
        fetch('/admin/battlepass/items')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    allShopItems = data.items;
                    showItemSelectModal();
                } else {
                    showAlert('error', '获取物品数据失败');
                }
            })
            .catch(error => {
                showAlert('error', '获取物品数据失败: ' + error.message);
            });
    } else {
        showItemSelectModal();
    }
}

function showItemSelectModal() {
    const modal = document.getElementById('itemSelectModal');
    const itemsGrid = document.getElementById('itemsGrid');

    // 清空网格
    itemsGrid.innerHTML = '';

    // 生成物品卡片
    allShopItems.forEach(item => {
        const card = document.createElement('div');
        card.className = 'item-card';
        card.innerHTML = `
            <img src="${item.image_path || '/static/uploads/default-item.jpg'}"
                 alt="${item.name}"
                 onerror="this.src='/static/uploads/default-item.jpg'">
            <div class="item-name">${item.name}</div>
            <div class="item-id">${item.id}</div>
        `;

        card.addEventListener('click', function() {
            selectItem(item);
        });

        itemsGrid.appendChild(card);
    });

    // 显示弹窗
    modal.style.display = 'flex';

    // 绑定搜索功能
    const searchInput = document.getElementById('itemSearchInput');
    searchInput.value = '';
    searchInput.addEventListener('input', function() {
        filterItems(this.value);
    });
}

function selectItem(item) {
    // 更新隐藏字段
    document.getElementById('selected_item_id').value = item.id;
    document.getElementById('selected_image_path').value = item.image_path || '';
    document.getElementById('item_name').value = item.name;

    // 更新显示
    const display = document.getElementById('selectedItemDisplay');
    const image = document.getElementById('selectedItemImage');
    const name = document.getElementById('selectedItemName');
    const id = document.getElementById('selectedItemId');

    image.src = item.image_path || '/static/uploads/default-item.jpg';
    name.textContent = item.name;
    id.textContent = item.id;
    display.style.display = 'block';

    // 关闭弹窗
    closeItemSelectModal();

    showAlert('success', '已选择物品: ' + item.name);
}

function filterItems(searchTerm) {
    const cards = document.querySelectorAll('.item-card');
    const term = searchTerm.toLowerCase();

    cards.forEach(card => {
        const name = card.querySelector('.item-name').textContent.toLowerCase();
        const id = card.querySelector('.item-id').textContent.toLowerCase();

        if (name.includes(term) || id.includes(term)) {
            card.style.display = 'block';
        } else {
            card.style.display = 'none';
        }
    });
}

function closeItemSelectModal() {
    document.getElementById('itemSelectModal').style.display = 'none';
}
</script>
{% endblock %}
