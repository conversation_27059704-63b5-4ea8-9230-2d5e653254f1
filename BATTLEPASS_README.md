# 通行证系统使用说明

## 系统概述

通行证系统是一个基于任务完成的等级奖励系统，玩家通过完成游戏内任务获得经验，升级解锁各种奖励。

## 主要功能

### 1. 任务完成与经验获取
- 玩家完成任务后，客户端发送POST请求到服务器
- 每次任务完成默认获得1000经验（可在管理后台调整）
- 每天最多完成20次任务获得经验（可在管理后台调整）

### 2. 等级系统
- 支持自定义最高等级（默认50级）
- 每个等级有不同的经验需求
- 升级时自动发放积分和物品奖励

### 3. 奖励系统
- **积分奖励**：升级时自动发放到用户账户
- **物品奖励**：从现有商城物品中选择，自动发放到用户仓库
- 奖励配置完全可自定义

### 4. 管理后台
- 系统配置：最高等级、每日任务限制、任务经验值等
- 等级配置：每个等级的经验需求和积分奖励
- 奖励配置：为每个等级添加物品奖励

## API接口

### 任务完成接口
```
POST /api/quest/complete
Content-Type: application/json

{
    "steam_id": "玩家Steam ID",
    "quest_name": "任务名称"
}
```

**响应示例：**
```json
{
    "success": true,
    "message": "任务完成成功",
    "data": {
        "exp_gained": 1000,
        "total_exp": 5000,
        "old_level": 2,
        "new_level": 3,
        "level_up": true,
        "points_reward": 1000,
        "rewards_given": [
            {
                "level": 3,
                "item_name": "AK47突击步枪",
                "quantity": 1,
                "reward_type": "item"
            }
        ],
        "daily_quest_count": 5,
        "daily_quest_limit": 20,
        "user_points": 15000
    }
}
```

### 获取通行证信息接口
```
GET /api/battlepass/info
```

## 数据库表结构

### 1. battlepass_levels - 通行证等级配置表
- `level`: 等级
- `exp_required`: 升级到此等级所需总经验
- `points_reward`: 升级奖励积分

### 2. battlepass_rewards - 通行证物品奖励表
- `level`: 等级
- `item_id`: 物品ID
- `item_name`: 物品名称
- `quantity`: 数量
- `reward_type`: 奖励类型（item/points）

### 3. user_battlepass - 用户通行证进度表
- `steamid`: 用户Steam ID
- `current_level`: 当前等级
- `total_exp`: 总经验
- `daily_quest_count`: 今日完成任务次数
- `last_quest_date`: 最后完成任务日期

### 4. quest_completion_logs - 任务完成记录表
- `steamid`: 用户Steam ID
- `quest_name`: 任务名称
- `exp_gained`: 获得经验
- `completed_at`: 完成时间

### 5. battlepass_reward_claims - 通行证奖励领取记录表
- `steamid`: 用户Steam ID
- `level`: 等级
- `item_name`: 物品名称
- `quantity`: 数量
- `claimed_at`: 领取时间

## 安装和配置

### 1. 数据库初始化
运行SQL脚本创建必要的数据库表：
```bash
mysql -u username -p database_name < sql/battlepass_system.sql
```

### 2. 启动服务
通行证系统已集成到主应用中，启动主应用即可：
```bash
python app.py
```

### 3. 管理后台配置
1. 以管理员身份登录系统
2. 访问 `/admin/battlepass` 进入通行证管理页面
3. 配置系统参数、等级设置和奖励

## 使用流程

### 管理员配置流程
1. **系统配置**：设置最高等级、每日任务限制、任务经验值
2. **等级配置**：为每个等级设置经验需求和积分奖励
3. **奖励配置**：从商城物品中选择，为各等级添加物品奖励

### 玩家使用流程
1. **完成任务**：在游戏中完成任务
2. **获得经验**：客户端自动发送请求，玩家获得经验
3. **等级升级**：达到经验要求时自动升级
4. **领取奖励**：积分自动到账，物品自动发放到仓库
5. **查看进度**：在网站通行证页面查看进度和奖励

## 测试

使用提供的测试脚本测试系统功能：
```bash
python test_battlepass.py
```

## 注意事项

1. **每日限制**：每天最多完成20次任务获得经验，超过限制不再获得经验
2. **自动发放**：所有奖励都是自动发放，无需手动领取
3. **数据安全**：所有操作都有日志记录，便于追踪和调试
4. **兼容性**：系统不影响现有功能，可以安全集成

## 故障排除

### 常见问题
1. **任务完成无响应**：检查API接口是否正确，Steam ID是否存在
2. **奖励未发放**：检查奖励配置是否正确，用户是否达到等级要求
3. **每日限制问题**：检查系统时间和用户最后任务时间

### 日志查看
- 应用日志：`app.log`
- 通行证日志：查看控制台输出中的 `battlepass` 相关信息

## 扩展功能

系统设计支持以下扩展：
1. **季度通行证**：可以添加季度重置功能
2. **付费通行证**：可以添加高级通行证功能
3. **任务系统**：可以集成更复杂的任务系统
4. **成就系统**：可以基于通行证数据实现成就系统
