-- 创建回收记录表
CREATE TABLE IF NOT EXISTS `recycle_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `steamid` varchar(255) NOT NULL COMMENT '玩家的Steam ID',
  `item_name` varchar(255) NOT NULL COMMENT '回收物品名称',
  `quantity` int(11) NOT NULL DEFAULT '1' COMMENT '回收数量',
  `points_earned` int(11) NOT NULL DEFAULT '0' COMMENT '获得的积分',
  `recycle_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '回收时间',
  PRIMARY KEY (`id`),
  KEY `idx_item_name` (`item_name`),
  KEY `idx_steamid` (`steamid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 创建回收统计表（用于统计每种物品的回收总数）
CREATE TABLE IF NOT EXISTS `recycle_stats` (
  `item_name` varchar(255) NOT NULL COMMENT '回收物品名称',
  `total_recycled` int(11) NOT NULL DEFAULT '0' COMMENT '总回收数量',
  `last_updated` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
  PRIMARY KEY (`item_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
