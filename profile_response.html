<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>7788商城</title>
    <link rel="stylesheet" href="/static/css/style.css">
    <link rel="stylesheet" href="{{ url_for('static', filename='vendor/fontawesome/all.min.css') }}">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="main-nav">
        <div class="container nav-container">
            <div class="logo">
                <a href="/">
                    <i class="fas fa-gamepad"></i>
                    <span>7788商城</span>
                </a>
            </div>
            <ul class="nav-menu">
                <li class="nav-item"><a href="/home" class="nav-link "><i class="fas fa-home"></i> 主页</a></li>
                <li class="nav-item"><a href="/items" class="nav-link "><i class="fas fa-cube"></i> 物品代码</a></li>
                <li class="nav-item"><a href="/sponsor" class="nav-link "><i class="fas fa-heart"></i> 赞助</a></li>
                <li class="nav-item"><a href="/entertainment" class="nav-link "><i class="fas fa-dice"></i> 娱乐</a></li>
                <li class="nav-item"><a href="/profile" class="nav-link active"><i class="fas fa-user"></i> 个人信息</a></li>
            </ul>
            <div class="search-box">
                <form action="/search" method="GET">
                    <input type="text" name="q" placeholder="搜索物品..." >
                    <button type="submit"><i class="fas fa-search"></i></button>
                </form>
            </div>
        </div>
    </nav>
    
    <!-- 主内容区 -->
    <main class="container">
        
            <!-- 个人信息页面 -->
            <div class="profile-container">
                <div class="profile-header">
                    <div class="profile-avatar">
                        <img src="/static/img/avatar.jpg" alt="用户头像">
                    </div>
                    <div class="profile-info">
                        <h1>用户名称</h1>
                        <p class="user-id">ID: SCUM123456</p>
                        <div class="user-stats">
                            <div class="stat">
                                <span class="stat-value">64</span>
                                <span class="stat-label">已保存物品</span>
                            </div>
                            <div class="stat">
                                <span class="stat-value">高级</span>
                                <span class="stat-label">赞助等级</span>
                            </div>
                            <div class="stat">
                                <span class="stat-value">2023-01-15</span>
                                <span class="stat-label">加入日期</span>
                            </div>
                        </div>
                    </div>
                    <div class="profile-actions">
                        <button class="edit-profile-btn"><i class="fas fa-edit"></i> 编辑资料</button>
                        <button class="settings-btn"><i class="fas fa-cog"></i> 设置</button>
                    </div>
                </div>
                
                <div class="profile-tabs">
                    <button class="tab-btn active" data-tab="saved-items">已保存物品</button>
                    <button class="tab-btn" data-tab="history">历史记录</button>
                    <button class="tab-btn" data-tab="settings">账户设置</button>
                </div>
                
                <div class="profile-content">
                    <div class="tab-content active" id="saved-items">
                        <h2>已保存物品</h2>
                        <div class="saved-items-grid">
                            <!-- 这里可以循环显示用户保存的物品 -->
                            <div class="no-items">
                                <p>您还没有保存任何物品</p>
                                <a href="/items" class="browse-btn">浏览物品</a>
                            </div>
                        </div>
                    </div>
                    
                    <div class="tab-content" id="history">
                        <h2>历史记录</h2>
                        <div class="history-list">
                            <!-- 这里可以循环显示用户的历史记录 -->
                            <div class="no-history">
                                <p>暂无历史记录</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="tab-content" id="settings">
                        <h2>账户设置</h2>
                        <form class="settings-form">
                            <div class="form-group">
                                <label for="username">用户名</label>
                                <input type="text" id="username" value="用户名称">
                            </div>
                            <div class="form-group">
                                <label for="email">邮箱</label>
                                <input type="email" id="email" value="<EMAIL>">
                            </div>
                            <div class="form-group">
                                <label for="password">密码</label>
                                <input type="password" id="password" value="********">
                            </div>
                            <button type="submit" class="save-settings-btn">保存设置</button>
                        </form>
                    </div>
                </div>
            </div>
        
    </main>
    
    <!-- 复制成功提示 -->
    <div class="copy-tooltip" id="copyTooltip">已复制到剪贴板<br>请前往游戏内指令框输入</div>
    
    <!-- 页脚 -->
    <footer class="main-footer">
        <div class="container">
            <div class="footer-bottom">
                <p>&copy; 2025 出其东门 | 保留所有权利</p>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script>
    document.addEventListener('DOMContentLoaded', function() {
        // 视图切换
        const gridViewBtn = document.querySelector('.grid-view');
        const listViewBtn = document.querySelector('.list-view');
        const productsGrids = document.querySelectorAll('.products-grid');
        
        if (gridViewBtn && listViewBtn && productsGrids.length > 0) {
            gridViewBtn.addEventListener('click', function() {
                productsGrids.forEach(grid => grid.classList.remove('list-layout'));
                gridViewBtn.classList.add('active');
                listViewBtn.classList.remove('active');
            });
            
            listViewBtn.addEventListener('click', function() {
                productsGrids.forEach(grid => grid.classList.add('list-layout'));
                listViewBtn.classList.add('active');
                gridViewBtn.classList.remove('active');
            });
        }
        
        // 复制功能
        const copyButtons = document.querySelectorAll('.copy-btn');
        const copyTooltip = document.getElementById('copyTooltip');
        
        copyButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                e.stopPropagation();
                const textToCopy = this.getAttribute('data-name');
                
                // 创建临时文本区域
                const textArea = document.createElement('textarea');
                textArea.value = textToCopy;
                document.body.appendChild(textArea);
                textArea.select();
                
                // 复制文本
                document.execCommand('copy');
                
                // 移除临时元素
                document.body.removeChild(textArea);
                
                // 显示提示
                copyTooltip.style.opacity = '1';
                copyTooltip.style.top = (this.getBoundingClientRect().top - 60) + 'px';
                copyTooltip.style.left = (this.getBoundingClientRect().left + this.offsetWidth / 2) + 'px';
                
                // 3秒后隐藏提示
                setTimeout(function() {
                    copyTooltip.style.opacity = '0';
                }, 2000);
            });
        });

        // 卡片点击也触发复制
        const productCards = document.querySelectorAll('.product-card');
        
        productCards.forEach(card => {
            card.addEventListener('click', function(e) {
                // 如果点击的是按钮或其子元素，不执行操作（让按钮事件处理）
                if (e.target.closest('.copy-btn')) {
                    return;
                }
                
                const textToCopy = this.getAttribute('data-name');
                
                // 创建临时文本区域
                const textArea = document.createElement('textarea');
                textArea.value = textToCopy;
                document.body.appendChild(textArea);
                textArea.select();
                
                // 复制文本
                document.execCommand('copy');
                
                // 移除临时元素
                document.body.removeChild(textArea);
                
                // 显示提示
                copyTooltip.style.opacity = '1';
                copyTooltip.style.top = (this.getBoundingClientRect().top + 50) + 'px';
                copyTooltip.style.left = (this.getBoundingClientRect().left + this.offsetWidth / 2) + 'px';
                
                // 2秒后隐藏提示
                setTimeout(function() {
                    copyTooltip.style.opacity = '0';
                }, 2000);
            });
        });
        
        // 个人信息页面标签切换
        const tabBtns = document.querySelectorAll('.tab-btn');
        const tabContents = document.querySelectorAll('.tab-content');
        
        if (tabBtns.length > 0) {
            tabBtns.forEach(btn => {
                btn.addEventListener('click', function() {
                    // 移除所有active类
                    tabBtns.forEach(b => b.classList.remove('active'));
                    tabContents.forEach(c => c.classList.remove('active'));
                    
                    // 添加active类到当前点击的按钮
                    this.classList.add('active');
                    
                    // 显示对应内容
                    const tabId = this.getAttribute('data-tab');
                    document.getElementById(tabId).classList.add('active');
                });
            });
        }
    });
    </script>
</body>
</html> 