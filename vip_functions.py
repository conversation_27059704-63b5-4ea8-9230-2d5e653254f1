import pymysql
from app import get_db_connection, logger

def get_vip_level_info(level):
    """获取指定VIP等级的配置信息"""
    conn = get_db_connection()
    if not conn:
        return None

    try:
        with conn.cursor() as cursor:
            sql = "SELECT * FROM vip_levels WHERE level = %s"
            cursor.execute(sql, (level,))
            vip_info = cursor.fetchone()
            return vip_info
    except Exception as e:
        logger.error(f"获取VIP等级信息失败: {e}")
        return None
    finally:
        conn.close()

def get_all_vip_levels():
    """获取所有VIP等级的配置信息"""
    conn = get_db_connection()
    if not conn:
        return []

    try:
        with conn.cursor() as cursor:
            sql = "SELECT * FROM vip_levels ORDER BY level"
            cursor.execute(sql)
            vip_levels = cursor.fetchall()
            return vip_levels
    except Exception as e:
        logger.error(f"获取所有VIP等级信息失败: {e}")
        return []
    finally:
        conn.close()

def upgrade_vip_level(steamid, target_level):
    """升级用户的VIP等级"""
    conn = get_db_connection()
    if not conn:
        return False, "数据库连接失败"

    try:
        # 获取用户信息
        with conn.cursor() as cursor:
            sql = "SELECT * FROM users WHERE steamid = %s"
            cursor.execute(sql, (steamid,))
            user = cursor.fetchone()
            
            if not user:
                return False, "用户不存在"
            
            current_level = user.get('vip_level', 0)
            current_points = user.get('points', 0)
            
            # 获取目标VIP等级信息
            vip_info = get_vip_level_info(target_level)
            if not vip_info:
                return False, "VIP等级信息不存在"
            
            # 检查是否是升级操作
            if target_level <= current_level:
                return False, "不能降级或重复购买相同等级"
            
            # 检查积分是否足够
            points_required = vip_info['points_required']
            if current_points < points_required:
                return False, f"积分不足，需要{points_required}积分"
            
            # 扣除积分并更新VIP等级
            with conn.cursor() as cursor:
                # 更新用户积分和VIP等级
                sql = """UPDATE users 
                        SET points = points - %s, 
                            vip_level = %s,
                            is_vip = 1
                        WHERE steamid = %s"""
                cursor.execute(sql, (points_required, target_level, steamid))
                
                # 记录VIP购买日志
                sql = """INSERT INTO vip_purchase_logs 
                        (steamid, purchase_time, vip_level, points_spent) 
                        VALUES (%s, NOW(), %s, %s)"""
                cursor.execute(sql, (steamid, target_level, points_required))
                
                # 记录操作日志
                sql = """INSERT INTO action_logs 
                        (steamid, log_time, action_type, details) 
                        VALUES (%s, %s, %s, %s)"""
                details = {
                    'action': 'upgrade_vip',
                    'from_level': current_level,
                    'to_level': target_level,
                    'points_spent': points_required
                }
                import json
                import time
                cursor.execute(sql, (steamid, int(time.time() * 1000), 'vip_upgrade', json.dumps(details)))
            
            conn.commit()
            return True, f"成功升级到VIP{target_level}，消费{points_required}积分"
    except Exception as e:
        conn.rollback()
        logger.error(f"升级VIP等级失败: {e}")
        return False, f"升级失败: {str(e)}"
    finally:
        conn.close()

def calculate_discounted_price(original_price, vip_level):
    """根据VIP等级计算折扣后的价格"""
    if vip_level <= 0:
        return original_price
    
    vip_info = get_vip_level_info(vip_level)
    if not vip_info:
        return original_price
    
    discount_rate = vip_info['discount_rate']
    return int(original_price * discount_rate)

def calculate_checkin_bonus(base_bonus, vip_level):
    """根据VIP等级计算签到奖励"""
    if vip_level <= 0:
        return base_bonus
    
    vip_info = get_vip_level_info(vip_level)
    if not vip_info:
        return base_bonus
    
    return vip_info['checkin_bonus']

def calculate_points_bonus(base_points, vip_level):
    """根据VIP等级计算积分奖励倍率"""
    if vip_level <= 0:
        return base_points
    
    vip_info = get_vip_level_info(vip_level)
    if not vip_info:
        return base_points
    
    bonus_rate = vip_info['points_bonus_rate']
    return int(base_points * bonus_rate)
