// 缓存名称和版本
const CACHE_NAME = 'scum-shop-cache-v1';

// 需要缓存的资源列表
const CACHE_URLS = [
  '/',
  '/static/css/style.css',
  '/static/vendor/bootstrap/css/bootstrap.min.css',
  '/static/vendor/fontawesome/css/all.min.css',
  '/static/uploads/default-item.jpg'
];

// 安装Service Worker
self.addEventListener('install', event => {
  console.log('Service Worker 安装中...');
  
  // 等待缓存完成
  event.waitUntil(
    caches.open(CACHE_NAME)
      .then(cache => {
        console.log('缓存打开');
        return cache.addAll(CACHE_URLS);
      })
      .then(() => {
        console.log('初始资源已缓存');
        return self.skipWaiting();
      })
  );
});

// 激活Service Worker
self.addEventListener('activate', event => {
  console.log('Service Worker 激活中...');
  
  // 清理旧缓存
  event.waitUntil(
    caches.keys().then(cacheNames => {
      return Promise.all(
        cacheNames.map(cacheName => {
          if (cacheName !== CACHE_NAME) {
            console.log('删除旧缓存:', cacheName);
            return caches.delete(cacheName);
          }
        })
      );
    }).then(() => {
      console.log('Service Worker 已激活');
      return self.clients.claim();
    })
  );
});

// 处理请求
self.addEventListener('fetch', event => {
  // 只处理GET请求
  if (event.request.method !== 'GET') return;
  
  // 获取请求URL
  const url = new URL(event.request.url);
  
  // 处理图片请求 - 使用缓存优先策略
  if (
    url.pathname.startsWith('/static/uploads/') || 
    url.pathname.endsWith('.jpg') || 
    url.pathname.endsWith('.jpeg') || 
    url.pathname.endsWith('.png') || 
    url.pathname.endsWith('.gif') || 
    url.pathname.endsWith('.webp')
  ) {
    event.respondWith(cacheFirstStrategy(event.request));
  } 
  // 处理静态资源请求 - 使用缓存优先策略
  else if (url.pathname.startsWith('/static/')) {
    event.respondWith(cacheFirstStrategy(event.request));
  }
  // 其他请求 - 使用网络优先策略
  else {
    event.respondWith(networkFirstStrategy(event.request));
  }
});

// 缓存优先策略
async function cacheFirstStrategy(request) {
  const cachedResponse = await caches.match(request);
  
  if (cachedResponse) {
    // 如果缓存中有响应，返回缓存的响应
    console.log('从缓存返回:', request.url);
    return cachedResponse;
  }
  
  // 如果缓存中没有，从网络获取
  try {
    const networkResponse = await fetch(request);
    
    // 检查响应是否有效
    if (!networkResponse || networkResponse.status !== 200 || networkResponse.type !== 'basic') {
      return networkResponse;
    }
    
    // 将响应添加到缓存
    const responseToCache = networkResponse.clone();
    const cache = await caches.open(CACHE_NAME);
    cache.put(request, responseToCache);
    
    console.log('添加到缓存:', request.url);
    return networkResponse;
  } catch (error) {
    console.error('获取资源失败:', error);
    
    // 如果是图片请求，返回默认图片
    if (request.url.match(/\.(jpg|jpeg|png|gif|webp)$/i)) {
      const defaultImage = await caches.match('/static/uploads/default-item.jpg');
      if (defaultImage) {
        return defaultImage;
      }
    }
    
    // 返回错误响应
    return new Response('Network error', { status: 408, headers: { 'Content-Type': 'text/plain' } });
  }
}

// 网络优先策略
async function networkFirstStrategy(request) {
  try {
    // 先尝试从网络获取
    const networkResponse = await fetch(request);
    
    // 检查响应是否有效
    if (!networkResponse || networkResponse.status !== 200 || networkResponse.type !== 'basic') {
      return networkResponse;
    }
    
    // 将响应添加到缓存
    const responseToCache = networkResponse.clone();
    const cache = await caches.open(CACHE_NAME);
    cache.put(request, responseToCache);
    
    return networkResponse;
  } catch (error) {
    console.log('网络请求失败，尝试从缓存获取:', request.url);
    
    // 如果网络请求失败，尝试从缓存获取
    const cachedResponse = await caches.match(request);
    if (cachedResponse) {
      return cachedResponse;
    }
    
    // 如果缓存中也没有，返回错误响应
    return new Response('Network error', { status: 408, headers: { 'Content-Type': 'text/plain' } });
  }
}
