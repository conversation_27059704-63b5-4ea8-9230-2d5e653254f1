import logging
import random
import json
from datetime import datetime
from decimal import Decimal
from flask import Blueprint, render_template, request, jsonify, session, redirect, url_for, flash, current_app

# 创建蓝图
lottery_bp = Blueprint('lottery', __name__)

# 自定义JSON编码器，处理Decimal类型
class DecimalJSONEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, Decimal):
            return float(obj)
        return super(DecimalJSONEncoder, self).default(obj)

# 导入必要的函数
def get_db_connection():
    """This function will be imported from app at runtime"""
    pass

def get_user_by_steamid(steamid, use_cache=True):
    """This function will be imported from app at runtime"""
    pass

def clear_user_cache(steamid):
    """This function will be imported from app at runtime"""
    pass

def get_all_data():
    """This function will be imported from app at runtime"""
    pass

# 设置日志
logger = logging.getLogger("lottery")

# 默认抽奖配置
DEFAULT_LOTTERY_CONFIG = {
    'single_cost': 1000,  # 单抽花费
    'ten_cost': 9500,     # 十连抽花费（有优惠）
    'hundred_cost': 90000 # 百连抽花费（有优惠）
}

def get_lottery_config():
    """获取抽奖配置"""
    conn = get_db_connection()
    config = DEFAULT_LOTTERY_CONFIG.copy()

    if conn:
        try:
            with conn.cursor() as cursor:
                sql = "SELECT config_key, config_value FROM system_config WHERE config_key LIKE 'lottery_%'"
                cursor.execute(sql)
                results = cursor.fetchall()

                for item in results:
                    key = item['config_key'].replace('lottery_', '')
                    if key in config:
                        config[key] = int(item['config_value'])
        except Exception as e:
            logger.error(f"获取抽奖配置失败: {e}")
        finally:
            conn.close()

    return config

def save_lottery_config(config):
    """保存抽奖配置"""
    conn = get_db_connection()
    if not conn:
        return False

    try:
        with conn.cursor() as cursor:
            for key, value in config.items():
                config_key = f"lottery_{key}"
                # 检查配置是否存在
                sql = "SELECT COUNT(*) as count FROM system_config WHERE config_key = %s"
                cursor.execute(sql, (config_key,))
                result = cursor.fetchone()

                if result and result['count'] > 0:
                    # 更新现有配置
                    sql = "UPDATE system_config SET config_value = %s WHERE config_key = %s"
                    cursor.execute(sql, (str(value), config_key))
                else:
                    # 插入新配置
                    sql = "INSERT INTO system_config (config_key, config_value) VALUES (%s, %s)"
                    cursor.execute(sql, (config_key, str(value)))

        conn.commit()
        return True
    except Exception as e:
        logger.error(f"保存抽奖配置失败: {e}")
        conn.rollback()
        return False
    finally:
        conn.close()

def get_all_prizes():
    """获取所有奖品"""
    conn = get_db_connection()
    prizes = []

    if conn:
        try:
            with conn.cursor() as cursor:
                sql = """
                SELECT id, item_id, item_name, probability, image_path,
                       created_at, updated_at, created_by, is_active
                FROM lottery_prizes
                ORDER BY probability ASC, id ASC
                """
                cursor.execute(sql)
                prizes = cursor.fetchall()

                # 将Decimal类型转换为float
                for prize in prizes:
                    if 'probability' in prize and isinstance(prize['probability'], Decimal):
                        prize['probability'] = float(prize['probability'])

                # 计算总概率
                total_probability = 0
                for prize in prizes:
                    if prize['is_active']:
                        total_probability += float(prize['probability'])

                # 添加总概率到结果中
                for prize in prizes:
                    prize['total_probability'] = total_probability
        except Exception as e:
            logger.error(f"获取奖品列表失败: {e}")
        finally:
            conn.close()

    return prizes

def add_prize(item_id, item_name, probability, image_path, created_by):
    """添加奖品"""
    conn = get_db_connection()
    if not conn:
        return False, "数据库连接失败"

    try:
        with conn.cursor() as cursor:
            sql = """
            INSERT INTO lottery_prizes
            (item_id, item_name, probability, image_path, created_by)
            VALUES (%s, %s, %s, %s, %s)
            """
            cursor.execute(sql, (item_id, item_name, probability, image_path, created_by))

        conn.commit()
        return True, "奖品添加成功"
    except Exception as e:
        logger.error(f"添加奖品失败: {e}")
        conn.rollback()
        return False, f"添加奖品失败: {str(e)}"
    finally:
        conn.close()

def update_prize(prize_id, item_id, item_name, probability, image_path, is_active):
    """更新奖品"""
    conn = get_db_connection()
    if not conn:
        return False, "数据库连接失败"

    try:
        with conn.cursor() as cursor:
            sql = """
            UPDATE lottery_prizes
            SET item_id = %s, item_name = %s, probability = %s,
                image_path = %s, is_active = %s
            WHERE id = %s
            """
            cursor.execute(sql, (item_id, item_name, probability, image_path, is_active, prize_id))

        conn.commit()
        return True, "奖品更新成功"
    except Exception as e:
        logger.error(f"更新奖品失败: {e}")
        conn.rollback()
        return False, f"更新奖品失败: {str(e)}"
    finally:
        conn.close()

def delete_prize(prize_id):
    """删除奖品"""
    conn = get_db_connection()
    if not conn:
        return False, "数据库连接失败"

    try:
        with conn.cursor() as cursor:
            sql = "DELETE FROM lottery_prizes WHERE id = %s"
            cursor.execute(sql, (prize_id,))

        conn.commit()
        return True, "奖品删除成功"
    except Exception as e:
        logger.error(f"删除奖品失败: {e}")
        conn.rollback()
        return False, f"删除奖品失败: {str(e)}"
    finally:
        conn.close()

def draw_lottery(steamid, cost, draw_type):
    """抽奖逻辑"""
    conn = get_db_connection()
    if not conn:
        return False, "数据库连接失败", None

    try:
        # 获取用户信息
        user = get_user_by_steamid(steamid)
        if not user:
            return False, "获取用户信息失败", None

        # 检查用户积分是否足够
        if user['points'] < cost:
            return False, "积分不足，无法抽奖", None

        # 获取所有激活的奖品
        with conn.cursor() as cursor:
            sql = """
            SELECT id, item_id, item_name, probability, image_path
            FROM lottery_prizes
            WHERE is_active = 1
            """
            cursor.execute(sql)
            prizes = cursor.fetchall()

            # 将Decimal类型转换为float
            for prize in prizes:
                if 'probability' in prize and isinstance(prize['probability'], Decimal):
                    prize['probability'] = float(prize['probability'])

        if not prizes:
            return False, "当前没有可抽取的奖品", None

        # 计算总概率
        total_probability = sum(float(prize['probability']) for prize in prizes)

        # 随机抽取奖品
        random_num = random.uniform(0, total_probability)
        current_sum = 0
        selected_prize = None

        for prize in prizes:
            current_sum += float(prize['probability'])
            if random_num <= current_sum:
                selected_prize = prize
                break

        # 如果没有选中奖品（理论上不应该发生），选择第一个奖品
        if not selected_prize and prizes:
            selected_prize = prizes[0]

        # 开始事务
        conn.begin()

        # 1. 扣除用户积分
        with conn.cursor() as cursor:
            sql = "UPDATE users SET points = points - %s WHERE steamid = %s"
            cursor.execute(sql, (cost, steamid))

        # 2. 添加到用户仓库
        now = datetime.now()
        with conn.cursor() as cursor:
            sql = """
            INSERT INTO user_inventory
            (steamid, item_name, item_id, quantity, purchase_date)
            VALUES (%s, %s, %s, %s, %s)
            """
            cursor.execute(sql, (steamid, selected_prize['item_name'], selected_prize['item_id'], 1, now))

        # 3. 记录抽奖日志
        with conn.cursor() as cursor:
            sql = """
            INSERT INTO lottery_logs
            (steamid, item_id, item_name, points_cost, draw_time, draw_type)
            VALUES (%s, %s, %s, %s, %s, %s)
            """
            cursor.execute(sql, (
                steamid,
                selected_prize['item_id'],
                selected_prize['item_name'],
                cost,
                now,
                draw_type
            ))

        # 提交事务
        conn.commit()

        # 清除用户缓存
        clear_user_cache(steamid)

        # 获取更新后的用户积分
        updated_user = get_user_by_steamid(steamid, use_cache=False)
        updated_points = updated_user['points'] if updated_user else 0

        return True, "抽奖成功", {
            'prize': selected_prize,
            'cost': cost,
            'points_before': user['points'],
            'points_after': updated_points
        }

    except Exception as e:
        logger.error(f"抽奖失败: {e}")
        if conn:
            conn.rollback()
        return False, f"抽奖失败: {str(e)}", None
    finally:
        if conn:
            conn.close()

def batch_draw_lottery(steamid, single_cost, times, draw_type):
    """批量抽奖"""
    total_cost = single_cost * times

    # 获取用户信息
    user = get_user_by_steamid(steamid)
    if not user:
        return False, "获取用户信息失败", None

    # 检查用户积分是否足够
    if user['points'] < total_cost:
        return False, "积分不足，无法抽奖", None

    conn = get_db_connection()
    if not conn:
        return False, "数据库连接失败", None

    try:
        # 获取所有激活的奖品
        with conn.cursor() as cursor:
            sql = """
            SELECT id, item_id, item_name, probability, image_path
            FROM lottery_prizes
            WHERE is_active = 1
            """
            cursor.execute(sql)
            prizes = cursor.fetchall()

            # 将Decimal类型转换为float
            for prize in prizes:
                if 'probability' in prize and isinstance(prize['probability'], Decimal):
                    prize['probability'] = float(prize['probability'])

        if not prizes:
            return False, "当前没有可抽取的奖品", None

        # 计算总概率
        total_probability = sum(float(prize['probability']) for prize in prizes)

        # 开始事务
        conn.begin()

        # 1. 扣除用户积分
        with conn.cursor() as cursor:
            sql = "UPDATE users SET points = points - %s WHERE steamid = %s"
            cursor.execute(sql, (total_cost, steamid))

        # 2. 进行多次抽奖
        results = []
        now = datetime.now()

        for _ in range(times):
            # 随机抽取奖品
            random_num = random.uniform(0, total_probability)
            current_sum = 0
            selected_prize = None

            for prize in prizes:
                current_sum += float(prize['probability'])
                if random_num <= current_sum:
                    selected_prize = prize
                    break

            # 如果没有选中奖品（理论上不应该发生），选择第一个奖品
            if not selected_prize and prizes:
                selected_prize = prizes[0]

            # 添加到结果列表
            results.append(selected_prize)

            # 添加到用户仓库
            with conn.cursor() as cursor:
                sql = """
                INSERT INTO user_inventory
                (steamid, item_name, item_id, quantity, purchase_date)
                VALUES (%s, %s, %s, %s, %s)
                """
                cursor.execute(sql, (steamid, selected_prize['item_name'], selected_prize['item_id'], 1, now))

            # 记录抽奖日志
            with conn.cursor() as cursor:
                sql = """
                INSERT INTO lottery_logs
                (steamid, item_id, item_name, points_cost, draw_time, draw_type)
                VALUES (%s, %s, %s, %s, %s, %s)
                """
                cursor.execute(sql, (
                    steamid,
                    selected_prize['item_id'],
                    selected_prize['item_name'],
                    single_cost,
                    now,
                    draw_type
                ))

        # 提交事务
        conn.commit()

        # 清除用户缓存
        clear_user_cache(steamid)

        # 获取更新后的用户积分
        updated_user = get_user_by_steamid(steamid, use_cache=False)
        updated_points = updated_user['points'] if updated_user else 0

        # 统计结果
        prize_counts = {}
        for result in results:
            item_name = result['item_name']
            if item_name in prize_counts:
                prize_counts[item_name]['count'] += 1
            else:
                prize_counts[item_name] = {
                    'prize': result,
                    'count': 1
                }

        summary = list(prize_counts.values())

        return True, "批量抽奖成功", {
            'results': results,
            'summary': summary,
            'total_cost': total_cost,
            'points_before': user['points'],
            'points_after': updated_points
        }

    except Exception as e:
        logger.error(f"批量抽奖失败: {e}")
        if conn:
            conn.rollback()
        return False, f"批量抽奖失败: {str(e)}", None
    finally:
        if conn:
            conn.close()

def get_lottery_history(steamid, limit=20):
    """获取用户抽奖历史"""
    conn = get_db_connection()
    history = []

    if conn:
        try:
            with conn.cursor() as cursor:
                sql = """
                SELECT id, item_id, item_name, points_cost, draw_time, draw_type
                FROM lottery_logs
                WHERE steamid = %s
                ORDER BY draw_time DESC
                LIMIT %s
                """
                cursor.execute(sql, (steamid, limit))
                history = cursor.fetchall()
        except Exception as e:
            logger.error(f"获取抽奖历史失败: {e}")
        finally:
            conn.close()

    return history

def get_global_lottery_history(limit=50):
    """获取全局抽奖历史，包含所有用户"""
    conn = get_db_connection()
    history = []

    if conn:
        try:
            with conn.cursor() as cursor:
                sql = """
                SELECT l.id, l.steamid, u.username, l.item_id, l.item_name, l.points_cost, l.draw_time, l.draw_type
                FROM lottery_logs l
                LEFT JOIN users u ON l.steamid = u.steamid
                ORDER BY l.draw_time DESC
                LIMIT %s
                """
                cursor.execute(sql, (limit,))
                history = cursor.fetchall()
        except Exception as e:
            logger.error(f"获取全局抽奖历史失败: {e}")
        finally:
            conn.close()

    return history

# 路由定义
@lottery_bp.route('/lottery')
def lottery_page():
    """抽奖页面"""
    # 验证用户是否已登录
    if 'steamid' not in session:
        flash('请先登录后再访问抽奖中心')
        return redirect(url_for('login'))

    steamid = session.get('steamid')
    user = get_user_by_steamid(steamid)

    if not user:
        flash('获取用户信息失败')
        return redirect(url_for('login'))

    # 获取奖品列表
    prizes = get_all_prizes()

    # 获取抽奖配置
    config = get_lottery_config()

    # 获取用户抽奖历史
    user_history = get_lottery_history(steamid)

    # 获取全局抽奖历史
    global_history = get_global_lottery_history(50)

    return render_template('lottery.html',
                          active_page='entertainment',
                          user=user,
                          prizes=prizes,
                          config=config,
                          user_history=user_history,
                          global_history=global_history)

@lottery_bp.route('/api/lottery/draw', methods=['POST'])
def api_draw_lottery():
    """抽奖API"""
    if 'steamid' not in session:
        return jsonify({'success': False, 'message': '请先登录'})

    steamid = session.get('steamid')
    data = request.json
    draw_type = data.get('type', 'single')

    # 获取抽奖配置
    config = get_lottery_config()

    if draw_type == 'single':
        cost = config['single_cost']
        success, message, result = draw_lottery(steamid, cost, 'single')
    elif draw_type == 'ten':
        cost = config['ten_cost']
        success, message, result = batch_draw_lottery(steamid, cost // 10, 10, 'ten')
    elif draw_type == 'hundred':
        cost = config['hundred_cost']
        success, message, result = batch_draw_lottery(steamid, cost // 100, 100, 'hundred')
    else:
        return jsonify({'success': False, 'message': '无效的抽奖类型'})

    if success:
        response = {'success': True, 'message': message, 'result': result}
        return current_app.response_class(
            json.dumps(response, cls=DecimalJSONEncoder),
            mimetype='application/json'
        )
    else:
        return jsonify({'success': False, 'message': message})

# 管理员路由
@lottery_bp.route('/admin/lottery')
def admin_lottery():
    """抽奖管理页面"""
    # 验证用户是否已登录且是管理员
    if 'steamid' not in session:
        flash('请先登录')
        return redirect(url_for('login'))

    steamid = session.get('steamid')
    user = get_user_by_steamid(steamid)

    if not user or not user.get('is_admin'):
        flash('您没有管理员权限')
        return redirect(url_for('home'))

    # 获取奖品列表
    prizes = get_all_prizes()

    # 获取抽奖配置
    config = get_lottery_config()

    # 获取所有物品数据用于选择
    all_items = get_all_data()

    return render_template('admin_lottery.html',
                          active_page='admin',
                          user=user,
                          prizes=prizes,
                          config=config,
                          all_items=all_items)

@lottery_bp.route('/api/admin/lottery/prizes', methods=['GET'])
def api_get_prizes():
    """获取所有奖品API"""
    # 验证用户是否已登录且是管理员
    if 'steamid' not in session:
        return jsonify({'success': False, 'message': '请先登录'})

    steamid = session.get('steamid')
    user = get_user_by_steamid(steamid)

    if not user or not user.get('is_admin'):
        return jsonify({'success': False, 'message': '您没有管理员权限'})

    prizes = get_all_prizes()
    return jsonify({'success': True, 'prizes': prizes})

@lottery_bp.route('/api/admin/lottery/prize', methods=['POST'])
def api_add_prize():
    """添加奖品API"""
    # 验证用户是否已登录且是管理员
    if 'steamid' not in session:
        return jsonify({'success': False, 'message': '请先登录'})

    steamid = session.get('steamid')
    user = get_user_by_steamid(steamid)

    if not user or not user.get('is_admin'):
        return jsonify({'success': False, 'message': '您没有管理员权限'})

    data = request.json
    item_id = data.get('item_id')
    item_name = data.get('item_name')
    probability = data.get('probability')
    image_path = data.get('image_path')

    if not item_id or not item_name or probability is None:
        return jsonify({'success': False, 'message': '请提供完整的奖品信息'})

    success, message = add_prize(item_id, item_name, probability, image_path, steamid)
    return jsonify({'success': success, 'message': message})

@lottery_bp.route('/api/admin/lottery/prize/<int:prize_id>', methods=['PUT'])
def api_update_prize(prize_id):
    """更新奖品API"""
    # 验证用户是否已登录且是管理员
    if 'steamid' not in session:
        return jsonify({'success': False, 'message': '请先登录'})

    steamid = session.get('steamid')
    user = get_user_by_steamid(steamid)

    if not user or not user.get('is_admin'):
        return jsonify({'success': False, 'message': '您没有管理员权限'})

    data = request.json
    item_id = data.get('item_id')
    item_name = data.get('item_name')
    probability = data.get('probability')
    image_path = data.get('image_path')
    is_active = data.get('is_active', True)

    if not item_id or not item_name or probability is None:
        return jsonify({'success': False, 'message': '请提供完整的奖品信息'})

    success, message = update_prize(prize_id, item_id, item_name, probability, image_path, is_active)
    return jsonify({'success': success, 'message': message})

@lottery_bp.route('/api/admin/lottery/prize/<int:prize_id>', methods=['DELETE'])
def api_delete_prize(prize_id):
    """删除奖品API"""
    # 验证用户是否已登录且是管理员
    if 'steamid' not in session:
        return jsonify({'success': False, 'message': '请先登录'})

    steamid = session.get('steamid')
    user = get_user_by_steamid(steamid)

    if not user or not user.get('is_admin'):
        return jsonify({'success': False, 'message': '您没有管理员权限'})

    success, message = delete_prize(prize_id)
    return jsonify({'success': success, 'message': message})

@lottery_bp.route('/api/admin/lottery/config', methods=['GET'])
def api_get_config():
    """获取抽奖配置API"""
    # 验证用户是否已登录且是管理员
    if 'steamid' not in session:
        return jsonify({'success': False, 'message': '请先登录'})

    steamid = session.get('steamid')
    user = get_user_by_steamid(steamid)

    if not user or not user.get('is_admin'):
        return jsonify({'success': False, 'message': '您没有管理员权限'})

    config = get_lottery_config()
    return jsonify({'success': True, 'config': config})

@lottery_bp.route('/api/admin/lottery/config', methods=['POST'])
def api_update_config():
    """更新抽奖配置API"""
    # 验证用户是否已登录且是管理员
    if 'steamid' not in session:
        return jsonify({'success': False, 'message': '请先登录'})

    steamid = session.get('steamid')
    user = get_user_by_steamid(steamid)

    if not user or not user.get('is_admin'):
        return jsonify({'success': False, 'message': '您没有管理员权限'})

    data = request.json
    config = {
        'single_cost': int(data.get('single_cost', DEFAULT_LOTTERY_CONFIG['single_cost'])),
        'ten_cost': int(data.get('ten_cost', DEFAULT_LOTTERY_CONFIG['ten_cost'])),
        'hundred_cost': int(data.get('hundred_cost', DEFAULT_LOTTERY_CONFIG['hundred_cost']))
    }

    success = save_lottery_config(config)
    if success:
        return jsonify({'success': True, 'message': '配置更新成功', 'config': config})
    else:
        return jsonify({'success': False, 'message': '配置更新失败'})
