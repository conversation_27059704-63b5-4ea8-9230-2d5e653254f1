{% extends "base.html" %}
{% block title %}登录 - SCUM物品代码网站{% endblock %}

{% block head_extra %}
<style>
    .login-container {
        max-width: 500px;
        margin: 50px auto;
        padding: 0 15px;
    }
    
    .login-card {
        background: linear-gradient(145deg, #2a2a2a, #333333);
        border-radius: 12px;
        box-shadow: 0 8px 30px rgba(0, 0, 0, 0.3);
        overflow: hidden;
        border: 1px solid rgba(255, 255, 255, 0.1);
    }
    
    .login-header {
        background: linear-gradient(135deg, #00a8ff, #0077cc);
        padding: 25px 30px;
        text-align: center;
        position: relative;
    }
    
    .login-header h1 {
        color: #fff;
        font-size: 1.8rem;
        margin: 0;
        font-weight: 600;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    }
    
    .login-header p {
        color: rgba(255, 255, 255, 0.9);
        margin: 10px 0 0;
        font-size: 1rem;
    }
    
    .login-body {
        padding: 30px;
        background-color: #2a2a2a;
    }
    
    .login-form .form-group {
        margin-bottom: 25px;
    }
    
    .login-form label {
        display: block;
        margin-bottom: 8px;
        color: #e0e0e0;
        font-weight: 500;
        font-size: 0.95rem;
    }
    
    .login-form .input-group {
        position: relative;
    }
    
    .login-form .input-icon {
        position: absolute;
        left: 15px;
        top: 50%;
        transform: translateY(-50%);
        color: #00a8ff;
        font-size: 1.1rem;
        z-index: 10;
    }
    
    .login-form .form-control {
        height: 50px;
        background-color: #333;
        border: 1px solid rgba(255, 255, 255, 0.1);
        border-radius: 8px;
        color: #fff;
        padding: 10px 15px 10px 45px;
        font-size: 1rem;
        transition: all 0.3s ease;
        width: 100%;
    }
    
    .login-form .form-control:focus {
        background-color: #3a3a3a;
        border-color: #00a8ff;
        box-shadow: 0 0 0 3px rgba(0, 168, 255, 0.25);
    }
    
    .login-form .form-text {
        color: #aaa;
        font-size: 0.85rem;
        margin-top: 8px;
    }
    
    .login-form .btn-login {
        height: 50px;
        background: linear-gradient(135deg, #00a8ff, #0077cc);
        border: none;
        border-radius: 8px;
        color: #fff;
        font-size: 1rem;
        font-weight: 600;
        width: 100%;
        margin-top: 10px;
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    
    .login-form .btn-login:hover {
        background: linear-gradient(135deg, #0095e0, #0066b3);
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0, 168, 255, 0.3);
    }
    
    .login-form .btn-login i {
        margin-right: 8px;
        font-size: 1.1rem;
    }
    
    .login-footer {
        text-align: center;
        padding: 20px 30px;
        background-color: #222;
        border-top: 1px solid rgba(255, 255, 255, 0.05);
    }
    
    .login-footer p {
        color: #aaa;
        margin: 0;
        font-size: 0.9rem;
    }
    
    .login-footer a {
        color: #00a8ff;
        text-decoration: none;
        font-weight: 500;
    }
    
    .login-footer a:hover {
        text-decoration: underline;
    }
    
    .alert-custom {
        background-color: rgba(220, 53, 69, 0.2);
        color: #ff6b6b;
        border: 1px solid rgba(220, 53, 69, 0.3);
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 25px;
        display: flex;
        align-items: center;
    }
    
    .alert-custom i {
        margin-right: 10px;
        font-size: 1.2rem;
    }
    
    /* 动画效果 */
    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(20px); }
        to { opacity: 1; transform: translateY(0); }
    }
    
    .login-card {
        animation: fadeIn 0.5s ease-out forwards;
    }
</style>
{% endblock %}

{% block content %}
<div class="login-container">
    <div class="login-card">
        <div class="login-header">
            <h1><i class="fab fa-steam"></i> Steam ID 登录</h1>
            <p>请输入已在游戏中注册的 Steam ID</p>
        </div>
        
        <div class="login-body">
            {% if error_message %}
            <div class="alert-custom">
                <i class="fas fa-exclamation-circle"></i>
                <span>{{ error_message }}</span>
            </div>
            {% endif %}
            
            <form action="{{ url_for('login') }}" method="post" id="loginForm" class="login-form">
                <div class="form-group">
                    <label for="steamid">Steam ID</label>
                    <div class="input-group">
                        <i class="fab fa-steam input-icon"></i>
                        <input type="text" class="form-control" id="steamid" name="steamid" 
                               placeholder="输入您的 Steam ID" value="{{ steamid if steamid else '' }}" required>
                    </div>
                    <small class="form-text">请输入已在游戏内注册的 Steam ID</small>
                </div>
                
                {% if show_password %}
                <div class="form-group">
                    <label for="password">密码</label>
                    <div class="input-group">
                        <i class="fas fa-lock input-icon"></i>
                        <input type="password" class="form-control" id="password" name="password" 
                               placeholder="输入密码" required>
                    </div>
                </div>
                {% endif %}
                
                <button type="submit" class="btn-login">
                    {% if show_password %}
                    <i class="fas fa-sign-in-alt"></i>登录
                    {% else %}
                    <i class="fas fa-arrow-right"></i>验证
                    {% endif %}
                </button>
            </form>
        </div>
        
        <div class="login-footer">
            <p>遇到问题？<a href="#">联系游戏管理员</a></p>
        </div>
    </div>
</div>
{% endblock %}
