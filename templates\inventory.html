{% extends "base.html" %}

{% block title %}物品仓库 - 7788商城{% endblock %}

{% block content %}
<div class="container">
    <div class="page-header">
        <h1><i class="fas fa-warehouse"></i> 我的物品仓库</h1>
        <p>这里存放着您购买的所有物品</p>
    </div>

    <!-- 仓库标签页 -->
    <div class="inventory-tabs">
        <button class="tab-btn active" data-tab="inventory-tab">我的物品</button>
        <button class="tab-btn" data-tab="history-tab">购买记录</button>
    </div>

    <!-- 物品管理工具栏 -->
    <div class="inventory-toolbar">
        <div class="toolbar-left">
            <button id="batch-ship-btn" class="batch-action-btn" disabled>
                <i class="fas fa-shipping-fast"></i> 批量发货
            </button>
            <span id="selected-count" class="selected-count">0 个物品已选择</span>
        </div>
        <div class="toolbar-right">
            <div class="toggle-container">
                <label class="toggle-switch">
                    <input type="checkbox" id="show-shipped-toggle" {% if show_shipped %}checked{% endif %}>
                    <span class="toggle-slider"></span>
                </label>
                <span class="toggle-label">显示已发货物品</span>
            </div>
        </div>
    </div>

    <!-- 操作提示 -->
    <div class="inventory-tips">
        <p><i class="fas fa-info-circle"></i> 提示：选中多个物品后可使用批量发货功能</p>
    </div>

    <!-- 我的物品标签内容 -->
    <div id="inventory-tab" class="tab-content active">
        {% if inventory_items %}
        <div class="inventory-grid">
            {% for item in inventory_items %}
            <div class="inventory-item {% if item.all_used %}used{% endif %}">
                <div class="item-image">
                    {% if not item.all_used %}
                    <div class="item-checkbox">
                        <input type="checkbox" class="item-select"
                               data-ids="{{ item.unused_item_ids|join(',') }}"
                               data-items="{{ item.unused_items|tojson }}">
                    </div>
                    {% endif %}
                    <img src="{{ item.image_path or '/static/uploads/default-item.jpg' }}" alt="{{ item.item_name }}">
                    {% if item.all_used %}
                    <div class="used-overlay">
                        <span>已使用</span>
                    </div>
                    {% endif %}
                </div>
                <div class="item-content">
                    <div class="item-header">
                        <h3 class="item-name">{{ item.item_name }}</h3>
                        <span class="item-quantity">x{{ item.total_quantity }}</span>
                    </div>
                    <div class="item-details">
                        <p class="item-date"><i class="far fa-calendar-alt"></i> {{ item.purchase_date.strftime('%Y-%m-%d %H:%M') }}</p>
                        <div class="item-status">
                            {% if item.all_used %}
                            <span class="status-used"><i class="fas fa-check-circle"></i> 已使用</span>
                            {% elif item.any_used %}
                            <span class="status-partial"><i class="fas fa-adjust"></i> 部分可用</span>
                            {% else %}
                            <span class="status-available"><i class="fas fa-circle"></i> 可用</span>
                            {% endif %}
                        </div>
                    </div>
                    <div class="item-actions">
                        {% if not item.all_used %}
                        <button class="ship-btn"
                                data-ids="{{ item.unused_item_ids|join(',') }}"
                                data-items="{{ item.unused_items|tojson }}"
                                data-name="{{ item.item_name }}"
                                data-steamid="{{ user.steamid }}">
                            <i class="fas fa-shipping-fast"></i> 发货 ({{ item.total_quantity }})
                        </button>
                        {% else %}
                        <span class="shipped-status"><i class="fas fa-check-circle"></i> 已发货</span>
                        {% endif %}
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
        {% else %}
        <div class="empty-inventory">
            <div class="empty-icon">
                <i class="fas fa-box-open"></i>
            </div>
            <h2>您的仓库还是空的</h2>
            <p>前往物品页面购买一些物品吧！</p>
            <a href="/items" class="primary-btn"><i class="fas fa-shopping-cart"></i> 去购物</a>
        </div>
        {% endif %}
    </div>

    <!-- 购买记录标签内容 -->
    <div id="history-tab" class="tab-content">
        {% if purchase_history %}
        <div class="purchase-history">
            <table class="history-table">
                <thead>
                    <tr>
                        <th>物品名称</th>
                        <th>数量</th>
                        <th>花费积分</th>
                        <th>购买时间</th>
                    </tr>
                </thead>
                <tbody>
                    {% for record in purchase_history %}
                    <tr>
                        <td>{{ record.item_name }}</td>
                        <td>{{ record.quantity }}</td>
                        <td>{{ record.points_spent | format_number }}</td>
                        <td>{{ record.purchase_date.strftime('%Y-%m-%d %H:%M') }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="empty-history">
            <div class="empty-icon">
                <i class="fas fa-receipt"></i>
            </div>
            <h2>暂无购买记录</h2>
            <p>您还没有购买过任何物品</p>
            <a href="/items" class="primary-btn"><i class="fas fa-shopping-cart"></i> 去购物</a>
        </div>
        {% endif %}
    </div>
</div>

<!-- 操作提示工具提示 -->
<div id="actionTooltip" class="copy-tooltip">操作成功!</div>

<!-- 数量选择模态框 -->
<div id="quantityModal" class="modal">
    <div class="modal-content">
        <div class="modal-header">
            <h2>选择发货数量 <span id="totalQuantityDisplay"></span></h2>
            <span class="close">&times;</span>
        </div>
        <div class="modal-body">
            <div id="quantityItems"></div>
        </div>
        <div class="modal-footer">
            <button id="confirmShipBtn" class="primary-btn">确认发货</button>
            <button id="cancelShipBtn" class="secondary-btn">取消</button>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // 标签页切换
    const tabBtns = document.querySelectorAll('.tab-btn');
    const tabContents = document.querySelectorAll('.tab-content');

    tabBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            // 移除所有active类
            tabBtns.forEach(b => b.classList.remove('active'));
            tabContents.forEach(c => c.classList.remove('active'));

            // 添加active类到当前点击的按钮
            this.classList.add('active');

            // 显示对应内容
            const tabId = this.getAttribute('data-tab');
            document.getElementById(tabId).classList.add('active');
        });
    });

    const actionTooltip = document.getElementById('actionTooltip');

    // 显示提示工具提示
    function showTooltip(message, element = null) {
        if (!actionTooltip) return;

        actionTooltip.textContent = message;
        actionTooltip.style.opacity = '1';

        if (element) {
            actionTooltip.style.top = (element.getBoundingClientRect().top - 60) + 'px';
            actionTooltip.style.left = (element.getBoundingClientRect().left + element.offsetWidth / 2) + 'px';
        } else {
            actionTooltip.style.top = '50%';
            actionTooltip.style.left = '50%';
            actionTooltip.style.transform = 'translate(-50%, -50%)';
        }

        setTimeout(function() {
            actionTooltip.style.opacity = '0';
            actionTooltip.style.transform = '';
        }, 2000);
    }

    // 定期检查WebSocket连接状态
    let serverConnected = false;

    function checkServerStatus() {
        fetch('/check_ws_status')
            .then(response => response.json())
            .then(data => {
                serverConnected = data.connected;
                updateShipButtons();
            })
            .catch(error => {
                console.error('检查服务器状态失败:', error);
                serverConnected = false;
                updateShipButtons();
            });
    }

    // 立即检查一次状态
    checkServerStatus();

    // 每30秒检查一次状态
    setInterval(checkServerStatus, 30000);

    // 更新发货按钮状态
    function updateShipButtons() {
        const shipButtons = document.querySelectorAll('.ship-btn');

        shipButtons.forEach(button => {
            if (serverConnected) {
                button.removeAttribute('disabled');
                button.title = '发货物品';
                button.classList.remove('disabled');
            } else {
                button.setAttribute('disabled', 'disabled');
                button.title = '发货服务器连接失败，请稍后再试';
                button.classList.add('disabled');
            }
        });
    }

    // 数量选择模态框相关代码
    const quantityModal = document.getElementById('quantityModal');
    const quantityItems = document.getElementById('quantityItems');
    const confirmShipBtn = document.getElementById('confirmShipBtn');
    const cancelShipBtn = document.getElementById('cancelShipBtn');
    const closeModalBtn = quantityModal.querySelector('.close');

    // 当前选中的物品数据
    let currentItemData = {
        ids: [],
        items: [],
        name: '',
        steamid: '',
        button: null
    };

    // 关闭模态框
    function closeModal() {
        quantityModal.style.display = 'none';
    }

    // 绑定关闭按钮事件
    closeModalBtn.addEventListener('click', closeModal);
    cancelShipBtn.addEventListener('click', closeModal);

    // 点击模态框外部关闭
    window.addEventListener('click', function(event) {
        if (event.target === quantityModal) {
            closeModal();
        }
    });

    // 创建数量选择器HTML
    function createQuantitySelectors(items) {
        console.log('创建数量选择器输入数据:', items);

        // 确保有效的数组
        if (!Array.isArray(items) || items.length === 0) {
            console.log('无效的物品数据');
            return '<div class="no-items">没有可用的物品数据</div>';
        }

        let html = '';

        // 直接使用已合并的物品数据
        items.forEach(item => {
            // 确保有效的物品数据
            const itemId = item.id || 'unknown';
            const quantity = parseInt(item.quantity) || 1;
            const name = item.name || '物品';

            // 生成单个物品的HTML
            html += `
            <div class="quantity-item" data-id="${itemId}">
                <div class="quantity-info">
                    <span class="item-name">${name}</span>
                    <span class="item-quantity">数量: ${quantity}</span>
                </div>
                <div class="quantity-selector">
                    <label for="quantity-${itemId}">选择发货数量:</label>
                    <div class="quantity-control">
                        <button type="button" class="quantity-btn minus" data-id="${itemId}">-</button>
                        <input type="number" id="quantity-${itemId}" class="quantity-input"
                               min="1" max="${quantity}" value="${quantity}" data-id="${itemId}">
                        <button type="button" class="quantity-btn plus" data-id="${itemId}">+</button>
                    </div>
                </div>
            </div>
            `;
        });

        // 如果没有生成任何HTML，显示错误信息
        if (!html) {
            html = '<div class="no-items">没有可用的物品数据</div>';
        }

        return html;
    }

    // 处理数量变化
    function handleQuantityChange(event) {
        const target = event.target;

        if (target.classList.contains('quantity-btn')) {
            const itemId = target.getAttribute('data-id');
            const input = document.getElementById(`quantity-${itemId}`);
            const currentValue = parseInt(input.value) || 0;
            const max = parseInt(input.getAttribute('max')) || 1;

            if (target.classList.contains('plus') && currentValue < max) {
                input.value = currentValue + 1;
            } else if (target.classList.contains('minus') && currentValue > 1) {
                input.value = currentValue - 1;
            }
        } else if (target.classList.contains('quantity-input')) {
            // 限制输入值范围
            const max = parseInt(target.getAttribute('max')) || 1;
            let value = parseInt(target.value) || 0;

            if (value < 1) value = 1;
            if (value > max) value = max;

            target.value = value;
        }
    }

    // 绑定数量选择器事件
    quantityItems.addEventListener('click', handleQuantityChange);
    quantityItems.addEventListener('input', handleQuantityChange);

    // 安全解析JSON数据
    function safeParseJSON(jsonString, defaultValue = []) {
        if (!jsonString) return defaultValue;

        // 在控制台输出原始数据，便于调试
        console.log('原始数据字符串:', jsonString);
        console.log('原始数据类型:', typeof jsonString);
        console.log('原始数据长度:', jsonString.length);
        console.log('原始数据前20个字符:', jsonString.substring(0, 20));

        // 如果已经是对象，直接返回
        if (typeof jsonString === 'object' && jsonString !== null) {
            console.log('已经是对象，直接返回');
            return jsonString;
        }

        // 处理HTML实体编码
        let cleanedString = jsonString;
        try {
            // 尝试解码HTML实体
            cleanedString = cleanedString.replace(/&quot;/g, '"')
                                         .replace(/&#39;/g, "'")
                                         .replace(/&lt;/g, '<')
                                         .replace(/&gt;/g, '>')
                                         .replace(/&amp;/g, '&');
        } catch (e) {
            console.error('解码HTML实体失败:', e);
        }

        // 处理可能的转义问题
        try {
            // 如果字符串以\"开头，可能是转义问题
            if (cleanedString.includes('\\"')) {
                cleanedString = cleanedString.replace(/\\"/g, '"');
                console.log('处理转义后的数据:', cleanedString);
            }

            // 如果字符串以引号包裹，可能需要去除外层引号
            if ((cleanedString.startsWith('"') && cleanedString.endsWith('"')) ||
                (cleanedString.startsWith('\'') && cleanedString.endsWith('\'')))
            {
                cleanedString = cleanedString.substring(1, cleanedString.length - 1);
                console.log('去除外层引号后的数据:', cleanedString);
            }
        } catch (e) {
            console.error('处理转义失败:', e);
        }

        // 尝试清理空白字符和特殊字符
        try {
            cleanedString = cleanedString.replace(/\s+/g, ' ').trim();

            // 确保数组格式正确
            if (!cleanedString.startsWith('[') && !cleanedString.startsWith('{')) {
                // 如果不是以[或{开头，尝试添加
                if (cleanedString.includes('{') && cleanedString.includes('}')) {
                    cleanedString = '[' + cleanedString + ']';
                    console.log('添加数组括号后的数据:', cleanedString);
                }
            }
        } catch (e) {
            console.error('清理数据失败:', e);
        }

        try {
            // 尝试解析JSON
            return JSON.parse(cleanedString);
        } catch (e) {
            console.error('解析JSON失败:', e);

            // 尝试手动解析
            try {
                console.log('尝试手动解析数组');
                const items = [];

                // 尝试提取ID和数量
                // 更宽松的正则表达式，匹配各种可能的格式
                const regex = /["']?id["']?\s*:\s*["']?([^"',}]+)["']?[^}]*["']?quantity["']?\s*:\s*([\d.]+)/g;
                let match;

                while ((match = regex.exec(cleanedString)) !== null) {
                    items.push({
                        id: match[1],
                        quantity: parseInt(match[2]) || 1
                    });
                }

                console.log('手动解析结果:', items);

                if (items.length > 0) {
                    return items;
                }

                // 如果上面的方法失败，尝试从字符串中提取数字作为数量
                const numberRegex = /\d+/g;
                const numbers = [];
                while ((match = numberRegex.exec(cleanedString)) !== null) {
                    numbers.push(parseInt(match[0]));
                }

                if (numbers.length > 0) {
                    console.log('从字符串中提取的数字:', numbers);
                    // 假设第一个数字是ID，第二个数字是数量
                    return [{
                        id: numbers[0].toString(),
                        quantity: numbers.length > 1 ? numbers[1] : 1
                    }];
                }
            } catch (e2) {
                console.error('手动解析失败:', e2);
            }

            // 如果所有方法都失败，返回默认值
            return defaultValue;
        }
    }

    // 为物品添加名称信息
    function addNameToItems(items, name) {
        return items.map(item => ({
            ...item,
            name: name
        }));
    }

    // 发货功能
    const shipButtons = document.querySelectorAll('.ship-btn');

    shipButtons.forEach(button => {
        button.addEventListener('click', function() {
            // 如果按钮被禁用，不执行操作
            if (this.hasAttribute('disabled')) {
                customAlert('发货服务器连接失败，请稍后再试');
                return;
            }

            const idsString = this.getAttribute('data-ids') || '';
            const ids = idsString.split(',').filter(id => id.trim() !== '');
            const name = this.getAttribute('data-name') || '物品';
            const steamid = this.getAttribute('data-steamid') || '';

            // 输出原始数据到控制台
            console.log('点击发货按钮:', this);
            console.log('data-ids:', idsString);
            console.log('data-name:', name);
            console.log('data-items 原始属性:', this.getAttribute('data-items'));

            // 安全解析物品数据
            const itemsData = this.getAttribute('data-items') || '[]';
            let items = safeParseJSON(itemsData, []);

            console.log('解析后的物品数据:', items);

            // 如果解析失败或数组为空，创建简单的物品数据
            if (!items || items.length === 0) {
                console.log('创建简单物品数据从 IDs:', ids);
                items = ids.map(id => ({ id, quantity: 1 }));
            }

            // 添加物品名称
            items = addNameToItems(items, name);
            console.log('添加名称后的物品数据:', items);

            if (ids.length === 0) {
                customAlert('没有可发货的物品');
                return;
            }

            // 如果只有一个物品且数量为1，直接发货
            if (items.length === 1 && items[0].quantity === 1) {
                // 创建数量映射，只包含这一个物品
                const singleQuantities = {};
                singleQuantities[String(ids[0])] = 1;
                console.log('单个物品直接发货，数量映射:', singleQuantities);
                sendShippingRequest(ids, singleQuantities, name, this, 1);
                return;
            }

            // 合并相同物品，确保每个物品只显示一次
            const mergedItems = [];
            const itemMap = {};

            // 按物品名称分组
            items.forEach(item => {
                if (!itemMap[name]) {
                    // 如果这个物品还没有在映射中，创建一个新条目
                    itemMap[name] = {
                        id: item.id,  // 使用第一个物品的ID
                        name: name,
                        quantity: 0,
                        ids: []  // 存储所有相关的ID
                    };
                    mergedItems.push(itemMap[name]);
                }

                // 累加数量并添加ID
                itemMap[name].quantity += (item.quantity || 1);
                if (item.id && !itemMap[name].ids.includes(item.id)) {
                    itemMap[name].ids.push(item.id);
                }
            });

            console.log('合并后的物品数据:', mergedItems);

            // 计算总数量
            const totalItemQuantity = mergedItems.reduce((sum, item) => sum + (parseInt(item.quantity) || 1), 0);

            // 保存当前物品数据
            currentItemData = {
                ids,
                items: mergedItems,
                name,
                steamid,
                button: this,
                totalQuantity: totalItemQuantity
            };

            // 显示总数量
            const totalQuantityDisplay = document.getElementById('totalQuantityDisplay');
            if (totalQuantityDisplay) {
                totalQuantityDisplay.textContent = `(总数量: ${totalItemQuantity})`;
            }

            // 显示数量选择模态框
            quantityItems.innerHTML = createQuantitySelectors(mergedItems);
            quantityModal.style.display = 'block';
        });
    });

    // 确认发货按钮事件
    confirmShipBtn.addEventListener('click', function() {
        console.log('确认发货按钮点击');
        console.log('当前物品数据:', currentItemData);

        // 收集所有数量输入
        const quantities = {};
        const inputs = quantityItems.querySelectorAll('.quantity-input');
        let totalSelectedQuantity = 0; // 用于计算总选择数量

        console.log('数量输入元素:', inputs.length);

        inputs.forEach(input => {
            const itemId = input.getAttribute('data-id');
            const quantity = parseInt(input.value) || 0;
            console.log(`物品 ${itemId} 选择数量: ${quantity}`);

            if (quantity > 0) {
                totalSelectedQuantity += quantity; // 累计总选择数量

                // 如果是合并的物品，需要将数量分配给所有相关的ID
                const mergedItem = currentItemData.items.find(item => item.id === itemId);

                if (mergedItem && mergedItem.ids && mergedItem.ids.length > 0) {
                    console.log(`合并物品 ${itemId} 有多个ID:`, mergedItem.ids);

                    // 如果选择的数量小于等于可用ID数量，只发送那些数量
                    if (quantity <= mergedItem.ids.length) {
                        // 只使用前 quantity 个ID
                        for (let i = 0; i < quantity; i++) {
                            if (i < mergedItem.ids.length) {
                                quantities[String(mergedItem.ids[i])] = 1;
                            }
                        }
                    } else {
                        // 如果选择的数量大于ID数量，需要将数量分配给每个ID
                        // 计算每个ID应该发送的数量
                        const idsCount = mergedItem.ids.length;
                        const baseQuantity = Math.floor(quantity / idsCount); // 基础数量
                        let remainder = quantity % idsCount; // 余数

                        mergedItem.ids.forEach(id => {
                            let idQuantity = baseQuantity;
                            if (remainder > 0) {
                                idQuantity++;
                                remainder--;
                            }
                            quantities[String(id)] = idQuantity;
                        });
                    }
                } else {
                    // 如果是普通物品，直接设置数量
                    quantities[String(itemId)] = quantity;
                }
            }
        });

        console.log('最终发货数量映射:', quantities);
        console.log('总选择数量:', totalSelectedQuantity);

        // 如果没有选择任何数量，显示错误
        if (totalSelectedQuantity <= 0) {
            customAlert('请选择要发货的数量');
            return;
        }

        // 关闭模态框
        closeModal();

        // 只发送有数量的物品ID
        const selectedIds = Object.keys(quantities);
        console.log('只发送有数量的物品IDs:', selectedIds);

        // 使用选择的总数量发送发货请求
        sendShippingRequest(selectedIds, quantities, currentItemData.name, currentItemData.button, totalSelectedQuantity);
    });

    // 发送发货请求函数
    function sendShippingRequest(ids, quantities, name, buttonElement, selectedQuantity) {
        // 确保ids是数组
        if (!Array.isArray(ids)) {
            ids = [];
        }

        // 确保quantities是对象
        if (typeof quantities !== 'object' || quantities === null) {
            quantities = {};
        }

        // 计算总数量用于确认消息
        let totalQuantity = 0;

        // 如果提供了选择数量，直接使用
        if (selectedQuantity && selectedQuantity > 0) {
            totalQuantity = selectedQuantity;
            console.log('使用提供的选择数量:', totalQuantity);
        }
        // 否则计算数量
        else if (Object.keys(quantities).length > 0) {
            // 如果有指定数量，累加所有数量
            totalQuantity = Object.values(quantities).reduce((sum, qty) => sum + (parseInt(qty) || 0), 0);
            console.log('从数量映射计算总数量:', totalQuantity);
        } else {
            // 如果没有指定数量，使用物品数量总和
            const itemsData = buttonElement.getAttribute('data-items') || '[]';
            const items = safeParseJSON(itemsData, []);

            if (items && items.length > 0) {
                items.forEach(item => {
                    totalQuantity += parseInt(item.quantity) || 1;
                });
                console.log('从物品数据计算总数量:', totalQuantity);
            } else {
                totalQuantity = ids.length; // 如果没有物品数据，使用ID数量
                console.log('使用ID数量作为总数量:', totalQuantity);
            }
        }

        // 如果总数量为0，显示错误
        if (totalQuantity <= 0) {
            customAlert('没有可发货的物品');
            return;
        }

        console.log('最终发货数量:', totalQuantity);

        customConfirm(`确定要发货 ${totalQuantity} 个"${name || '物品'}"吗？此操作不可撤销。`, (confirmed) => {
            if (confirmed) {
                // 检查服务器状态
                fetch('/check_ws_status')
                    .then(response => response.json())
                    .then(data => {
                        if (!data.connected) {
                            customAlert('发货服务器连接失败，请稍后再试');
                            return;
                        }

                        // 发送发货请求
                        fetch('/mark_as_used', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'X-Requested-With': 'XMLHttpRequest'
                            },
                            body: JSON.stringify({
                                item_ids: ids,
                                ship_item: true,  // 标记需要发货
                                quantities: quantities  // 发送数量信息
                            })
                        })
                        .then(response => response.json())
                        .then(data => {
                            if (data.success) {
                                // 显示成功提示
                                const resultQuantity = data.total_quantity || (data.shipped_items ? data.shipped_items.length : 0);
                                showTooltip(`发货成功！${resultQuantity} 个物品已发送`, buttonElement);

                                // 延迟刷新页面，让用户看到提示
                                setTimeout(() => {
                                    window.location.reload();
                                }, 1500);
                            } else {
                                customAlert(data.message || '操作失败，请稍后再试');
                            }
                        })
                        .catch(error => {
                            console.error('请求失败:', error);
                            customAlert('网络错误，请稍后重试');
                        });
                    })
                    .catch(error => {
                        console.error('检查服务器状态失败:', error);
                        customAlert('无法检查发货服务器状态，请稍后再试');
                    });
            }
        });
    }

    // 批量发货功能
    const batchShipBtn = document.getElementById('batch-ship-btn');
    const itemCheckboxes = document.querySelectorAll('.item-select');
    const selectedCountDisplay = document.getElementById('selected-count');
    let selectedItems = [];

    // 防止复选框点击事件冒泡
    itemCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('click', function(e) {
            e.stopPropagation(); // 阻止事件冒泡
        });
    });

    // 防止复选框容器点击事件冒泡
    const checkboxContainers = document.querySelectorAll('.item-checkbox');
    checkboxContainers.forEach(container => {
        container.addEventListener('click', function(e) {
            e.stopPropagation(); // 阻止事件冒泡
        });
    });

    // 更新选中物品计数和批量发货按钮状态
    function updateSelectedCount() {
        selectedItems = [];
        let totalSelectedQuantity = 0;

        console.log('更新选中物品计数');

        itemCheckboxes.forEach(checkbox => {
            if (checkbox.checked) {
                try {
                    // 获取物品ID列表
                    const ids = (checkbox.getAttribute('data-ids') || '').split(',').filter(id => id.trim() !== '');
                    console.log('选中物品IDs:', ids);

                    // 尝试获取物品数量
                    let itemQuantity = 1; // 默认数量为1

                    // 尝试从父元素获取数量信息
                    const parentItem = checkbox.closest('.inventory-item');
                    if (parentItem) {
                        const quantityElement = parentItem.querySelector('.item-quantity');
                        if (quantityElement) {
                            // 从如 "x5" 的文本中提取数字
                            const quantityText = quantityElement.textContent;
                            const match = quantityText.match(/\d+/);
                            if (match) {
                                itemQuantity = parseInt(match[0]) || 1;
                                console.log('从父元素获取数量:', itemQuantity);
                            }
                        }
                    }

                    // 将所有ID添加到选中列表
                    ids.forEach(id => {
                        selectedItems.push(id);
                    });

                    // 累计总数量
                    totalSelectedQuantity += itemQuantity;
                } catch (e) {
                    console.error('处理选中物品时出错:', e);
                    // 出错时使用安全的默认值
                    const ids = (checkbox.getAttribute('data-ids') || '').split(',').filter(id => id.trim() !== '');
                    ids.forEach(id => {
                        selectedItems.push(id);
                        totalSelectedQuantity += 1;
                    });
                }
            }
        });

        console.log('选中物品总数:', totalSelectedQuantity);
        console.log('选中物品IDs:', selectedItems);

        // 更新显示，显示总数量
        selectedCountDisplay.textContent = `${totalSelectedQuantity} 个物品已选择`;

        // 更新批量发货按钮文本
        batchShipBtn.innerHTML = `<i class="fas fa-shipping-fast"></i> 批量发货 (${totalSelectedQuantity})`;

        if (selectedItems.length > 0 && serverConnected) {
            batchShipBtn.removeAttribute('disabled');
        } else {
            batchShipBtn.setAttribute('disabled', 'disabled');
        }
    }

    // 绑定复选框事件
    itemCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', updateSelectedCount);
    });

    // 批量发货按钮点击事件
    batchShipBtn.addEventListener('click', function() {
        console.log('批量发货按钮点击');

        if (selectedItems.length === 0) {
            customAlert('请选择要发货的物品');
            return;
        }

        console.log('选中的物品IDs:', selectedItems);

        // 获取选中物品的总数量
        let totalSelectedQuantity = 0;
        let selectedItemsInfo = [];

        // 收集所有选中的物品数据
        itemCheckboxes.forEach(checkbox => {
            if (checkbox.checked) {
                try {
                    // 获取物品名称
                    const parentItem = checkbox.closest('.inventory-item');
                    let itemName = '物品';
                    let itemQuantity = 1;

                    if (parentItem) {
                        // 获取名称
                        const nameElement = parentItem.querySelector('.item-name');
                        if (nameElement) {
                            itemName = nameElement.textContent.trim();
                        }

                        // 获取数量
                        const quantityElement = parentItem.querySelector('.item-quantity');
                        if (quantityElement) {
                            const quantityText = quantityElement.textContent;
                            const match = quantityText.match(/\d+/);
                            if (match) {
                                itemQuantity = parseInt(match[0]) || 1;
                            }
                        }
                    }

                    console.log(`选中物品: ${itemName}, 数量: ${itemQuantity}`);

                    // 获取物品ID列表
                    const ids = (checkbox.getAttribute('data-ids') || '').split(',').filter(id => id.trim() !== '');

                    // 添加到选中物品信息
                    selectedItemsInfo.push({
                        name: itemName,
                        quantity: itemQuantity,
                        ids: ids
                    });

                    // 累计总数量
                    totalSelectedQuantity += itemQuantity;
                } catch (e) {
                    console.error('处理选中物品时出错:', e);
                }
            }
        });

        console.log('选中物品信息:', selectedItemsInfo);
        console.log('总选中数量:', totalSelectedQuantity);

        // 如果没有选中物品或总数量为0，显示错误
        if (selectedItemsInfo.length === 0 || totalSelectedQuantity === 0) {
            customAlert('没有可发货的物品');
            return;
        }

        // 创建数量映射
        const quantities = {};
        selectedItems.forEach(id => {
            quantities[String(id)] = 1; // 默认每个ID发货一个
        });

        // 如果所有物品都只有一个，直接发货
        if (selectedItemsInfo.every(item => item.quantity === 1)) {
            console.log('所有物品都是单个数量，直接发货');

            // 显示确认对话框
            customConfirm(`确定要发货 ${totalSelectedQuantity} 个选中物品吗？此操作不可撤销。`, (confirmed) => {
                if (confirmed) {
                    // 只发送有数量的物品ID
                    const selectedIds = Object.keys(quantities);
                    console.log('只发送选中的物品IDs:', selectedIds);
                    sendShippingRequest(selectedIds, quantities, '选中物品', batchShipBtn, totalSelectedQuantity);
                }
            });
            return;
        }

        // 如果有多个物品或数量大于1，显示数量选择模态框
        // 准备模态框数据
        const mergedItems = selectedItemsInfo.map(item => ({
            id: item.ids[0] || 'unknown',  // 使用第一个ID作为主键
            name: item.name,
            quantity: item.quantity,
            ids: item.ids
        }));

        // 保存当前物品数据
        currentItemData = {
            ids: selectedItems,
            items: mergedItems,
            name: '选中物品',
            steamid: '',
            button: batchShipBtn,
            totalQuantity: totalSelectedQuantity
        };

        // 显示总数量
        const totalQuantityDisplay = document.getElementById('totalQuantityDisplay');
        if (totalQuantityDisplay) {
            totalQuantityDisplay.textContent = `(总数量: ${totalSelectedQuantity})`;
        }

        // 显示数量选择模态框
        quantityItems.innerHTML = createQuantitySelectors(mergedItems);
        quantityModal.style.display = 'block';
    });

    // 显示/隐藏已发货物品切换
    const showShippedToggle = document.getElementById('show-shipped-toggle');
    showShippedToggle.addEventListener('change', function() {
        const showShipped = this.checked ? '1' : '0';
        window.location.href = `/inventory?show_shipped=${showShipped}`;
    });

    // 添加样式
    const style = document.createElement('style');
    style.textContent = `
        /* 禁用按钮样式 */
        .ship-btn.disabled {
            background-color: #999;
            cursor: not-allowed;
            box-shadow: none;
        }
        .ship-btn.disabled:hover {
            background-color: #999;
            transform: none;
            box-shadow: none;
        }

        /* 模态框样式 */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            overflow: auto;
            background-color: rgba(0,0,0,0.5);
        }

        .modal-content {
            background-color: #fefefe;
            margin: 10% auto;
            padding: 0;
            border-radius: 8px;
            box-shadow: 0 4px 16px rgba(0,0,0,0.2);
            width: 80%;
            max-width: 600px;
            animation: modalFadeIn 0.3s;
        }

        @keyframes modalFadeIn {
            from {opacity: 0; transform: translateY(-20px);}
            to {opacity: 1; transform: translateY(0);}
        }

        .modal-header {
            padding: 15px 20px;
            background-color: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
            border-top-left-radius: 8px;
            border-top-right-radius: 8px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-header h2 {
            margin: 0;
            font-size: 1.25rem;
            color: #333;
        }

        .close {
            color: #aaa;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }

        .close:hover {
            color: #333;
        }

        .modal-body {
            padding: 20px;
            max-height: 60vh;
            overflow-y: auto;
        }

        .modal-footer {
            padding: 15px 20px;
            background-color: #f8f9fa;
            border-top: 1px solid #dee2e6;
            border-bottom-left-radius: 8px;
            border-bottom-right-radius: 8px;
            display: flex;
            justify-content: flex-end;
            gap: 10px;
        }

        /* 数量选择器样式 */
        .quantity-item {
            margin-bottom: 15px;
            padding: 15px;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            background-color: #f8f9fa;
        }

        .quantity-info {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
        }

        .quantity-selector {
            display: flex;
            flex-direction: column;
        }

        .quantity-selector label {
            margin-bottom: 5px;
            font-weight: bold;
        }

        .quantity-control {
            display: flex;
            align-items: center;
        }

        .quantity-btn {
            width: 36px;
            height: 36px;
            border: 1px solid #dee2e6;
            background-color: #f8f9fa;
            font-size: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            user-select: none;
        }

        .quantity-btn.minus {
            border-radius: 4px 0 0 4px;
        }

        .quantity-btn.plus {
            border-radius: 0 4px 4px 0;
        }

        .quantity-input {
            width: 60px;
            height: 36px;
            border: 1px solid #dee2e6;
            border-left: none;
            border-right: none;
            text-align: center;
            font-size: 16px;
        }

        .quantity-input::-webkit-inner-spin-button,
        .quantity-input::-webkit-outer-spin-button {
            -webkit-appearance: none;
            margin: 0;
        }

        .quantity-input {
            -moz-appearance: textfield;
        }
    `;
    document.head.appendChild(style);
});
</script>
{% endblock %}