from flask import Flask, render_template, request, redirect, url_for, send_from_directory, session, flash, jsonify
import requests
import math
import os
import json
import logging
import pymysql
import time
from datetime import datetime, timedelta
import random
import functools
from pymysql.cursors import DictCursor
from dbutils.pooled_db import PooledDB  # 添加数据库连接池

# 导入通行证系统
from battlepass import battlepass_bp
import battlepass

app = Flask(__name__)
app.secret_key = os.urandom(24)

# 注册通行证蓝图
app.register_blueprint(battlepass_bp)

# 设置通行证模块的函数引用
battlepass.get_db_connection = lambda: get_db_connection()
battlepass.get_user_by_steamid = get_user_by_steamid
battlepass.clear_user_cache = clear_user_cache

# 设置静态文件缓存时间（1个月）
app.config['SEND_FILE_MAX_AGE_DEFAULT'] = 2592000

# 设置会话的持久化时间为30天
app.config['PERMANENT_SESSION_LIFETIME'] = timedelta(days=30)

# 自定义静态文件路由，添加缓存控制
@app.route('/static/<path:filename>')
def custom_static(filename):
    cache_timeout = 2592000  # 1个月的秒数
    response = send_from_directory('static', filename)
    response.cache_control.max_age = cache_timeout
    response.cache_control.public = True
    response.headers['Expires'] = (datetime.now() + timedelta(seconds=cache_timeout)).strftime('%a, %d %b %Y %H:%M:%S GMT')
    return response


# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("app.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("app")

# 数据库连接配置
DB_CONFIG = {
    'host': '***************',
    'user': 'SCUM',
    'password': 'Zcj199671228',
    'database': 'scum',
    'charset': 'utf8mb4',
    'cursorclass': pymysql.cursors.DictCursor
}

# 创建数据库连接池
DB_POOL = PooledDB(
    creator=pymysql,  # 使用的数据库模块
    maxconnections=10,  # 连接池最大连接数
    mincached=2,  # 初始化时连接池中至少创建的空闲连接
    maxcached=5,  # 连接池中最大空闲连接数
    blocking=True,  # 连接池中如果没有可用连接时，是否阻塞等待
    maxusage=None,  # 一个连接最多被重复使用的次数，None表示无限制
    **DB_CONFIG
)

# 用户信息缓存
USER_CACHE = {}
USER_CACHE_TTL = 60  # 缓存有效期（秒）
USER_CACHE_TIMESTAMPS = {}  # 存储缓存时间戳

def get_db_connection():
    """从连接池获取数据库连接"""
    try:
        return DB_POOL.connection()
    except Exception as e:
        logger.error(f"数据库连接失败: {e}")
        return None

def get_user_by_steamid(steamid, use_cache=True):
    """根据steamid获取用户信息，支持缓存"""
    # 检查缓存是否有效
    current_time = time.time()
    if use_cache and steamid in USER_CACHE:
        if current_time - USER_CACHE_TIMESTAMPS.get(steamid, 0) < USER_CACHE_TTL:
            return USER_CACHE[steamid]

    conn = get_db_connection()
    if not conn:
        return None

    try:
        with conn.cursor() as cursor:
            sql = "SELECT * FROM users WHERE steamid = %s"
            cursor.execute(sql, (steamid,))
            user = cursor.fetchone()

            # 更新缓存
            if user and use_cache:
                USER_CACHE[steamid] = user
                USER_CACHE_TIMESTAMPS[steamid] = current_time

            return user
    except Exception as e:
        logger.error(f"获取用户信息失败: {e}")
        return None
    finally:
        conn.close()

def get_user_by_username(username):
    """根据用户名获取用户信息"""
    conn = get_db_connection()
    if not conn:
        return None

    try:
        with conn.cursor() as cursor:
            sql = "SELECT * FROM users WHERE username = %s"
            cursor.execute(sql, (username,))
            user = cursor.fetchone()
            return user
    except Exception as e:
        logger.error(f"获取用户信息失败: {e}")
        return None
    finally:
        conn.close()

def create_user(steamid, username, password):
    """创建新用户"""
    conn = get_db_connection()
    if not conn:
        return False

    try:
        with conn.cursor() as cursor:
            sql = """INSERT INTO users
                    (steamid, username, password)
                    VALUES (%s, %s, %s)"""
            cursor.execute(sql, (steamid, username, password))
        conn.commit()
        return True
    except Exception as e:
        logger.error(f"创建用户失败: {e}")
        return False
    finally:
        conn.close()

# 全局用户信息处理
@app.context_processor
def inject_user():
    """将用户信息注入到所有模板中"""
    user = None
    if 'steamid' in session:
        steamid = session['steamid']
        user = get_user_by_steamid(steamid)

        # 减少日志记录详细程度，避免打印整个用户对象
        if user:
            logger.debug(f"已注入用户信息到模板: steamid={steamid}")

    return {'user': user}

ITEMS_PER_PAGE = 50  # 原来可能是较小的值，如10或20
LOCAL_DATA_DIR = "data"
LOCAL_DATA_FILE = os.path.join(LOCAL_DATA_DIR, "all-data.json")

def get_all_data():
    """
    获取所有商品数据
    优先从本地缓存读取，如果本地缓存不存在则从API获取
    """
    # 优先从本地缓存读取
    if os.path.exists(LOCAL_DATA_FILE):
        try:
            with open(LOCAL_DATA_FILE, 'r', encoding='utf-8') as f:
                logger.info("从本地缓存读取数据")
                return json.load(f)
        except Exception as e:
            logger.error(f"读取本地缓存出错: {e}")
            # 如果本地缓存读取失败，继续尝试从API获取

    # 如果本地缓存不存在或读取失败，则从API获取
    logger.warning("本地缓存不存在或读取失败，尝试从API获取数据")
    url = "http://127.0.0.1:5778/api/all-data"

    try:
        response = requests.get(url)

        # 检查请求是否成功
        if response.status_code == 200:
            return response.json()
        else:
            logger.error(f"请求失败，状态码: {response.status_code}")
            return None
    except Exception as e:
        logger.error(f"请求发生错误: {e}")
        return None

def get_categories(data):
    """提取所有物品包的分类"""
    if not data or 'packages' not in data:
        return []

    categories = set()
    for package in data['packages']:
        if 'category' in package and package['category']:
            categories.add(package['category'])

    return sorted(list(categories))

def sort_items(items, sort_method):
    """根据指定方法排序物品"""
    if not items:
        logger.info(f"排序函数收到空列表，返回原始列表")
        return items

    # 记录排序前的数据结构，帮助调试
    if items and len(items) > 0:
        sample_item = items[0]
        logger.info(f"排序前的数据示例: {sample_item}")
        logger.info(f"排序方法: {sort_method}")
        logger.info(f"排序前的第一项points值: {sample_item.get('points')}, 类型: {type(sample_item.get('points'))}")

    try:
        if sort_method == 'name':
            # 按名称排序
            if isinstance(items, list) and items:
                if 'packageName' in items[0]:
                    logger.info(f"按packageName排序, 项目数: {len(items)}")
                    return sorted(items, key=lambda x: str(x.get('packageName', '')).lower())
                elif 'coordinateName' in items[0]:
                    logger.info(f"按coordinateName排序, 项目数: {len(items)}")
                    return sorted(items, key=lambda x: str(x.get('coordinateName', '')).lower())
                else:
                    logger.warning(f"无法确定排序字段，项目不包含packageName或coordinateName")
        elif sort_method == 'points_asc':
            # 点数从低到高
            logger.info(f"按points升序排序, 项目数: {len(items)}")

            # 确保points值是数字
            result = []
            for item in items:
                # 深拷贝物品以避免修改原始数据
                item_copy = dict(item)

                # 获取points并转换为数字
                points_value = item.get('points', 0)
                try:
                    if isinstance(points_value, str):
                        # 移除非数字字符
                        clean_points = ''.join(c for c in points_value if c.isdigit() or c == '.')
                        if clean_points:
                            points_value = float(clean_points)
                        else:
                            points_value = 0
                    else:
                        points_value = float(points_value) if points_value is not None else 0
                except (ValueError, TypeError):
                    points_value = 0

                item_copy['_sort_points'] = points_value
                result.append(item_copy)

            # 排序并返回原始项目（不包含临时排序字段）
            sorted_items = sorted(result, key=lambda x: x.get('_sort_points', 0))
            logger.info(f"排序后的第一项: {sorted_items[0] if sorted_items else 'None'}")

            # 返回原始项目（不含临时排序字段）
            return [item for item in sorted_items]

        elif sort_method == 'points_desc':
            # 点数从高到低
            logger.info(f"按points降序排序, 项目数: {len(items)}")

            # 确保points值是数字
            result = []
            for item in items:
                # 深拷贝物品以避免修改原始数据
                item_copy = dict(item)

                # 获取points并转换为数字
                points_value = item.get('points', 0)
                try:
                    if isinstance(points_value, str):
                        # 移除非数字字符
                        clean_points = ''.join(c for c in points_value if c.isdigit() or c == '.')
                        if clean_points:
                            points_value = float(clean_points)
                        else:
                            points_value = 0
                    else:
                        points_value = float(points_value) if points_value is not None else 0
                except (ValueError, TypeError):
                    points_value = 0

                item_copy['_sort_points'] = points_value
                result.append(item_copy)

            # 排序并返回原始项目（不包含临时排序字段）
            sorted_items = sorted(result, key=lambda x: x.get('_sort_points', 0), reverse=True)
            logger.info(f"排序后的第一项: {sorted_items[0] if sorted_items else 'None'}")

            # 返回原始项目（不含临时排序字段）
            return [item for item in sorted_items]
    except Exception as e:
        logger.error(f"排序出错: {str(e)}")
        # 如果排序出错，返回原始列表
        return items

    logger.info(f"没有应用任何排序，返回原始列表")
    return items

def paginate_items(items, page, per_page):
    """分页处理物品列表"""
    if not items:
        logger.info("分页函数收到空列表，返回空结果")
        return [], 0, 0

    try:
        total_items = len(items)

        # 只有当物品数量大于每页显示数量时，才需要分页
        if total_items <= per_page:
            logger.info(f"物品总数({total_items})小于等于每页显示数量({per_page})，无需分页")
            return items, 1, 1

        total_pages = math.ceil(total_items / per_page)
        page = max(1, min(page, total_pages))  # 确保页码有效

        start_idx = (page - 1) * per_page
        end_idx = min(start_idx + per_page, total_items)

        logger.info(f"分页: 总项目数={total_items}, 总页数={total_pages}, 当前页={page}, 范围={start_idx}-{end_idx}")
        return items[start_idx:end_idx], total_pages, page
    except Exception as e:
        logger.error(f"分页处理出错: {e}")
        # 如果出错，返回所有项目和页码1
        return items, 1, 1

@app.route('/')
def index():
    """首页路由，默认跳转到武器分类"""
    return redirect(url_for('home'))

@app.route('/home')
def home():
    """主页路由"""
    return render_template('home.html', active_page='home')

@app.route('/items')
def items():
    """物品代码路由，直接显示武器分类的物品"""
    category = '武器'  # 默认显示武器分类
    page = int(request.args.get('page', 1))
    sort = request.args.get('sort', 'name')

    logger.info(f"访问物品页面，默认分类: {category}, 排序: {sort}, 页码: {page}")

    data = get_all_data()
    categories = get_categories(data)

    current_items = {'coordinates': [], 'packages': []}
    total_pages = 1
    display_category = category

    if data and 'packages' in data and data['packages']:
        # 根据分类过滤物品包
        filtered_packages = [p for p in data['packages'] if str(p.get('category', '')) == category]
        logger.info(f"过滤分类 '{category}' 后的物品数量: {len(filtered_packages)}")

        # 先排序
        sorted_packages = sort_items(filtered_packages, sort)

        # 确定是否需要分页
        if len(sorted_packages) <= ITEMS_PER_PAGE:
            logger.info(f"物品数量({len(sorted_packages)})不超过每页显示数量({ITEMS_PER_PAGE})，无需分页")
            current_items['packages'] = sorted_packages
            total_pages = 1
            current_page = 1
        else:
            # 需要分页
            paginated_packages, total_pages, current_page = paginate_items(
                sorted_packages, page, ITEMS_PER_PAGE
            )
            current_items['packages'] = paginated_packages

        logger.info(f"页面物品数量: {len(current_items['packages'])}, 总页数: {total_pages}")

    return render_template('items.html',
                          products=data,
                          current_items=current_items,
                          categories=categories,
                          active_category=category,
                          active_page='items',
                          display_category=display_category,
                          current_page=page,
                          total_pages=total_pages,
                          sort=sort)

@app.route('/sponsor')
def sponsor():
    """赞助页面路由"""
    if 'steamid' in session:
        steamid = session.get('steamid')
        user = get_user_by_steamid(steamid)
    else:
        user = None

    vip_levels = get_all_vip_levels()

    return render_template('sponsor.html', user=user, vip_levels=vip_levels, active_page='sponsor')

@app.route('/profile')
def profile():
    """用户个人信息页面"""
    if 'steamid' not in session:
        return redirect(url_for('login'))

    steamid = session.get('steamid')
    user = get_user_by_steamid(steamid)

    if not user:
        session.pop('steamid', None)
        session.pop('username', None)
        return redirect(url_for('login'))

    # 检查用户是否已登录，以及今天是否已签到
    is_signed_in_today = False
    last_sign_time = None
    # 添加当前时间变量供模板使用
    now = datetime.now()

    try:
        if user and user.get('last_sign_time'):
            last_sign_time = user.get('last_sign_time')
            today = now.date()

            # 将last_sign_time转换为日期对象进行比较
            if isinstance(last_sign_time, datetime):
                last_sign_date = last_sign_time.date()
            else:
                last_sign_date = datetime.strptime(str(last_sign_time), '%Y-%m-%d %H:%M:%S').date()

            is_signed_in_today = (today == last_sign_date)
    except Exception as e:
        logger.error(f"获取用户签到状态失败: {e}")

    # 减少无用的日志记录
    logger.debug(f"用户查看个人信息页面: steamid={steamid}")

    return render_template('profile.html',
                          user=user,
                          active_page='profile',
                          is_signed_in_today=is_signed_in_today,
                          last_sign_time=last_sign_time,
                          now=now,
                          timedelta=timedelta)

@app.route('/data')
def data():
    """数据页面"""
    # 验证用户是否已登录
    if 'steamid' not in session:
        flash('请先登录后再访问数据页面')
        return redirect(url_for('login'))

    # 获取排行榜数据（积分前10的用户）
    top_users = []
    total_users = 0
    total_kills = 0

    # 回收排行榜数据
    top_recyclers = []

    # 实时数据统计（最新的击杀和回收记录）
    recent_activities = []

    try:
        conn = get_db_connection()
        if conn:
            with conn.cursor() as cursor:
                # 获取排行榜数据
                sql = """SELECT steamid, username, country, points, kills, deaths, is_vip, vip_level, is_admin
                        FROM users
                        ORDER BY points DESC
                        LIMIT 10"""
                cursor.execute(sql)
                top_users = cursor.fetchall()

                # 获取统计数据
                cursor.execute("SELECT COUNT(*) as total FROM users")
                total_users = cursor.fetchone()['total']

                cursor.execute("SELECT SUM(kills) as total FROM users")
                result = cursor.fetchone()
                total_kills = result['total'] if result['total'] else 0

                # 获取回收排行榜数据
                sql = """SELECT r.steamid, u.username, r.item_name, SUM(r.quantity) as total_recycled, SUM(r.points_earned) as total_points
                        FROM recycle_logs r
                        JOIN users u ON r.steamid = u.steamid
                        GROUP BY r.steamid, u.username, r.item_name
                        ORDER BY total_recycled DESC
                        LIMIT 10"""
                cursor.execute(sql)
                top_recyclers = cursor.fetchall()

                # 获取最新的击杀记录
                sql = """SELECT k.id, k.steamid as killer_steamid, ku.username as killer_name,
                        k.victimid as victim_steamid, vu.username as victim_name,
                        k.weapon, k.distance, k.log_time, 'kill' as type
                        FROM kill_logs k
                        JOIN users ku ON k.steamid = ku.steamid
                        JOIN users vu ON k.victimid = vu.steamid
                        ORDER BY k.log_time DESC
                        LIMIT 5"""
                cursor.execute(sql)
                recent_kills = cursor.fetchall()

                # 获取最新的回收记录
                sql = """SELECT r.id, r.steamid, u.username, r.item_name, r.quantity,
                        r.points_earned, r.recycle_time, 'recycle' as type
                        FROM recycle_logs r
                        JOIN users u ON r.steamid = u.steamid
                        ORDER BY r.recycle_time DESC
                        LIMIT 5"""
                cursor.execute(sql)
                recent_recycles = cursor.fetchall()

                # 合并并按时间排序
                recent_activities = list(recent_kills) + list(recent_recycles)

                # 将时间戳转换为时间对象以便于比较
                for activity in recent_activities:
                    if 'log_time' in activity:
                        activity['time'] = datetime.fromtimestamp(activity['log_time'])
                    elif 'recycle_time' in activity:
                        activity['time'] = activity['recycle_time']

                # 按时间降序排序
                recent_activities = sorted(recent_activities, key=lambda x: x['time'], reverse=True)

                # 只取前10条
                recent_activities = recent_activities[:10]

            conn.close()
    except Exception as e:
        logger.error(f"获取数据页面数据失败: {e}")

    return render_template('data.html',
                          active_page='data',
                          top_users=top_users,
                          total_users=total_users,
                          total_kills=total_kills,
                          top_recyclers=top_recyclers,
                          recent_activities=recent_activities)

@app.route('/entertainment')
def entertainment():
    """娱乐版块页面"""
    # 验证用户是否已登录
    if 'steamid' not in session:
        flash('请先登录后再访问娱乐中心')
        return redirect(url_for('login'))

    return render_template('entertainment.html',
                          active_page='entertainment')

@app.route('/recycle')
def recycle():
    """物品回收页面"""
    # 获取所有数据
    data = get_all_data()

    # 提取可回收物品
    recycle_items = []
    total_recycled_count = 0

    if data and 'recycles' in data:
        recycle_items = data['recycles']
        logger.info(f"找到 {len(recycle_items)} 个回收物品")

        # 过滤可回收的物品
        recycle_items = [item for item in recycle_items if item.get('canRecycle', False)]
        logger.info(f"可回收物品数量: {len(recycle_items)}")

        # 获取回收统计数据
        conn = get_db_connection()
        recycle_stats = {}
        total_recycled_count = 0

        if conn:
            try:
                with conn.cursor() as cursor:
                    # 查询所有回收统计数据
                    sql = "SELECT item_name, total_recycled FROM recycle_stats"
                    cursor.execute(sql)
                    stats = cursor.fetchall()

                    # 将统计数据转换为字典
                    for stat in stats:
                        item_count = stat['total_recycled']
                        recycle_stats[stat['item_name']] = item_count
                        total_recycled_count += item_count

                    # 如果没有统计数据，也可以直接查询总数
                    if not stats:
                        sql = "SELECT SUM(quantity) as total FROM recycle_logs"
                        cursor.execute(sql)
                        result = cursor.fetchone()
                        if result and result['total']:
                            total_recycled_count = result['total']
            except Exception as e:
                logger.error(f"获取回收统计数据失败: {e}")
            finally:
                conn.close()

        # 将统计数据添加到回收物品中
        for item in recycle_items:
            item_name = item.get('recycleName', '')
            item['total_recycled'] = recycle_stats.get(item_name, 0)

        # 按回收积分降序排序
        recycle_items = sorted(recycle_items, key=lambda x: x.get('recyclePoints', 0), reverse=True)
    else:
        logger.warning(f"all-data.json 中没有找到 recycles 字段")
        if data:
            logger.info(f"all-data.json 中的字段: {list(data.keys())}")

    return render_template('recycle.html',
                          active_page='recycle',
                          recycle_items=recycle_items,
                          total_recycled_count=total_recycled_count)

@app.route('/sign_in', methods=['GET', 'POST'])
def sign_in():
    """用户签到"""
    if 'steamid' not in session:
        flash('请先登录再签到')
        return redirect(url_for('login'))

    # 如果是GET请求，重定向到娱乐页面
    if request.method == 'GET':
        return redirect(url_for('entertainment'))

    steamid = session.get('steamid')
    # 判断是否是AJAX请求
    is_ajax = request.headers.get('X-Requested-With') == 'XMLHttpRequest'

    # 检查今天是否已经签到
    try:
        conn = get_db_connection()
        if not conn:
            if is_ajax:
                return jsonify({'success': False, 'message': '数据库连接失败'})
            flash('数据库连接失败')
            return redirect(url_for('entertainment'))

        # 检查上次签到时间
        with conn.cursor() as cursor:
            sql = """SELECT last_sign_time, checkin_streak FROM users WHERE steamid = %s"""
            cursor.execute(sql, (steamid,))
            result = cursor.fetchone()

            today = datetime.now().date()
            checkin_streak = result.get('checkin_streak', 0) if result else 0

            if result and result.get('last_sign_time'):
                last_sign_time = result.get('last_sign_time')
                last_sign_date = last_sign_time.date() if isinstance(last_sign_time, datetime) else datetime.strptime(str(last_sign_time), '%Y-%m-%d %H:%M:%S').date()

                if today == last_sign_date:
                    if is_ajax:
                        return jsonify({'success': False, 'message': '今天已经签到过了，请明天再来'})
                    flash('今天已经签到过了，请明天再来')
                    return redirect(url_for('entertainment'))

                # 检查是否是连续签到（昨天有签到）
                yesterday = today - timedelta(days=1)
                is_consecutive = (last_sign_date == yesterday)

                if is_consecutive:
                    # 连续签到，连续天数+1
                    checkin_streak += 1
                else:
                    # 不是连续签到，重置连续天数为1
                    checkin_streak = 1
            else:
                # 首次签到，连续天数设为1
                checkin_streak = 1

        # 更新签到状态和积分
        base_reward = 1000  # 默认签到奖励
        # 先获取用户信息
        user = get_user_by_steamid(steamid)
        vip_level = user.get('vip_level', 0) if isinstance(user, dict) else 0
        sign_points = calculate_checkin_bonus(base_reward, vip_level)  # 根据VIP等级计算签到奖励
        bonus_points = 0    # 额外奖励积分
        messages = []

        # 如果连续签到达到10天，额外奖励1000积分
        if checkin_streak > 0 and checkin_streak % 10 == 0:
            bonus_points = 1000
            if vip_level > 0:
                bonus_points = int(bonus_points * (1 + vip_level * 0.2))  # VIP用户额外奖励也有加成
            messages.append(f'恭喜您连续签到{checkin_streak}天，获得额外奖励{bonus_points}积分！')
            if not is_ajax:
                flash(f'恭喜您连续签到{checkin_streak}天，获得额外奖励{bonus_points}积分！')

        total_points = sign_points + bonus_points

        # 显示VIP签到奖励提示
        if vip_level > 0:
            messages.append(f'VIP{vip_level}特权：签到奖励提升至{sign_points}积分！')
            if not is_ajax:
                flash(f'VIP{vip_level}特权：签到奖励提升至{sign_points}积分！')

        now = datetime.now()

        with conn.cursor() as cursor:
            # 更新用户积分、签到时间和连续签到天数
            sql = """UPDATE users SET points = points + %s, last_sign_time = %s, checkin_streak = %s,
                    checkin_days = checkin_days + 1 WHERE steamid = %s"""
            cursor.execute(sql, (total_points, now, checkin_streak, steamid))

            # 添加签到日志
            sql = """INSERT INTO gift_logs (steamid, gift_type, points, received_at) VALUES (%s, %s, %s, %s)"""
            cursor.execute(sql, (steamid, 0, total_points, now))  # 0表示签到奖励

        conn.commit()

        # 清除用户缓存，确保获取最新数据
        clear_user_cache(steamid)

        # 获取更新后的用户信息
        updated_user = get_user_by_steamid(steamid, use_cache=False)
        updated_points = updated_user.get('points', 0) if updated_user else 0

        success_message = f'签到成功，获得{sign_points}积分！{bonus_points > 0 and "额外奖励"+str(bonus_points)+"积分！" or ""}'
        messages.append(success_message)

        if not is_ajax:
            flash(success_message)

        # 如果是AJAX请求，返回JSON响应
        if is_ajax:
            return jsonify({
                'success': True,
                'message': '<br>'.join(messages),
                'points': updated_points,
                'checkin_streak': checkin_streak,
                'is_signed_in_today': True
            })

    except Exception as e:
        logger.error(f"签到处理出错: {e}")
        if is_ajax:
            return jsonify({'success': False, 'message': '签到失败，请稍后再试'})
        flash('签到失败，请稍后再试')
    finally:
        if conn:
            conn.close()

    return redirect(url_for('entertainment'))

@app.route('/category/<category>')
def category_view(category):
    """按分类展示物品"""
    page = int(request.args.get('page', 1))
    sort = request.args.get('sort', 'name')

    logger.info(f"访问分类: {category}, 排序: {sort}, 页码: {page}")

    data = get_all_data()
    categories = get_categories(data)

    current_items = {'coordinates': [], 'packages': []}
    total_pages = 1
    display_category = category

    if data:
        if category == '传送点' and 'coordinates' in data and data['coordinates']:
            # 只显示传送点
            logger.info(f"加载传送点分类，数量: {len(data['coordinates'])}")

            # 先排序
            sorted_coordinates = sort_items(data['coordinates'], sort)

            # 确定是否需要分页
            if len(sorted_coordinates) <= ITEMS_PER_PAGE:
                logger.info(f"传送点数量({len(sorted_coordinates)})不超过每页显示数量({ITEMS_PER_PAGE})，无需分页")
                current_items['coordinates'] = sorted_coordinates
                total_pages = 1
                current_page = 1
            else:
                # 需要分页
                paginated_coordinates, total_pages, current_page = paginate_items(
                    sorted_coordinates, page, ITEMS_PER_PAGE
                )
                current_items['coordinates'] = paginated_coordinates

            logger.info(f"页面传送点数量: {len(current_items['coordinates'])}, 总页数: {total_pages}")

        elif 'packages' in data and data['packages']:
            # 根据分类过滤物品包
            filtered_packages = [p for p in data['packages'] if str(p.get('category', '')) == category]
            logger.info(f"过滤分类 '{category}' 后的物品数量: {len(filtered_packages)}")

            # 先排序
            sorted_packages = sort_items(filtered_packages, sort)

            # 确定是否需要分页
            if len(sorted_packages) <= ITEMS_PER_PAGE:
                logger.info(f"物品数量({len(sorted_packages)})不超过每页显示数量({ITEMS_PER_PAGE})，无需分页")
                current_items['packages'] = sorted_packages
                total_pages = 1
                current_page = 1
            else:
                # 需要分页
                paginated_packages, total_pages, current_page = paginate_items(
                    sorted_packages, page, ITEMS_PER_PAGE
                )
                current_items['packages'] = paginated_packages

            logger.info(f"页面物品数量: {len(current_items['packages'])}, 总页数: {total_pages}")

    return render_template('items.html',
                          products=data,
                          current_items=current_items,
                          categories=categories,
                          active_category=category,
                          active_page='items',
                          display_category=display_category,
                          current_page=page,
                          total_pages=total_pages,
                          sort=sort)

@app.route('/search')
def search():
    """搜索物品"""
    query = request.args.get('q', '').strip().lower()
    page = int(request.args.get('page', 1))
    sort = request.args.get('sort', 'name')

    if not query:
        return redirect(url_for('items'))  # 重定向到物品页面而不是主页

    # 记录搜索关键词
    logger.info(f"搜索关键词: {query}")

    data = get_all_data()

    # 检查数据是否正确加载
    if not data:
        logger.error("未能加载数据，搜索失败")
        return render_template('index.html',
                              active_page='items',
                              error="搜索失败，无法加载数据")

    # 记录数据结构以帮助调试
    if 'packages' in data:
        logger.info(f"数据中包含 {len(data['packages'])} 个物品包")
        if data['packages'] and len(data['packages']) > 0:
            logger.info(f"物品包示例: {data['packages'][0]}")
    if 'coordinates' in data:
        logger.info(f"数据中包含 {len(data['coordinates'])} 个传送点")
        if data['coordinates'] and len(data['coordinates']) > 0:
            logger.info(f"传送点示例: {data['coordinates'][0]}")

    categories = get_categories(data)

    # 将搜索词拆分成多个关键词，支持多关键词搜索
    keywords = query.split()

    filtered_coordinates = []
    filtered_packages = []

    if data:
        # 搜索传送点
        if 'coordinates' in data and data['coordinates']:
            for c in data['coordinates']:
                coord_name = str(c.get('coordinateName', '')).lower()
                coord_remark = str(c.get('remark', '')).lower()

                # 如果任一关键词匹配，则添加到结果中
                match = False
                for keyword in keywords:
                    if keyword in coord_name or keyword in coord_remark:
                        match = True
                        break

                if match:
                    filtered_coordinates.append(c)
                    logger.info(f"匹配传送点: {c.get('coordinateName', '')}")

        # 搜索物品包
        if 'packages' in data and data['packages']:
            for p in data['packages']:
                package_name = str(p.get('packageName', '')).lower()
                package_remark = str(p.get('remark', '')).lower()
                package_category = str(p.get('category', '')).lower()

                # 检查每个关键词是否匹配任何字段
                match = False
                for keyword in keywords:
                    if (keyword in package_name or
                        keyword in package_remark or
                        keyword in package_category):
                        match = True
                        logger.info(f"匹配关键词 '{keyword}' 在物品: {p.get('packageName', '')}")
                        break

                if match:
                    filtered_packages.append(p)

    # 记录搜索结果数量
    logger.info(f"搜索结果: 找到 {len(filtered_coordinates)} 个传送点, {len(filtered_packages)} 个物品包")

    # 检查搜索结果总数
    total_items = len(filtered_coordinates) + len(filtered_packages)
    logger.info(f"搜索结果总数: {total_items} 项，每页显示: {ITEMS_PER_PAGE} 项")

    # 排序结果
    sorted_coordinates = sort_items(filtered_coordinates, sort)
    sorted_packages = sort_items(filtered_packages, sort)

    # 创建搜索结果数据结构
    search_results = {
        'coordinates': sorted_coordinates,
        'packages': sorted_packages
    }

    # 计算是否需要分页
    need_pagination = (len(sorted_coordinates) + len(sorted_packages)) > ITEMS_PER_PAGE
    if need_pagination:
        logger.info(f"搜索结果需要分页显示")
        total_pages = math.ceil((len(sorted_coordinates) + len(sorted_packages)) / ITEMS_PER_PAGE)
    else:
        logger.info(f"搜索结果不需要分页")
        total_pages = 1
        page = 1

    # 如果没有找到任何结果，添加提示信息
    if len(sorted_coordinates) == 0 and len(sorted_packages) == 0:
        logger.warning(f"搜索 '{query}' 未找到任何结果")

    return render_template('items.html',
                          products=search_results,  # 直接使用搜索结果作为products
                          current_items=search_results,  # 保持兼容
                          categories=categories,
                          active_category='search',
                          active_page='items',  # 保持在物品页面
                          display_category='search',
                          search_query=query,
                          current_page=page,
                          total_pages=total_pages,  # 动态计算总页数
                          sort=sort)

@app.route('/login', methods=['GET', 'POST'])
def login():
    """用户登录"""
    error_message = None

    if request.method == 'POST':
        steamid = request.form.get('steamid')
        password = request.form.get('password')

        if not steamid:
            error_message = "请输入Steam ID"
            return render_template('login.html', active_page='login', error_message=error_message)

        # 查询用户信息 - 登录时不使用缓存
        user = get_user_by_steamid(steamid, use_cache=False)

        # 减少调试日志，仅记录重要信息
        if user:
            logger.debug(f"用户登录: steamid={steamid}")

        # 检查用户是否存在
        if not user:
            # 用户不存在，显示错误信息
            error_message = "用户不存在，请联系管理员添加您的账号"
            return render_template('login.html', active_page='login', error_message=error_message)

        # 如果只提交了SteamID（第一步验证）
        if not password:
            # 检查密码是否为0（是否需要设置密码）
            if user['password'] == "0":
                # 密码为0，跳转到设置密码页面
                session['temp_steamid'] = steamid
                return redirect(url_for('set_password'))
            else:
                # 密码不为0，显示密码输入框进行第二步验证
                return render_template('login.html',
                                      active_page='login',
                                      steamid=steamid,
                                      show_password=True)
        else:
            # 用户提交了密码，验证密码是否正确
            if user['password'] == password:
                # 检查用户是否勾选了"记住我"
                remember = request.form.get('remember', 'off') == 'on'

                # 如果用户勾选了"记住我"，设置会话为永久性
                if remember:
                    session.permanent = True
                    logger.info(f"用户 {steamid} 选择了记住登录状态，设置30天有效期")

                # 登录成功，保存用户信息到session
                # 直接使用steamid作为用户标识，因为数据库中steamid是主键
                session['steamid'] = user['steamid']
                session['username'] = user['username'] or user['steamid']
                session['login_time'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                session['remember'] = remember

                # 更新用户登录状态
                try:
                    conn = get_db_connection()
                    if conn:
                        with conn.cursor() as cursor:
                            sql = "UPDATE users SET is_signed_in = 1 WHERE steamid = %s"
                            cursor.execute(sql, (steamid,))
                        conn.commit()
                        conn.close()
                except Exception as e:
                    logger.error(f"更新用户登录状态失败: {e}")

                # 登录成功跳转到个人页面
                return redirect(url_for('profile'))
            else:
                # 密码错误
                error_message = "密码错误，请重试"
                return render_template('login.html',
                                      active_page='login',
                                      steamid=steamid,
                                      show_password=True,
                                      error_message=error_message)

    # GET请求或其他情况
    return render_template('login.html',
                          active_page='login',
                          error_message=error_message)

@app.route('/set_password', methods=['GET', 'POST'])
def set_password():
    """设置用户密码"""
    if 'temp_steamid' not in session:
        return redirect(url_for('login'))

    if request.method == 'POST':
        new_password = request.form.get('new_password')
        confirm_password = request.form.get('confirm_password')

        if new_password != confirm_password:
            flash('两次输入的密码不一致')
            return render_template('set_password.html')

        if len(new_password) < 6:
            flash('密码长度至少为6位')
            return render_template('set_password.html')

        # 更新用户密码
        try:
            conn = get_db_connection()
            if not conn:
                flash('数据库连接失败')
                return render_template('set_password.html')

            with conn.cursor() as cursor:
                sql = "UPDATE users SET password = %s WHERE steamid = %s"
                cursor.execute(sql, (new_password, session['temp_steamid']))
            conn.commit()

            # 查询用户信息 - 不使用缓存获取最新数据
            with conn.cursor() as cursor:
                sql = "SELECT * FROM users WHERE steamid = %s"
                cursor.execute(sql, (session['temp_steamid'],))
                user = cursor.fetchone()

            if user:
                # 设置会话为永久性，使登录状态保持30天
                session.permanent = True

                # 设置session
                session['steamid'] = user['steamid']
                session['username'] = user['username'] or user['steamid']
                session['login_time'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

                # 更新登录状态
                with conn.cursor() as cursor:
                    sql = "UPDATE users SET is_signed_in = 1 WHERE steamid = %s"
                    cursor.execute(sql, (session['temp_steamid'],))
                conn.commit()

                # 清理临时变量
                session.pop('temp_steamid', None)

                # 更新用户缓存
                if user['steamid'] in USER_CACHE:
                    clear_user_cache(user['steamid'])

                return redirect(url_for('profile'))
            else:
                flash('无法获取用户信息')
        except Exception as e:
            logger.error(f"设置密码失败: {e}")
            flash(f'设置密码失败: {e}')
        finally:
            if conn:
                conn.close()

        return render_template('set_password.html')

    return render_template('set_password.html')

@app.route('/logout')
def logout():
    """用户登出"""
    steamid = session.get('steamid')
    if steamid:
        # 更新用户登录状态
        try:
            conn = get_db_connection()
            if conn:
                with conn.cursor() as cursor:
                    sql = "UPDATE users SET is_signed_in = 0 WHERE steamid = %s"
                    cursor.execute(sql, (steamid,))
                conn.commit()
                conn.close()

                # 清除用户缓存
                clear_user_cache(steamid)

        except Exception as e:
            logger.error(f"更新用户登录状态失败: {e}")

    session.pop('steamid', None)
    session.pop('username', None)
    return redirect(url_for('home'))

@app.route('/static/uploads/<path:filename>')
def serve_uploads(filename):
    """提供上传文件的访问接口"""
    return send_from_directory('static/uploads', filename)

@app.errorhandler(404)
def page_not_found(e):
    """处理404错误"""
    return render_template('index.html', error="页面不存在"), 404

@app.errorhandler(500)
def server_error(e):
    """处理500错误"""
    return render_template('index.html', error="服务器错误"), 500

@app.route('/dice_game', methods=['POST'])
def dice_game():
    """骰子游戏 - 支持批量摇骰子"""
    if 'steamid' not in session:
        return jsonify({
            'success': False,
            'message': '请先登录再进行游戏'
        }), 401

    steamid = session.get('steamid')

    # 获取用户信息
    user = get_user_by_steamid(steamid)
    if not user:
        return jsonify({
            'success': False,
            'message': '获取用户信息失败'
        }), 400

    # 获取请求数据
    data = request.get_json()
    game_cost = int(data.get('cost', 2000))  # 单次游戏花费
    times = int(data.get('times', 1))        # 游戏次数，默认为1

    # 计算总花费
    total_cost = game_cost * times
    current_points = user.get('points', 0)

    logger.info(f"骰子游戏 - 用户: {user.get('username')} ({steamid}), 当前积分: {current_points}, 游戏次数: {times}, 总花费: {total_cost}")

    # 检查用户积分是否足够
    if current_points < total_cost:
        return jsonify({
            'success': False,
            'message': '积分不足，无法进行游戏'
        }), 400

    try:
        # 单次游戏处理
        if times == 1:
            # 随机生成骰子点数 (1-6)
            dice_result = random.randint(1, 6)

            # 计算奖励积分
            if dice_result == 6:
                reward_points = game_cost * 2  # 双倍积分
            elif dice_result == 3:
                reward_points = int(game_cost * 1.25)  # 返还部分积分
            else:
                reward_points = 0

            # 计算净收益
            net_points = reward_points - game_cost
            success = net_points > 0

            # 更新后的积分
            new_points = current_points + net_points

            logger.info(f"骰子游戏结果 - 点数: {dice_result}, 奖励: {reward_points}, 净收益: {net_points}, 更新后积分: {new_points}")

            # 更新用户积分
            conn = get_db_connection()
            if not conn:
                return jsonify({
                    'success': False,
                    'message': '数据库连接失败'
                }), 500

            with conn.cursor() as cursor:
                sql = """UPDATE users SET points = points + %s WHERE steamid = %s"""
                cursor.execute(sql, (net_points, steamid))

                # 记录游戏日志
                sql = """INSERT INTO action_logs (steamid, log_time, action_type, details) VALUES (%s, %s, %s, %s)"""
                details = json.dumps({
                    'game_type': 'dice',
                    'cost': game_cost,
                    'dice_result': dice_result,
                    'reward': reward_points,
                    'net_points': net_points,
                    'points_before': current_points,
                    'points_after': new_points
                })
                cursor.execute(sql, (steamid, int(time.time() * 1000), 'game', details))

            conn.commit()
            conn.close()

            # 返回游戏结果JSON
            return jsonify({
                'success': success,
                'dice_result': dice_result,
                'cost': game_cost,
                'reward': reward_points,
                'net_points': net_points,
                'points_before': current_points,
                'points_after': new_points,
                'message': f'掷出了{dice_result}点，{"获得" if success else "失去"}{abs(net_points)}积分'
            })

        # 批量游戏处理
        else:
            results = []
            total_win = 0
            total_lose = 0
            win_count = 0
            lose_count = 0

            # 生成所有骰子结果
            for i in range(times):
                dice_result = random.randint(1, 6)

                # 计算奖励积分
                if dice_result == 6:
                    reward_points = game_cost * 2  # 双倍积分
                elif dice_result == 3:
                    reward_points = int(game_cost * 1.25)  # 返还部分积分
                else:
                    reward_points = 0

                # 计算净收益
                net_points = reward_points - game_cost
                success = net_points > 0

                # 统计输赢
                if success:
                    win_count += 1
                    total_win += net_points
                else:
                    lose_count += 1
                    total_lose += abs(net_points)

                # 添加到结果列表
                results.append({
                    'dice_result': dice_result,
                    'reward': reward_points,
                    'cost': game_cost,
                    'net_points': net_points,
                    'success': success
                })

            # 计算总净收益
            total_net_points = total_win - total_lose
            new_points = current_points + total_net_points

            logger.info(f"批量骰子游戏结果 - 次数: {times}, 赢: {win_count}, 输: {lose_count}, 净收益: {total_net_points}, 更新后积分: {new_points}")

            # 更新用户积分
            conn = get_db_connection()
            if not conn:
                return jsonify({
                    'success': False,
                    'message': '数据库连接失败'
                }), 500

            with conn.cursor() as cursor:
                sql = """UPDATE users SET points = points + %s WHERE steamid = %s"""
                cursor.execute(sql, (total_net_points, steamid))

                # 记录游戏日志
                sql = """INSERT INTO action_logs (steamid, log_time, action_type, details) VALUES (%s, %s, %s, %s)"""
                details = json.dumps({
                    'game_type': 'dice_batch',
                    'times': times,
                    'cost_per_game': game_cost,
                    'total_cost': total_cost,
                    'win_count': win_count,
                    'lose_count': lose_count,
                    'total_win': total_win,
                    'total_lose': total_lose,
                    'total_net_points': total_net_points,
                    'points_before': current_points,
                    'points_after': new_points
                })
                cursor.execute(sql, (steamid, int(time.time() * 1000), 'game', details))

            conn.commit()
            conn.close()

            # 返回批量游戏结果
            return jsonify({
                'results': results,
                'summary': {
                    'win_count': win_count,
                    'lose_count': lose_count,
                    'total_win': total_win,
                    'total_lose': total_lose,
                    'total_net_points': total_net_points,
                    'points_before': current_points,
                    'points_after': new_points
                }
            })

    except Exception as e:
        logger.error(f"骰子游戏处理出错: {e}")
        return jsonify({
            'success': False,
            'message': '游戏处理出错，请稍后再试'
        }), 500

def clear_user_cache(steamid):
    """清除特定用户的缓存"""
    if steamid in USER_CACHE:
        del USER_CACHE[steamid]

    if steamid in USER_CACHE_TIMESTAMPS:
        del USER_CACHE_TIMESTAMPS[steamid]

# 添加自定义过滤器
@app.template_filter('format_number')
def format_number(value):
    """将数字格式化为包含千位分隔符的字符串"""
    return f"{int(value):,}"

# 创建必要的数据表
def create_necessary_tables():
    """创建必要的数据表，如果它们不存在"""
    conn = get_db_connection()
    if not conn:
        logger.error("无法连接到数据库创建表")
        return False

    try:
        with conn.cursor() as cursor:
            # 创建用户仓库表
            sql = """
            CREATE TABLE IF NOT EXISTS user_inventory (
                id INT PRIMARY KEY AUTO_INCREMENT,
                steamid VARCHAR(50) NOT NULL,
                item_name VARCHAR(255) NOT NULL,
                item_id VARCHAR(255),
                quantity INT NOT NULL DEFAULT 1,
                purchase_date DATETIME NOT NULL,
                used BOOLEAN NOT NULL DEFAULT FALSE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
            """
            cursor.execute(sql)

            # 创建购买记录表
            sql = """
            CREATE TABLE IF NOT EXISTS purchase_history (
                id INT PRIMARY KEY AUTO_INCREMENT,
                steamid VARCHAR(50) NOT NULL,
                item_name VARCHAR(255) NOT NULL,
                item_id VARCHAR(255),
                quantity INT NOT NULL DEFAULT 1,
                points_spent INT NOT NULL,
                purchase_date DATETIME NOT NULL
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
            """
            cursor.execute(sql)

            # 检查users表是否有vip_level字段，如果没有则添加
            sql = """
            SELECT COUNT(*) as count FROM information_schema.columns
            WHERE table_schema = DATABASE()
            AND table_name = 'users'
            AND column_name = 'vip_level';
            """
            cursor.execute(sql)
            result = cursor.fetchone()
            if result and result['count'] == 0:
                try:
                    sql = "ALTER TABLE users ADD COLUMN vip_level INT DEFAULT 0 COMMENT 'VIP等级，0表示非VIP，1-9表示VIP等级';"
                    cursor.execute(sql)
                    logger.info("成功添加vip_level字段到users表")
                except Exception as e:
                    logger.warning(f"添加vip_level字段失败，可能已存在: {e}")

            # 创建VIP等级表
            sql = """
            CREATE TABLE IF NOT EXISTS vip_levels (
                level INT NOT NULL PRIMARY KEY,
                points_required INT NOT NULL COMMENT '升级所需积分',
                points_bonus_rate FLOAT NOT NULL COMMENT '积分奖励倍率',
                discount_rate FLOAT NOT NULL COMMENT '购物折扣率',
                checkin_bonus INT NOT NULL COMMENT '签到奖励积分'
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
            """
            cursor.execute(sql)

            # 检查vip_levels表是否有数据，如果没有则插入
            sql = "SELECT COUNT(*) as count FROM vip_levels;"
            cursor.execute(sql)
            result = cursor.fetchone()
            if result and result['count'] == 0:
                # 插入VIP等级数据
                sql = """
                INSERT INTO vip_levels (level, points_required, points_bonus_rate, discount_rate, checkin_bonus)
                VALUES
                (1, 200000, 1.2, 0.9, 1200),
                (2, 400000, 1.4, 0.8, 1400),
                (3, 600000, 1.6, 0.7, 1600),
                (4, 800000, 1.8, 0.6, 1800),
                (5, 1000000, 2.0, 0.5, 2000),
                (6, 1200000, 2.2, 0.4, 2200),
                (7, 1400000, 2.4, 0.3, 2400),
                (8, 1600000, 2.6, 0.2, 2600),
                (9, 1800000, 2.8, 0.1, 2800);
                """
                cursor.execute(sql)
                logger.info("成功插入VIP等级数据")

            # 创建通行证系统相关表
            # 1. 通行证等级配置表
            sql = """
            CREATE TABLE IF NOT EXISTS battlepass_levels (
                level INT NOT NULL PRIMARY KEY,
                exp_required INT NOT NULL COMMENT '升级到此等级所需总经验',
                points_reward INT NOT NULL DEFAULT 0 COMMENT '升级奖励积分',
                created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='通行证等级配置表';
            """
            cursor.execute(sql)

            # 2. 通行证物品奖励表
            sql = """
            CREATE TABLE IF NOT EXISTS battlepass_rewards (
                id INT NOT NULL AUTO_INCREMENT PRIMARY KEY,
                level INT NOT NULL COMMENT '等级',
                item_id VARCHAR(255) DEFAULT NULL COMMENT '物品ID',
                item_name VARCHAR(255) NOT NULL COMMENT '物品名称',
                quantity INT NOT NULL DEFAULT 1 COMMENT '数量',
                reward_type ENUM('points', 'item') NOT NULL DEFAULT 'item' COMMENT '奖励类型：积分或物品',
                is_active TINYINT(1) NOT NULL DEFAULT 1 COMMENT '是否激活',
                created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_level (level)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='通行证物品奖励表';
            """
            cursor.execute(sql)

            # 3. 用户通行证进度表
            sql = """
            CREATE TABLE IF NOT EXISTS user_battlepass (
                steamid VARCHAR(255) NOT NULL PRIMARY KEY,
                current_level INT NOT NULL DEFAULT 1 COMMENT '当前等级',
                current_exp INT NOT NULL DEFAULT 0 COMMENT '当前经验',
                total_exp INT NOT NULL DEFAULT 0 COMMENT '总经验',
                last_quest_date DATE DEFAULT NULL COMMENT '最后完成任务日期',
                daily_quest_count INT NOT NULL DEFAULT 0 COMMENT '今日完成任务次数',
                created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (steamid) REFERENCES users(steamid) ON DELETE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户通行证进度表';
            """
            cursor.execute(sql)

            # 4. 任务完成记录表
            sql = """
            CREATE TABLE IF NOT EXISTS quest_completion_logs (
                id INT NOT NULL AUTO_INCREMENT PRIMARY KEY,
                steamid VARCHAR(255) NOT NULL,
                quest_name VARCHAR(255) NOT NULL COMMENT '任务名称',
                exp_gained INT NOT NULL COMMENT '获得经验',
                completed_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                INDEX idx_steamid_date (steamid, completed_at),
                FOREIGN KEY (steamid) REFERENCES users(steamid) ON DELETE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='任务完成记录表';
            """
            cursor.execute(sql)

            # 5. 通行证奖励领取记录表
            sql = """
            CREATE TABLE IF NOT EXISTS battlepass_reward_claims (
                id INT NOT NULL AUTO_INCREMENT PRIMARY KEY,
                steamid VARCHAR(255) NOT NULL,
                level INT NOT NULL COMMENT '等级',
                reward_id INT NOT NULL COMMENT '奖励ID',
                item_name VARCHAR(255) NOT NULL COMMENT '物品名称',
                quantity INT NOT NULL DEFAULT 1 COMMENT '数量',
                claimed_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                INDEX idx_steamid (steamid),
                FOREIGN KEY (steamid) REFERENCES users(steamid) ON DELETE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='通行证奖励领取记录表';
            """
            cursor.execute(sql)

            # 插入通行证系统配置
            sql = """
            INSERT INTO system_config (config_key, config_value) VALUES
            ('battlepass_max_level', '50'),
            ('battlepass_daily_quest_limit', '20'),
            ('battlepass_quest_exp', '1000'),
            ('battlepass_enabled', '1')
            ON DUPLICATE KEY UPDATE config_value = VALUES(config_value)
            """
            cursor.execute(sql)

            # 检查是否需要插入默认等级配置
            sql = "SELECT COUNT(*) as count FROM battlepass_levels"
            cursor.execute(sql)
            result = cursor.fetchone()
            if result and result['count'] == 0:
                # 插入默认通行证等级配置（1-50级）
                levels_data = []
                for level in range(1, 51):
                    if level == 1:
                        exp_required = 0
                        points_reward = 0
                    else:
                        # 经验需求递增公式：每级需要更多经验
                        exp_required = (level - 1) * 1000 + ((level - 1) * (level - 2) // 2) * 500
                        # 积分奖励：每5级有额外奖励
                        if level % 10 == 0:
                            points_reward = level * 200  # 10级倍数给更多奖励
                        elif level % 5 == 0:
                            points_reward = level * 100  # 5级倍数给中等奖励
                        else:
                            points_reward = 1000  # 普通等级给基础奖励
                    levels_data.append((level, exp_required, points_reward))

                sql = """INSERT INTO battlepass_levels (level, exp_required, points_reward) VALUES (%s, %s, %s)"""
                cursor.executemany(sql, levels_data)
                logger.info("成功插入通行证等级配置")

            # 创建VIP购买记录表
            sql = """
            CREATE TABLE IF NOT EXISTS vip_purchase_logs (
                id INT NOT NULL AUTO_INCREMENT PRIMARY KEY,
                steamid VARCHAR(255) NOT NULL,
                purchase_time DATETIME NOT NULL,
                vip_level INT NOT NULL,
                points_spent INT NOT NULL
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
            """
            cursor.execute(sql)

        conn.commit()
        logger.info("成功创建必要的数据表")
        return True
    except Exception as e:
        logger.error(f"创建数据表失败: {e}")
        return False
    finally:
        conn.close()

# 在应用启动时创建必要的表
create_necessary_tables()

@app.route('/buy_item', methods=['POST'])
def buy_item():
    """处理购买物品请求（单个物品）"""
    # 检查用户是否已登录
    if 'steamid' not in session:
        return jsonify({'success': False, 'message': '请先登录再购买物品'})

    steamid = session.get('steamid')
    user = get_user_by_steamid(steamid)

    if not user:
        return jsonify({'success': False, 'message': '用户信息获取失败'})

    # 获取请求数据
    data = request.json
    item_id = data.get('item_id')
    item_name = data.get('item_name')
    quantity = int(data.get('quantity', 1))

    # 计算VIP折扣价格
    original_price = int(data.get('original_price', 0))
    vip_level = user.get('vip_level', 0) if isinstance(user, dict) else 0
    total_price = calculate_discounted_price(original_price, vip_level) * quantity

    # 验证数据
    if not item_name or quantity < 1 or total_price < 0:
        return jsonify({'success': False, 'message': '无效的购买请求'})

    # 检查用户积分是否足够
    if user['points'] < total_price:
        return jsonify({'success': False, 'message': '积分不足，无法完成购买'})

    conn = get_db_connection()
    if not conn:
        return jsonify({'success': False, 'message': '数据库连接失败'})

    try:
        # 开始事务
        conn.begin()

        # 1. 扣除用户积分
        with conn.cursor() as cursor:
            sql = "UPDATE users SET points = points - %s WHERE steamid = %s"
            cursor.execute(sql, (total_price, steamid))

        # 2. 添加到用户仓库
        now = datetime.now()
        with conn.cursor() as cursor:
            sql = """INSERT INTO user_inventory
                   (steamid, item_name, item_id, quantity, purchase_date)
                   VALUES (%s, %s, %s, %s, %s)"""
            cursor.execute(sql, (steamid, item_name, item_id, quantity, now))

        # 3. 记录购买历史
        with conn.cursor() as cursor:
            sql = """INSERT INTO purchase_history
                   (steamid, item_name, item_id, quantity, points_spent, purchase_date)
                   VALUES (%s, %s, %s, %s, %s, %s)"""
            cursor.execute(sql, (steamid, item_name, item_id, quantity, total_price, now))

        # 提交事务
        conn.commit()

        # 清除用户缓存，强制重新获取最新的用户信息
        clear_user_cache(steamid)

        # 获取更新后的用户积分
        updated_user = get_user_by_steamid(steamid, use_cache=False)
        updated_points = updated_user['points'] if updated_user else 0

        return jsonify({
            'success': True,
            'message': f'成功购买 {quantity} 个 {item_name}',
            'user_points': updated_points
        })

    except Exception as e:
        # 发生错误时回滚事务
        conn.rollback()
        logger.error(f"购买物品失败: {e}")
        return jsonify({'success': False, 'message': f'购买失败: {str(e)}'})
    finally:
        conn.close()

@app.route('/buy_items', methods=['POST'])
def buy_items():
    """处理购买物品请求（批量购买，购物车结算）"""
    # 检查用户是否已登录
    if 'steamid' not in session:
        return jsonify({'success': False, 'message': '请先登录再购买物品'})

    steamid = session.get('steamid')
    user = get_user_by_steamid(steamid)

    if not user:
        return jsonify({'success': False, 'message': '用户信息获取失败'})

    # 获取请求数据
    data = request.json
    items = data.get('items', [])

    # 计算VIP折扣后的总价
    vip_level = user.get('vip_level', 0) if isinstance(user, dict) else 0
    total_price = 0

    # 对每个物品应用VIP折扣
    for item in items:
        original_price = int(item.get('original_price', 0))
        quantity = int(item.get('quantity', 1))
        discounted_price = calculate_discounted_price(original_price, vip_level)
        item['price'] = discounted_price  # 更新物品的折扣价格
        total_price += discounted_price * quantity

    # 验证数据
    if not items or len(items) == 0:
        return jsonify({'success': False, 'message': '购物车为空'})

    if total_price <= 0:
        return jsonify({'success': False, 'message': '无效的购买请求'})

    # 检查用户积分是否足够
    if user['points'] < total_price:
        return jsonify({'success': False, 'message': '积分不足，无法完成购买'})

    conn = get_db_connection()
    if not conn:
        return jsonify({'success': False, 'message': '数据库连接失败'})

    try:
        # 开始事务
        conn.begin()

        # 1. 扣除用户积分
        with conn.cursor() as cursor:
            sql = "UPDATE users SET points = points - %s WHERE steamid = %s"
            cursor.execute(sql, (total_price, steamid))

        # 2. 添加到用户仓库并记录购买历史
        now = datetime.now()
        for item in items:
            item_id = item.get('id')
            item_name = item.get('name')
            quantity = int(item.get('quantity', 1))
            price = int(item.get('price', 0))
            item_total = price * quantity

            # 添加到用户仓库
            with conn.cursor() as cursor:
                sql = """INSERT INTO user_inventory
                       (steamid, item_name, item_id, quantity, purchase_date)
                       VALUES (%s, %s, %s, %s, %s)"""
                cursor.execute(sql, (steamid, item_name, item_id, quantity, now))

            # 记录购买历史
            with conn.cursor() as cursor:
                sql = """INSERT INTO purchase_history
                       (steamid, item_name, item_id, quantity, points_spent, purchase_date)
                       VALUES (%s, %s, %s, %s, %s, %s)"""
                cursor.execute(sql, (steamid, item_name, item_id, quantity, item_total, now))

        # 提交事务
        conn.commit()

        # 清除用户缓存，强制重新获取最新的用户信息
        clear_user_cache(steamid)

        # 获取更新后的用户积分
        updated_user = get_user_by_steamid(steamid, use_cache=False)
        updated_points = updated_user['points'] if updated_user else 0

        # 准备返回的已购买物品信息
        purchased_items = []
        for item in items:
            purchased_items.append({
                'id': item.get('id'),
                'name': item.get('name'),
                'quantity': item.get('quantity', 1)
            })

        return jsonify({
            'success': True,
            'message': f'成功购买 {len(items)} 种物品',
            'user_points': updated_points,
            'purchased_items': purchased_items
        })

    except Exception as e:
        # 发生错误时回滚事务
        conn.rollback()
        logger.error(f"批量购买物品失败: {e}")
        return jsonify({'success': False, 'message': f'购买失败: {str(e)}'})
    finally:
        conn.close()

@app.route('/inventory')
def inventory():
    """用户物品仓库页面"""
    # 检查用户是否已登录
    if 'steamid' not in session:
        flash('请先登录再查看仓库')
        return redirect(url_for('login'))

    steamid = session.get('steamid')
    user = get_user_by_steamid(steamid)

    if not user:
        session.pop('steamid', None)
        session.pop('username', None)
        return redirect(url_for('login'))

    # 获取是否显示已发货物品的参数，默认不显示
    show_shipped = request.args.get('show_shipped', '0') == '1'

    # 获取用户仓库中的物品
    inventory_items = []
    grouped_items = {}
    conn = get_db_connection()

    if conn:
        try:
            with conn.cursor() as cursor:
                # 根据是否显示已发货物品构建SQL查询
                if show_shipped:
                    sql = """SELECT id, item_name, item_id, quantity, purchase_date, used
                            FROM user_inventory
                            WHERE steamid = %s
                            ORDER BY purchase_date DESC"""
                    cursor.execute(sql, (steamid,))
                else:
                    sql = """SELECT id, item_name, item_id, quantity, purchase_date, used
                            FROM user_inventory
                            WHERE steamid = %s AND used = 0
                            ORDER BY purchase_date DESC"""
                    cursor.execute(sql, (steamid,))

                raw_items = cursor.fetchall()

                # 获取所有物品的数据以查找图片路径
                data = get_all_data()
                image_paths = {}

                if data and 'packages' in data:
                    # 创建物品名称到图片路径的映射
                    for package in data['packages']:
                        image_paths[package.get('packageName')] = package.get('imagePath', '/static/uploads/default-item.jpg')

                # 按物品名称分组
                for item in raw_items:
                    item_name = item['item_name']

                    # 设置图片路径
                    item['image_path'] = image_paths.get(item_name, '/static/uploads/default-item.jpg')

                    # 如果是相同物品，合并到一组
                    if item_name not in grouped_items:
                        grouped_items[item_name] = {
                            'item_name': item_name,
                            'image_path': item['image_path'],
                            'purchase_date': item['purchase_date'],  # 使用第一个物品的购买日期
                            'total_quantity': 0,
                            'all_used': True,  # 默认所有物品都已使用
                            'any_used': False,  # 默认没有物品已使用
                            'unused_item_ids': [],  # 未使用的物品ID列表
                            'unused_items': []  # 未使用的物品对象列表，包含数量信息
                        }

                    # 添加到组中并更新总数量
                    grouped_items[item_name]['total_quantity'] += item['quantity']

                    # 更新使用状态
                    if not item['used']:
                        grouped_items[item_name]['all_used'] = False
                        grouped_items[item_name]['unused_item_ids'].append(str(item['id']))
                        # 添加包含数量信息的物品对象
                        grouped_items[item_name]['unused_items'].append({
                            'id': str(item['id']),
                            'quantity': item['quantity']
                        })
                    if item['used']:
                        grouped_items[item_name]['any_used'] = True

                # 转换为列表
                inventory_items = list(grouped_items.values())

        except Exception as e:
            logger.error(f"获取用户仓库物品失败: {e}")
        finally:
            conn.close()

    # 获取购买历史
    purchase_history = []
    conn = get_db_connection()

    if conn:
        try:
            with conn.cursor() as cursor:
                sql = """SELECT id, item_name, item_id, quantity, points_spent, purchase_date
                        FROM purchase_history
                        WHERE steamid = %s
                        ORDER BY purchase_date DESC
                        LIMIT 20"""
                cursor.execute(sql, (steamid,))
                purchase_history = cursor.fetchall()
        except Exception as e:
            logger.error(f"获取用户购买历史失败: {e}")
        finally:
            conn.close()

    return render_template('inventory.html',
                          user=user,
                          inventory_items=inventory_items,
                          purchase_history=purchase_history,
                          show_shipped=show_shipped,
                          active_page='inventory')

@app.route('/check_ws_status', methods=['GET'])
def check_ws_status():
    """检查WebSocket连接状态"""
    return jsonify({
        'connected': WS_CONNECTION_STATUS['connected'],
        'timestamp': int(time.time())
    })

@app.route('/mark_as_used', methods=['POST'])
def mark_as_used():
    """标记物品为已使用并发送发货请求，支持选择发货数量"""
    # 检查用户是否已登录
    if 'steamid' not in session:
        return jsonify({'success': False, 'message': '请先登录'})

    steamid = session.get('steamid')
    data = request.json
    item_ids = data.get('item_ids', [])
    single_item_id = data.get('item_id')
    ship_item = data.get('ship_item', False)  # 是否需要发货
    quantities = data.get('quantities', {})  # 新增：物品ID到发货数量的映射

    # 记录请求数据以便调试
    logger.info(f"Mark as used request: item_ids={item_ids}, single_item_id={single_item_id}, ship_item={ship_item}")
    logger.info(f"Quantities map: {quantities}")

    # 支持单个物品ID或多个物品ID的批量操作
    if single_item_id and not item_ids:
        item_ids = [single_item_id]

    if not item_ids:
        return jsonify({'success': False, 'message': '无效的请求，未提供物品ID'})

    # 如果需要发货，检查WebSocket连接状态
    if ship_item and not WS_CONNECTION_STATUS['connected']:
        return jsonify({'success': False, 'message': '发货服务器连接失败，请稍后再试'})

    conn = get_db_connection()
    if not conn:
        return jsonify({'success': False, 'message': '数据库连接失败'})

    try:
        shipped_items = []
        failed_items = []

        total_shipped_quantity = 0

        # 如果有数量映射，只处理有数量的物品
        if quantities and len(quantities) > 0:
            logger.info(f"Using quantities map with {len(quantities)} items")
            # 只处理有数量的物品ID
            item_ids_to_process = [item_id for item_id in item_ids if str(item_id) in quantities and int(quantities.get(str(item_id), 0)) > 0]
            logger.info(f"Processing only these item IDs with quantities: {item_ids_to_process}")
        else:
            # 如果没有数量映射，处理所有物品ID
            item_ids_to_process = item_ids
            logger.info(f"No quantities map, processing all item IDs: {item_ids_to_process}")

        for item_id in item_ids_to_process:
            # 验证这个物品确实属于当前用户，并获取数量信息
            with conn.cursor() as cursor:
                sql = """SELECT id, item_name, item_id as original_item_id, quantity, purchase_date FROM user_inventory
                       WHERE id = %s AND steamid = %s AND used = 0"""
                cursor.execute(sql, (item_id, steamid))
                item = cursor.fetchone()

                if not item:
                    failed_items.append(item_id)
                    logger.warning(f"Item {item_id} not found or already used")
                    continue

                item_name = item['item_name']
                original_item_id = item['original_item_id']
                item_quantity = item['quantity']
                purchase_date = item['purchase_date']

                # 获取要发货的数量
                if quantities and str(item_id) in quantities:
                    # 使用指定的数量
                    try:
                        ship_quantity = int(quantities[str(item_id)])
                        logger.info(f"Using specified quantity {ship_quantity} for item {item_id}")
                    except (ValueError, TypeError):
                        ship_quantity = item_quantity
                        logger.error(f"Invalid quantity format for item {item_id}: {quantities[str(item_id)]}, using full quantity {item_quantity}")
                else:
                    # 如果没有指定数量，使用物品总数量
                    ship_quantity = item_quantity
                    logger.info(f"No quantity specified for item {item_id}, using full quantity {item_quantity}")

                # 确保发货数量不超过物品总数量
                ship_quantity = min(int(ship_quantity), item_quantity)

                # 如果发货数量小于等于0，跳过此物品
                if ship_quantity <= 0:
                    continue

                # 如果需要发货，发送发货请求
                if ship_item:
                    # 对于每个物品，发送指定数量次发货请求
                    shipping_success = True
                    for _ in range(ship_quantity):
                        success = send_shipping_message(steamid, item_name)
                        if not success:
                            shipping_success = False
                            break

                    if not shipping_success:
                        failed_items.append(item_id)
                        continue

                # 处理部分发货的情况
                if ship_quantity < item_quantity:
                    # 更新原记录的数量
                    with conn.cursor() as cursor:
                        # 将原记录的数量减少
                        remaining_quantity = item_quantity - ship_quantity
                        sql = "UPDATE user_inventory SET quantity = %s WHERE id = %s"
                        cursor.execute(sql, (remaining_quantity, item_id))

                        # 创建一个新记录用于标记已使用的部分
                        sql = """INSERT INTO user_inventory
                               (steamid, item_name, item_id, quantity, purchase_date, used)
                               VALUES (%s, %s, %s, %s, %s, 1)"""
                        cursor.execute(sql, (steamid, item_name, original_item_id, ship_quantity, purchase_date))
                else:
                    # 全部发货，直接标记为已使用
                    with conn.cursor() as cursor:
                        sql = "UPDATE user_inventory SET used = 1 WHERE id = %s"
                        cursor.execute(sql, (item_id,))

                shipped_items.append({
                    'id': item_id,
                    'name': item_name,
                    'quantity': ship_quantity
                })

                # 累计总发货数量
                total_shipped_quantity += ship_quantity

        conn.commit()

        if not shipped_items and failed_items:
            return jsonify({
                'success': False,
                'message': '所有物品发货失败',
                'failed_items': failed_items
            })

        return jsonify({
            'success': True,
            'message': f'成功{"发货" if ship_item else "标记为已使用"} {total_shipped_quantity} 个物品' +
                      (f', {len(failed_items)} 个物品失败' if failed_items else ''),
            'shipped_items': shipped_items,
            'failed_items': failed_items,
            'shipped': ship_item,
            'total_quantity': total_shipped_quantity
        })

    except Exception as e:
        logger.error(f"标记物品已使用失败: {e}")
        return jsonify({'success': False, 'message': f'操作失败: {str(e)}'})
    finally:
        conn.close()

# 全局WebSocket连接状态
WS_CONNECTION_STATUS = {
    'connected': False,
    'client': None,
    'initialized': False,  # 标记是否已经发送过初始化消息
    'reconnecting': False  # 标记是否正在重连中
}

# 初始化WebSocket客户端
def init_websocket_client():
    """初始化WebSocket客户端，连接到发货服务器"""
    try:
        import websocket
        import threading
        import json
        import time

        # 发送初始化消息，告知服务器这是一个发货客户端
        def send_init_message(ws):
            # 如果已经发送过初始化消息，不再重复发送
            if WS_CONNECTION_STATUS['initialized']:
                logger.info("已经发送过初始化消息，不再重复发送")
                return

            try:
                init_message = {
                    'type': 'init',
                    'client_type': 'shipping_client',
                    'timestamp': int(time.time())
                }
                ws.send(json.dumps(init_message))
                logger.info("已发送初始化消息到发货服务器")

                # 标记已经发送过初始化消息
                WS_CONNECTION_STATUS['initialized'] = True

                # 即使服务器没有回复，也认为连接已建立
                # 因为有些服务器可能不会回复初始化消息
                WS_CONNECTION_STATUS['connected'] = True
            except Exception as e:
                logger.error(f"发送初始化消息失败: {e}")
                # 如果发送初始化消息失败，设置连接状态为未连接
                WS_CONNECTION_STATUS['connected'] = False

        def on_message(ws, message):
            logger.info(f"收到WebSocket消息: {message}")
            try:
                data = json.loads(message)
                if data.get('type') == 'init_response' and data.get('status') == 'success':
                    logger.info("服务器已确认初始化消息")
                    WS_CONNECTION_STATUS['connected'] = True
            except Exception as e:
                logger.error(f"解析WebSocket消息失败: {e}")

        def on_error(ws, error):
            logger.error(f"WebSocket错误: {error}")
            WS_CONNECTION_STATUS['connected'] = False

        def on_close(ws, close_status_code, close_msg):
            logger.info(f"WebSocket连接关闭: {close_status_code} - {close_msg}")
            WS_CONNECTION_STATUS['connected'] = False

            # 如果已经在重连中，不再创建新的重连定时器
            if not WS_CONNECTION_STATUS['reconnecting']:
                WS_CONNECTION_STATUS['reconnecting'] = True
                logger.info("5秒后尝试重新连接...")

                def reconnect():
                    try:
                        connect_websocket()
                    finally:
                        # 无论重连成功与否，重置重连标志
                        WS_CONNECTION_STATUS['reconnecting'] = False

                # 使用守护线程进行重连，不影响主线程
                reconnect_thread = threading.Thread(target=reconnect)
                reconnect_thread.daemon = True
                reconnect_thread.start()

        def on_open(ws):
            logger.info("WebSocket连接已建立")
            # 设置连接状态为已连接
            WS_CONNECTION_STATUS['connected'] = True
            # 连接成功后发送初始化消息
            send_init_message(ws)

        def connect_websocket():
            # 如果已经连接成功且已发送初始化消息，不再重新连接
            if WS_CONNECTION_STATUS['connected'] and WS_CONNECTION_STATUS['initialized'] and not WS_CONNECTION_STATUS['reconnecting']:
                logger.info("已经连接成功且已发送初始化消息，不再重新连接")
                return WS_CONNECTION_STATUS['client']

            try:
                # 如果已经有连接，先关闭
                if WS_CONNECTION_STATUS['client'] and WS_CONNECTION_STATUS['client'].sock:
                    try:
                        WS_CONNECTION_STATUS['client'].close()
                    except:
                        pass

                ws = websocket.WebSocketApp("ws://127.0.0.1:9002",
                                          on_open=on_open,
                                          on_message=on_message,
                                          on_error=on_error,
                                          on_close=on_close)

                wst = threading.Thread(target=ws.run_forever)
                wst.daemon = True
                wst.start()
                logger.info("WebSocket客户端线程已启动")

                # 更新全局连接状态
                WS_CONNECTION_STATUS['client'] = ws

                return ws
            except Exception as e:
                logger.error(f"创建WebSocket连接失败: {e}")
                WS_CONNECTION_STATUS['connected'] = False
                return None

        # 连接WebSocket
        return connect_websocket()
    except ImportError:
        logger.error("未安装websocket-client库，请使用pip install websocket-client安装")
        return None
    except Exception as e:
        logger.error(f"初始化WebSocket客户端失败: {e}")
        return None

# 发送发货消息的函数
def send_shipping_message(steamid, item_name):
    """通过WebSocket发送发货消息"""
    if not WS_CONNECTION_STATUS['connected'] or not WS_CONNECTION_STATUS['client']:
        logger.error("发货失败: WebSocket连接未建立")
        return False

    try:
        import json
        shipping_data = {
            'steamid': steamid,
            'item_name': item_name
        }
        WS_CONNECTION_STATUS['client'].send(json.dumps(shipping_data))
        logger.info(f"已发送发货消息: {shipping_data}")
        return True
    except Exception as e:
        logger.error(f"发送发货消息失败: {e}")
        return False

@app.route('/delete_user_inventory', methods=['POST'])
def delete_user_inventory():
    """删除指定用户的全部仓库物品"""
    # 检查是否是管理员
    if 'steamid' not in session:
        return jsonify({'success': False, 'message': '请先登录'})

    current_user = get_user_by_steamid(session['steamid'])
    if not current_user or not current_user.get('is_admin'):
        return jsonify({'success': False, 'message': '权限不足'})

    # 获取要删除的用户steamid
    data = request.json
    if not data or 'steamid' not in data:
        return jsonify({'success': False, 'message': '缺少必要参数'})

    target_steamid = data['steamid']

    # 验证目标用户是否存在
    target_user = get_user_by_steamid(target_steamid)
    if not target_user:
        return jsonify({'success': False, 'message': '目标用户不存在'})

    # 不允许删除管理员的仓库
    if target_user.get('is_admin'):
        return jsonify({'success': False, 'message': '不能删除管理员的仓库'})

    conn = get_db_connection()
    if not conn:
        return jsonify({'success': False, 'message': '数据库连接失败'})

    try:
        with conn.cursor() as cursor:
            # 开始事务
            conn.begin()

            # 先获取用户仓库物品数量
            sql = "SELECT COUNT(*) as count FROM user_inventory WHERE steamid = %s"
            cursor.execute(sql, (target_steamid,))
            result = cursor.fetchone()
            items_count = result['count'] if result else 0

            if items_count == 0:
                return jsonify({'success': False, 'message': '该用户仓库中没有物品'})

            # 删除用户的所有仓库物品
            sql = "DELETE FROM user_inventory WHERE steamid = %s"
            cursor.execute(sql, (target_steamid,))

            # 记录操作日志
            log_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            details = {
                'action': 'delete_inventory',
                'items_deleted': items_count,
                'admin_steamid': session['steamid'],
                'admin_username': current_user.get('username', 'unknown'),
                'target_username': target_user.get('username', 'unknown')
            }
            
            # 创建操作日志表（如果不存在）
            sql = """CREATE TABLE IF NOT EXISTS `admin_action_logs` (
                `id` int(11) NOT NULL AUTO_INCREMENT,
                `admin_steamid` varchar(255) NOT NULL,
                `affected_steamid` varchar(255) NOT NULL,
                `action_type` varchar(50) NOT NULL,
                `details` text,
                `created_at` datetime NOT NULL,
                PRIMARY KEY (`id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;"""
            cursor.execute(sql)

            # 插入日志
            sql = """INSERT INTO admin_action_logs 
                    (admin_steamid, affected_steamid, action_type, details, created_at)
                    VALUES (%s, %s, %s, %s, %s)"""
            cursor.execute(sql, (
                session['steamid'],
                target_steamid,
                'delete_inventory',
                json.dumps(details),
                log_time
            ))

            # 提交事务
            conn.commit()

            # 清除用户缓存
            clear_user_cache(target_steamid)

            # 记录日志
            logger.info(f"管理员 {current_user.get('username')} ({session['steamid']}) 删除了用户 {target_user.get('username')} ({target_steamid}) 的仓库物品，共 {items_count} 个物品")

            return jsonify({
                'success': True,
                'message': f'成功删除用户 {target_user.get("username")} 的所有仓库物品（共 {items_count} 个）'
            })

    except Exception as e:
        # 发生错误时回滚事务
        conn.rollback()
        logger.error(f"删除用户仓库物品失败: {e}")
        return jsonify({'success': False, 'message': f'删除失败: {str(e)}'})
    finally:
        conn.close()

if __name__ == '__main__':
    # 确保数据目录存在
    os.makedirs(LOCAL_DATA_DIR, exist_ok=True)
    os.makedirs("static/uploads", exist_ok=True)

    # 检查本地数据缓存是否存在
    if not os.path.exists(LOCAL_DATA_FILE):
        logger.warning(f"本地数据缓存不存在: {LOCAL_DATA_FILE}")
        logger.info("请确保sync_api.py已运行并成功完成首次同步")

    # 尝试初始化WebSocket客户端
    try:
        # 导入必要的模块
        import time

        # 检查是否安装了websocket-client库
        try:
            import websocket
            logger.info("正在初始化WebSocket客户端...")
            ws_client = init_websocket_client()
            if ws_client:
                logger.info("WebSocket客户端初始化成功")
                # 等待连接建立，最多等待3秒
                retry_count = 0
                max_retries = 3

                # 如果连接已经建立，直接跳过等待
                if WS_CONNECTION_STATUS['connected']:
                    logger.info("WebSocket连接已经建立，无需等待")
                else:
                    # 等待连接建立
                    while not WS_CONNECTION_STATUS['connected'] and retry_count < max_retries:
                        logger.info(f"等待WebSocket连接建立... (尝试 {retry_count+1}/{max_retries})")
                        time.sleep(1)
                        retry_count += 1

                        # 如果在等待过程中连接建立，立即跳出
                        if WS_CONNECTION_STATUS['connected']:
                            logger.info(f"连接已建立，在第{retry_count}次尝试后")
                            break

                if WS_CONNECTION_STATUS['connected']:
                    logger.info("WebSocket连接已建立，发货功能可用")
                else:
                    logger.warning("WebSocket连接未建立，发货功能可能无法正常工作")
            else:
                logger.warning("WebSocket客户端初始化失败，发货功能可能无法正常工作")
        except ImportError:
            logger.warning("未安装websocket-client库，发货功能将不可用。请使用pip install websocket-client安装")
    except Exception as e:
        logger.error(f"初始化WebSocket客户端时出错: {e}")

    # VIP系统相关函数
    def get_vip_level_info(level):
        """获取指定VIP等级的配置信息"""
        conn = get_db_connection()
        if not conn:
            return None

        try:
            with conn.cursor() as cursor:
                sql = "SELECT * FROM vip_levels WHERE level = %s"
                cursor.execute(sql, (level,))
                vip_info = cursor.fetchone()
                return vip_info
        except Exception as e:
            logger.error(f"获取VIP等级信息失败: {e}")
            return None
        finally:
            conn.close()

    def get_all_vip_levels():
        """获取所有VIP等级的配置信息"""
        conn = get_db_connection()
        if not conn:
            return []

        try:
            with conn.cursor() as cursor:
                sql = "SELECT * FROM vip_levels ORDER BY level"
                cursor.execute(sql)
                vip_levels = cursor.fetchall()
                return vip_levels
        except Exception as e:
            logger.error(f"获取所有VIP等级信息失败: {e}")
            return []
        finally:
            conn.close()

    def upgrade_vip_level(steamid, target_level):
        """升级用户的VIP等级"""
        conn = get_db_connection()
        if not conn:
            return False, "数据库连接失败"

        try:
            # 获取用户信息
            with conn.cursor() as cursor:
                sql = "SELECT * FROM users WHERE steamid = %s"
                cursor.execute(sql, (steamid,))
                user = cursor.fetchone()

                if not user:
                    return False, "用户不存在"

                current_level = user.get('vip_level', 0)
                current_points = user.get('points', 0)

                # 获取目标VIP等级信息
                vip_info = get_vip_level_info(target_level)
                if not vip_info:
                    return False, "VIP等级信息不存在"

                # 检查是否是升级操作
                if target_level <= current_level:
                    return False, "不能降级或重复购买相同等级"

                # 检查积分是否足够
                points_required = vip_info['points_required']
                if current_points < points_required:
                    return False, f"积分不足，需要{points_required}积分"

                # 扣除积分并更新VIP等级
                with conn.cursor() as cursor:
                    # 更新用户积分和VIP等级
                    sql = """UPDATE users
                            SET points = points - %s,
                                vip_level = %s,
                                is_vip = 1
                            WHERE steamid = %s"""
                    cursor.execute(sql, (points_required, target_level, steamid))

                    # 记录VIP购买日志
                    sql = """INSERT INTO vip_purchase_logs
                            (steamid, purchase_time, vip_level, points_spent)
                            VALUES (%s, NOW(), %s, %s)"""
                    cursor.execute(sql, (steamid, target_level, points_required))

                    # 记录操作日志
                    sql = """INSERT INTO action_logs
                            (steamid, log_time, action_type, details)
                            VALUES (%s, %s, %s, %s)"""
                    details = {
                        'action': 'upgrade_vip',
                        'from_level': current_level,
                        'to_level': target_level,
                        'points_spent': points_required
                    }
                    cursor.execute(sql, (steamid, int(time.time() * 1000), 'vip_upgrade', json.dumps(details)))

                conn.commit()
                return True, f"成功升级到VIP{target_level}，消费{points_required}积分"
        except Exception as e:
            conn.rollback()
            logger.error(f"升级VIP等级失败: {e}")
            return False, f"升级失败: {str(e)}"
        finally:
            conn.close()

    def calculate_discounted_price(original_price, vip_level):
        """根据VIP等级计算折扣后的价格"""
        if vip_level <= 0:
            return original_price

        vip_info = get_vip_level_info(vip_level)
        if not vip_info:
            return original_price

        discount_rate = vip_info['discount_rate']
        return int(original_price * discount_rate)

    def calculate_checkin_bonus(base_bonus, vip_level):
        """根据VIP等级计算签到奖励"""
        if vip_level <= 0:
            return base_bonus

        vip_info = get_vip_level_info(vip_level)
        if not vip_info:
            return base_bonus

        return vip_info['checkin_bonus']

    def calculate_points_bonus(base_points, vip_level):
        """根据VIP等级计算积分奖励倍率"""
        if vip_level <= 0:
            return base_points

        vip_info = get_vip_level_info(vip_level)
        if not vip_info:
            return base_points

        bonus_rate = vip_info['points_bonus_rate']
        return int(base_points * bonus_rate)

    # VIP系统相关路由

    @app.route('/vip_levels', methods=['GET'])
    def vip_levels_api():
        """获取所有VIP等级信息API"""
        vip_levels = get_all_vip_levels()
        return jsonify(vip_levels)

    @app.route('/upgrade_vip', methods=['POST'])
    def upgrade_vip_api():
        """升级VIP等级API"""
        if 'steamid' not in session:
            flash('请先登录')
            return redirect(url_for('login'))

        steamid = session.get('steamid')
        data = request.get_json()
        target_level = data.get('level')

        if not target_level or not isinstance(target_level, int) or target_level < 1 or target_level > 9:
            return jsonify({
                'success': False,
                'message': '无效的VIP等级'
            }), 400

        success, message = upgrade_vip_level(steamid, target_level)

        if success:
            flash(message)
            return jsonify({
                'success': True,
                'message': message
            })
        else:
            flash(message)
            return jsonify({
                'success': False,
                'message': message
            }), 400

    @app.route('/sponsor/upgrade/<int:vip_level>', methods=['POST'])
    def sponsor_upgrade(vip_level):
        """处理VIP升级请求"""
        if 'steamid' not in session:
            return jsonify({
                'success': False,
                'message': '请先登录'
            }), 401

        steamid = session.get('steamid')
        user = get_user_by_steamid(steamid)

        if not user:
            return jsonify({
                'success': False,
                'message': '用户不存在'
            }), 404

        # 调用现有的VIP升级函数
        success, message = upgrade_vip_level(steamid, vip_level)

        if success:
            # 清除用户缓存，确保获取最新数据
            clear_user_cache(steamid)
            return jsonify({
                'success': True,
                'message': message
            })
        else:
            return jsonify({
                'success': False,
                'message': message
            }), 400

    # 管理员页面路由
    @app.route('/admin')
    def admin():
        """管理员页面"""
        # 检查用户是否已登录
        if 'steamid' not in session:
            flash('请先登录')
            return redirect(url_for('login'))

        # 检查用户是否是管理员
        steamid = session.get('steamid')
        user = get_user_by_steamid(steamid)

        if not user or not user.get('is_admin'):
            flash('您没有管理员权限')
            return redirect(url_for('home'))

        # 获取所有VIP等级信息
        vip_levels = get_all_vip_levels()

        return render_template('admin.html',
                              active_page='admin',
                              vip_levels=vip_levels)

    # 管理员API - 搜索用户
    @app.route('/api/admin/search_users', methods=['GET'])
    def admin_search_users():
        """管理员API - 搜索用户"""
        # 检查用户是否已登录且是管理员
        if 'steamid' not in session:
            return jsonify({'success': False, 'message': '请先登录'}), 401

        steamid = session.get('steamid')
        user = get_user_by_steamid(steamid)

        if not user or not user.get('is_admin'):
            return jsonify({'success': False, 'message': '您没有管理员权限'}), 403

        # 获取搜索参数
        search_term = request.args.get('search', '')
        page = int(request.args.get('page', 1))
        limit = int(request.args.get('limit', 10))
        offset = (page - 1) * limit

        conn = get_db_connection()
        if not conn:
            return jsonify({'success': False, 'message': '数据库连接失败'}), 500

        try:
            with conn.cursor() as cursor:
                # 构建搜索SQL
                if search_term:
                    sql = """SELECT steamid, username, points, is_vip, vip_level, is_admin
                            FROM users
                            WHERE steamid LIKE %s OR username LIKE %s
                            ORDER BY points DESC
                            LIMIT %s OFFSET %s"""
                    search_param = f'%{search_term}%'
                    cursor.execute(sql, (search_param, search_param, limit, offset))
                else:
                    sql = """SELECT steamid, username, points, is_vip, vip_level, is_admin
                            FROM users
                            ORDER BY points DESC
                            LIMIT %s OFFSET %s"""
                    cursor.execute(sql, (limit, offset))

                users = cursor.fetchall()

                # 获取总用户数
                if search_term:
                    count_sql = """SELECT COUNT(*) as total
                                FROM users
                                WHERE steamid LIKE %s OR username LIKE %s"""
                    cursor.execute(count_sql, (search_param, search_param))
                else:
                    count_sql = "SELECT COUNT(*) as total FROM users"
                    cursor.execute(count_sql)

                total = cursor.fetchone()['total']
                total_pages = math.ceil(total / limit)

                return jsonify({
                    'success': True,
                    'users': users,
                    'pagination': {
                        'total': total,
                        'page': page,
                        'limit': limit,
                        'total_pages': total_pages
                    }
                })
        except Exception as e:
            logger.error(f"管理员搜索用户失败: {e}")
            return jsonify({'success': False, 'message': f'搜索失败: {str(e)}'}), 500
        finally:
            conn.close()

    # 管理员API - 获取用户详情
    @app.route('/api/admin/user/<steamid>', methods=['GET'])
    def admin_get_user(steamid):
        """管理员API - 获取用户详情"""
        # 检查用户是否已登录且是管理员
        if 'steamid' not in session:
            return jsonify({'success': False, 'message': '请先登录'}), 401

        admin_steamid = session.get('steamid')
        admin = get_user_by_steamid(admin_steamid)

        if not admin or not admin.get('is_admin'):
            return jsonify({'success': False, 'message': '您没有管理员权限'}), 403

        # 获取目标用户信息
        user = get_user_by_steamid(steamid, use_cache=False)

        if not user:
            return jsonify({'success': False, 'message': '用户不存在'}), 404

        return jsonify({
            'success': True,
            'user': user
        })

    # 管理员API - 更新用户信息
    @app.route('/api/admin/user/<steamid>', methods=['POST'])
    def admin_update_user(steamid):
        """管理员API - 更新用户信息"""
        # 检查用户是否已登录且是管理员
        if 'steamid' not in session:
            return jsonify({'success': False, 'message': '请先登录'}), 401

        admin_steamid = session.get('steamid')
        admin = get_user_by_steamid(admin_steamid)

        if not admin or not admin.get('is_admin'):
            return jsonify({'success': False, 'message': '您没有管理员权限'}), 403

        # 获取目标用户信息
        user = get_user_by_steamid(steamid, use_cache=False)

        if not user:
            return jsonify({'success': False, 'message': '用户不存在'}), 404

        # 获取要更新的字段
        data = request.json
        points = data.get('points')
        is_vip = data.get('is_vip')
        vip_level = data.get('vip_level')
        is_admin = data.get('is_admin')

        # 构建更新SQL
        update_fields = []
        params = []

        if points is not None:
            update_fields.append("points = %s")
            params.append(points)

        if is_vip is not None:
            update_fields.append("is_vip = %s")
            params.append(1 if is_vip else 0)

        if vip_level is not None:
            update_fields.append("vip_level = %s")
            params.append(vip_level)

        if is_admin is not None:
            update_fields.append("is_admin = %s")
            params.append(1 if is_admin else 0)

        if not update_fields:
            return jsonify({'success': False, 'message': '没有提供要更新的字段'}), 400

        conn = get_db_connection()
        if not conn:
            return jsonify({'success': False, 'message': '数据库连接失败'}), 500

        try:
            with conn.cursor() as cursor:
                # 更新用户信息
                sql = f"UPDATE users SET {', '.join(update_fields)} WHERE steamid = %s"
                params.append(steamid)
                cursor.execute(sql, params)

                # 记录管理员操作日志
                log_sql = """INSERT INTO admin_logs
                            (steamid, log_time, action, target_steamid, details)
                            VALUES (%s, %s, %s, %s, %s)"""

                log_time = int(time.time() * 1000)  # 毫秒时间戳
                action = "update_user"
                details = json.dumps(data)

                cursor.execute(log_sql, (admin_steamid, log_time, action, steamid, details))

                conn.commit()

                # 清除用户缓存
                clear_user_cache(steamid)

                # 获取更新后的用户信息
                updated_user = get_user_by_steamid(steamid, use_cache=False)

                return jsonify({
                    'success': True,
                    'message': '用户信息更新成功',
                    'user': updated_user
                })
        except Exception as e:
            conn.rollback()
            logger.error(f"管理员更新用户信息失败: {e}")
            return jsonify({'success': False, 'message': f'更新失败: {str(e)}'}), 500
        finally:
            conn.close()

    # 创建抽奖相关的数据库表
    conn = get_db_connection()
    if conn:
        try:
            with conn.cursor() as cursor:
                # 创建抽奖奖品表
                sql = """
                CREATE TABLE IF NOT EXISTS lottery_prizes (
                    id INT PRIMARY KEY AUTO_INCREMENT,
                    item_id VARCHAR(255) NOT NULL,
                    item_name VARCHAR(255) NOT NULL,
                    probability DECIMAL(10,4) NOT NULL COMMENT '中奖概率，例如1.5表示1.5%',
                    image_path VARCHAR(255) DEFAULT NULL,
                    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    created_by VARCHAR(255) NOT NULL COMMENT '创建者的steamid',
                    is_active BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否激活，可用于临时禁用某个奖品'
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
                """
                cursor.execute(sql)

                # 创建抽奖记录表
                sql = """
                CREATE TABLE IF NOT EXISTS lottery_logs (
                    id INT PRIMARY KEY AUTO_INCREMENT,
                    steamid VARCHAR(255) NOT NULL,
                    item_id VARCHAR(255) NOT NULL,
                    item_name VARCHAR(255) NOT NULL,
                    points_cost INT NOT NULL,
                    draw_time DATETIME NOT NULL,
                    draw_type VARCHAR(20) NOT NULL COMMENT '抽奖类型：single, ten, hundred'
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
                """
                cursor.execute(sql)

                # 创建系统配置表（如果不存在）
                sql = """
                CREATE TABLE IF NOT EXISTS system_config (
                    config_key VARCHAR(50) PRIMARY KEY,
                    config_value TEXT NOT NULL,
                    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
                """
                cursor.execute(sql)

            conn.commit()
            logger.info("抽奖相关数据库表创建成功")
        except Exception as e:
            logger.error(f"创建抽奖相关数据库表失败: {e}")
        finally:
            conn.close()

    # 导入抽奖蓝图并注册
    from lottery import lottery_bp

    # 注入必要的函数
    import lottery
    lottery.get_db_connection = get_db_connection
    lottery.get_user_by_steamid = get_user_by_steamid
    lottery.clear_user_cache = clear_user_cache
    lottery.get_all_data = get_all_data

    # 注册抽奖蓝图
    app.register_blueprint(lottery_bp)

    # 导入外部API抽奖蓝图并注册
    from api_lottery import api_lottery_bp

    # 注入必要的函数
    import api_lottery
    api_lottery.get_db_connection = get_db_connection
    api_lottery.get_user_by_steamid = get_user_by_steamid
    api_lottery.clear_user_cache = clear_user_cache

    # 注册外部API抽奖蓝图
    app.register_blueprint(api_lottery_bp)
    logger.info("外部API抽奖接口已注册")

    # 导入游戏机器人API蓝图并注册
    from game_bot_api import game_bot_api as bot_api_blueprint

    # 注入必要的函数
    import game_bot_api as bot_api_module
    bot_api_module.get_db_connection = get_db_connection
    bot_api_module.get_user_by_steamid = get_user_by_steamid
    bot_api_module.clear_user_cache = clear_user_cache
    bot_api_module.get_all_data = get_all_data

    # 如果有WebSocket发货功能，注入发送消息的函数
    def send_ship_message(message):
        """发送发货消息到WebSocket服务器"""
        try:
            if 'ws_client' in globals() and ws_client and WS_CONNECTION_STATUS['connected']:
                # 构建发货消息
                ship_message = {
                    'type': 'ship',
                    'steamid': message.get('steamid'),
                    'item_name': message.get('item_name'),
                    'quantity': message.get('quantity', 1)
                }

                # 发送消息
                ws_client.send(json.dumps(ship_message))
                logger.info(f"已发送发货消息: {ship_message}")
                return True
            else:
                logger.warning("发送发货消息失败: WebSocket客户端未连接")
                return False
        except Exception as e:
            logger.error(f"发送发货消息失败: {e}")
            return False

    # 注入发送消息的函数
    bot_api_module.send_ship_message = send_ship_message

    # 注册游戏机器人API蓝图
    app.register_blueprint(bot_api_blueprint)
    logger.info("游戏机器人API接口已注册")

    app.run(debug=True, host='0.0.0.0', port=80)