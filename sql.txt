-- 创建 users 表 (用户信息表)
CREATE TABLE users (
    steamid VARCHAR(255) PRIMARY KEY,  -- Steam ID，作为主键，唯一标识用户
    username VARCHAR(255) UNIQUE,     -- 用户名，唯一
    password VARCHAR(255),            -- 密码（请务必加密存储！）
    is_signed_in TINYINT(1) DEFAULT 0, -- 是否签到 (0: 否, 1: 是)
    is_online TINYINT(1) DEFAULT 0,    -- 是否在线 (0: 否, 1: 是)
    position VARCHAR(255),            -- 职位（文本类型）
    is_admin TINYINT(1) DEFAULT 0,     -- 是否管理员 (0: 否, 1: 是)
    kills INT DEFAULT 0,              -- 击杀数
    deaths INT DEFAULT 0,             -- 死亡数
    kd FLOAT,                         -- KD 值 (击杀数/死亡数，可以计算得出，也可以存储)
    points INT DEFAULT 0,              -- 积分
    gift_received_1 INT DEFAULT 0,    -- 礼包1领取次数
    gift_received_2 INT DEFAULT 0,    -- 礼包2领取次数
    gift_received_3 INT DEFAULT 0,    -- 礼包3领取次数
    gift_received_4 INT DEFAULT 0,    -- 礼包4领取次数
    gift_received_5 INT DEFAULT 0,    -- 礼包5领取次数
    other_gift_received INT DEFAULT 0, -- 其他类型礼包领取次数
    skill_cd_1 BIGINT DEFAULT 0,       -- 技能1 CD (时间戳，毫秒)
    skill_cd_2 BIGINT DEFAULT 0,       -- 技能2 CD (时间戳，毫秒)
    skill_cd_3 BIGINT DEFAULT 0,       -- 技能3 CD (时间戳，毫秒)
    skill_cd_4 BIGINT DEFAULT 0,       -- 技能4 CD (时间戳，毫秒)
    skill_cd_5 BIGINT DEFAULT 0,       -- 技能5 CD (时间戳，毫秒)
    user_rank INT DEFAULT NULL,        -- 用户排名 (根据日志中的排名信息更新)
	country VARCHAR(255) DEFAULT NULL,  -- 国家
    season_points INT DEFAULT 0,       -- 赛季积分
    is_vip TINYINT(1) DEFAULT 0,        -- 是否VIP (0: 否, 1: 是)
	xyz TEXT DEFAULT NULL              -- 用于存储 XYZ 信息的文本列
);
-- 创建 gift_logs 表 (礼包领取记录表)
CREATE TABLE gift_logs (
    id INT PRIMARY KEY AUTO_INCREMENT, -- 自增ID，唯一标识每次领取记录
    steamid VARCHAR(255),            -- 领取用户的 Steam ID
    gift_type INT,                   -- 礼包类型 (例如：1-5 代表五种礼包, 0 代表其他)
    received_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP -- 领取时间（自动记录当前时间）
);

-- 创建 login_logs 表 (登录日志表)
CREATE TABLE login_logs (
    id INT PRIMARY KEY AUTO_INCREMENT, -- 自增ID，唯一标识每条登录记录
    steamid VARCHAR(255),            -- 登录用户的 Steam ID
    log_time BIGINT,                 -- 登录时间 (时间戳，毫秒)
    ip_address VARCHAR(50) DEFAULT NULL -- 登录 IP 地址 (可选)
);

-- 创建 chat_logs 表 (聊天日志表)
CREATE TABLE chat_logs (
    id INT PRIMARY KEY AUTO_INCREMENT, -- 自增ID，唯一标识每条聊天记录
    steamid VARCHAR(255),            -- 发送消息用户的 Steam ID
    log_time BIGINT,                 -- 聊天时间 (时间戳，毫秒)
    message TEXT,                    -- 聊天消息内容
    channel VARCHAR(50) DEFAULT NULL  -- 聊天频道 (可选，例如：全局、队伍、私聊等)
);

-- 创建 kill_logs 表 (击杀日志表)
CREATE TABLE kill_logs (
    id INT PRIMARY KEY AUTO_INCREMENT, -- 自增ID，唯一标识每条击杀记录
    steamid VARCHAR(255),            -- 击杀者的 Steam ID
    victimid VARCHAR(255),          -- 被击杀者的 Steam ID
    log_time BIGINT,                 -- 击杀时间 (时间戳，毫秒)
    weapon VARCHAR(50),              -- 使用的武器
    distance FLOAT DEFAULT NULL     -- 击杀距离（可选）
);

-- 创建 admin_logs 表 (管理员日志表)
CREATE TABLE admin_logs (
    id INT PRIMARY KEY AUTO_INCREMENT, -- 自增ID，唯一标识每条管理员操作记录
    steamid VARCHAR(255),            -- 执行操作的管理员的 Steam ID
    log_time BIGINT,                 -- 操作时间 (时间戳，毫秒)
    action VARCHAR(255),             -- 管理员执行的操作 (例如：封禁、踢出、修改配置等)
    target_steamid VARCHAR(255) DEFAULT NULL, -- 操作的目标用户 Steam ID (可选)
    details TEXT DEFAULT NULL        -- 操作的详细信息 (可选，例如封禁原因、修改的配置等)
);

-- 创建 action_logs 表 (行为日志表)
CREATE TABLE action_logs (
    id INT PRIMARY KEY AUTO_INCREMENT, -- 自增ID，唯一标识每条行为记录
    steamid VARCHAR(255),            -- 触发行为的用户的 Steam ID
    log_time BIGINT,                 -- 行为发生时间 (时间戳，毫秒)
    action_type VARCHAR(50),        -- 行为类型 (例如：移动、跳跃、开火、使用道具等，需自定义)
    details TEXT DEFAULT NULL        -- 行为的详细信息 (可选，根据行为类型存储不同的信息)
);