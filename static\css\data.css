/* 数据页面样式 */
.data-container {
    margin-top: 30px;
    margin-bottom: 40px;
}

.data-header {
    text-align: center;
    margin-bottom: 40px;
}

.data-header h1 {
    font-size: 2.2rem;
    font-weight: 700;
    margin-bottom: 16px;
    color: var(--text-primary);
}

.data-header p {
    font-size: 1.1rem;
    color: var(--text-secondary);
    max-width: 700px;
    margin: 0 auto;
}

/* 排行榜部分 */
.leaderboard-section {
    background-color: var(--card-bg);
    border-radius: var(--border-radius);
    padding: 30px;
    margin-bottom: 40px;
    border: 1px solid #333;
    box-shadow: var(--box-shadow);
}

.section-header {
    margin-bottom: 25px;
    text-align: center;
}

.section-header h2 {
    font-size: 1.6rem;
    font-weight: 600;
    margin-bottom: 10px;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    justify-content: center;
}

.section-header h2 i {
    margin-right: 10px;
    color: var(--primary-color);
}

.section-header p {
    color: var(--text-secondary);
    font-size: 1rem;
}

.leaderboard-table {
    overflow-x: auto;
}

.leaderboard-table table {
    width: 100%;
    border-collapse: collapse;
}

.leaderboard-table th, .leaderboard-table td {
    padding: 15px;
    text-align: left;
    border-bottom: 1px solid #333;
}

.leaderboard-table th {
    background-color: rgba(0, 0, 0, 0.2);
    color: var(--text-primary);
    font-weight: 600;
}

.leaderboard-table tr:hover {
    background-color: rgba(255, 255, 255, 0.05);
}

.leaderboard-table tr.current-user {
    background-color: rgba(0, 168, 255, 0.1);
}

.user-badges {
    display: flex;
    flex-wrap: wrap;
    gap: 5px;
}

.vip-badge, .admin-badge, .user-badge {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.8rem;
    font-weight: 600;
    display: inline-flex;
    align-items: center;
}

.vip-badge {
    background-color: #ffc107;
    color: #000;
}

.admin-badge {
    background-color: #f44336;
    color: #fff;
}

.user-badge {
    background-color: #607d8b;
    color: #fff;
}

.vip-badge i, .admin-badge i, .user-badge i {
    margin-right: 4px;
}

/* 服务器统计部分 */
.stats-section {
    background-color: var(--card-bg);
    border-radius: var(--border-radius);
    padding: 30px;
    margin-top: 40px;
    border: 1px solid #333;
    box-shadow: var(--box-shadow);
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.stat-card {
    background-color: rgba(0, 0, 0, 0.2);
    border-radius: 10px;
    padding: 20px;
    display: flex;
    align-items: center;
    border: 1px solid #444;
    transition: var(--transition);
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);
    border-color: var(--primary-color);
}

.stat-icon {
    width: 50px;
    height: 50px;
    background-color: #673ab7;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    box-shadow: 0 5px 15px rgba(103, 58, 183, 0.3);
}

.stat-icon i {
    font-size: 20px;
    color: white;
}

.stat-info {
    flex: 1;
}

.stat-info h3 {
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 5px;
    color: var(--text-secondary);
}

.stat-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-primary);
}

@media (max-width: 768px) {
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 576px) {
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .leaderboard-table th, .leaderboard-table td {
        padding: 8px 5px;
        font-size: 0.75rem;
    }
    
    .vip-badge, .admin-badge, .user-badge {
        padding: 3px 6px;
        font-size: 0.7rem;
    }
}
