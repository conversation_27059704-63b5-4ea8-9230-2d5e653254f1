-- 创建抽奖奖品表
CREATE TABLE lottery_prizes (
    id INT PRIMARY KEY AUTO_INCREMENT,
    item_id VARCHAR(255) NOT NULL,
    item_name VARCHAR(255) NOT NULL,
    probability DECIMAL(10,4) NOT NULL COMMENT '中奖概率，例如1.5表示1.5%',
    image_path VARCHAR(255) DEFAULT NULL,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by VARCHAR(255) NOT NULL COMMENT '创建者的steamid',
    is_active BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否激活，可用于临时禁用某个奖品'
);

-- 创建抽奖记录表
CREATE TABLE lottery_logs (
    id INT PRIMARY KEY AUTO_INCREMENT,
    steamid VARCHAR(255) NOT NULL,
    item_id VARCHAR(255) NOT NULL,
    item_name VARCHAR(255) NOT NULL,
    points_cost INT NOT NULL,
    draw_time DATETIME NOT NULL,
    draw_type VARCHAR(20) NOT NULL COMMENT '抽奖类型：single, ten, hundred'
);
