import os
import requests
import re
from urllib.parse import urlparse
from pathlib import Path

# 要下载的资源文件
RESOURCES = [
    # Bootstrap CSS
    {
        'url': 'https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/css/bootstrap.min.css',
        'local_path': 'static/vendor/bootstrap/css/bootstrap.min.css'
    },
    # Font Awesome CSS
    {
        'url': 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.2.0/css/all.min.css',
        'local_path': 'static/vendor/fontawesome/css/all.min.css'
    },
    # jQuery JS
    {
        'url': 'https://code.jquery.com/jquery-3.5.1.min.js',
        'local_path': 'static/vendor/jquery/jquery-3.5.1.min.js'
    },
    # Bootstrap JS
    {
        'url': 'https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/js/bootstrap.bundle.min.js',
        'local_path': 'static/vendor/bootstrap/js/bootstrap.bundle.min.js'
    },
    # Font Awesome webfonts (只保留已成功下载的文件)
    {
        'url': 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.2.0/webfonts/fa-solid-900.woff2',
        'local_path': 'static/vendor/fontawesome/webfonts/fa-solid-900.woff2'
    },
    {
        'url': 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.2.0/webfonts/fa-solid-900.ttf',
        'local_path': 'static/vendor/fontawesome/webfonts/fa-solid-900.ttf'
    },
    {
        'url': 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.2.0/webfonts/fa-regular-400.woff2',
        'local_path': 'static/vendor/fontawesome/webfonts/fa-regular-400.woff2'
    },
    {
        'url': 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.2.0/webfonts/fa-regular-400.ttf',
        'local_path': 'static/vendor/fontawesome/webfonts/fa-regular-400.ttf'
    },
    {
        'url': 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.2.0/webfonts/fa-brands-400.woff2',
        'local_path': 'static/vendor/fontawesome/webfonts/fa-brands-400.woff2'
    },
    {
        'url': 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.2.0/webfonts/fa-brands-400.ttf',
        'local_path': 'static/vendor/fontawesome/webfonts/fa-brands-400.ttf'
    },
]

# 需要更新的模板文件
TEMPLATE_FILES = [
    'templates/layout.html',
    'templates/index.html',
    'templates/fun.html',
    'templates/set_password.html',
    'templates/login.html',
    'templates/profile.html',
]

def download_file(url, local_path):
    """下载文件到本地"""
    try:
        # 确保目录存在
        os.makedirs(os.path.dirname(local_path), exist_ok=True)
        
        # 检查文件是否已存在
        if os.path.exists(local_path):
            print(f"文件已存在: {local_path}")
            return True
        
        # 下载文件
        print(f"正在下载: {url} -> {local_path}")
        response = requests.get(url, stream=True)
        response.raise_for_status()  # 检查响应状态
        
        with open(local_path, 'wb') as f:
            for chunk in response.iter_content(chunk_size=8192):
                if chunk:
                    f.write(chunk)
        
        print(f"下载完成: {local_path}")
        return True
    except Exception as e:
        print(f"下载失败 {url}: {e}")
        return False

def update_fontawesome_css():
    """更新Font Awesome CSS中的字体文件路径"""
    fa_css_path = 'static/vendor/fontawesome/css/all.min.css'
    if not os.path.exists(fa_css_path):
        print(f"Font Awesome CSS文件不存在: {fa_css_path}")
        return
    
    with open(fa_css_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 替换字体文件路径
    updated_content = re.sub(
        r'url\(["\']?(?:https?:)?//(?:.*?)/webfonts/([^)"\']+)["\']?\)', 
        r'url(../webfonts/\1)', 
        content
    )
    
    with open(fa_css_path, 'w', encoding='utf-8') as f:
        f.write(updated_content)
    
    print(f"已更新Font Awesome CSS中的字体文件路径: {fa_css_path}")

def update_html_files():
    """更新HTML文件中的资源引用"""
    replacements = {
        'https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/css/bootstrap.min.css': 
            "{{ url_for('static', filename='vendor/bootstrap/css/bootstrap.min.css') }}",
        'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.2.0/css/all.min.css': 
            "{{ url_for('static', filename='vendor/fontawesome/css/all.min.css') }}",
        'https://code.jquery.com/jquery-3.5.1.min.js': 
            "{{ url_for('static', filename='vendor/jquery/jquery-3.5.1.min.js') }}",
        'https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/js/bootstrap.bundle.min.js': 
            "{{ url_for('static', filename='vendor/bootstrap/js/bootstrap.bundle.min.js') }}"
    }
    
    for file_path in TEMPLATE_FILES:
        if not os.path.exists(file_path):
            print(f"模板文件不存在: {file_path}")
            continue
        
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        updated_content = content
        for old_url, new_url in replacements.items():
            updated_content = updated_content.replace(old_url, new_url)
        
        if updated_content != content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(updated_content)
            print(f"已更新文件: {file_path}")
        else:
            print(f"文件无需更新: {file_path}")

def main():
    # 下载所有资源
    for resource in RESOURCES:
        download_file(resource['url'], resource['local_path'])
    
    # 更新Font Awesome CSS中的字体文件路径
    update_fontawesome_css()
    
    # 更新HTML文件中的资源引用
    update_html_files()
    
    print("所有资源已下载并更新完成！")

if __name__ == "__main__":
    main() 