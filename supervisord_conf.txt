# Supervisor配置示例
# 如果您的系统使用supervisor而不是systemd，可以使用此配置
# 将此文件保存为 /etc/supervisor/conf.d/scum-shop.conf

[program:scum-shop-sync]
command=python /path/to/application/sync_api.py
directory=/path/to/application
user=your_username
autostart=true
autorestart=true
redirect_stderr=true
stdout_logfile=/path/to/application/logs/sync.log
environment=PYTHONUNBUFFERED=1

[program:scum-shop-web]
command=python /path/to/application/app.py
directory=/path/to/application
user=your_username
autostart=true
autorestart=true
redirect_stderr=true
stdout_logfile=/path/to/application/logs/app.log
environment=PYTHONUNBUFFERED=1

[group:scum-shop]
programs=scum-shop-sync,scum-shop-web
priority=999

# 使用方法：
# 1. 安装supervisor: sudo apt-get install supervisor
# 2. 修改上面的用户名和路径
# 3. 将此文件复制到 /etc/supervisor/conf.d/scum-shop.conf
# 4. 重新加载配置并启动服务：
#    sudo supervisorctl reread
#    sudo supervisorctl update
#    sudo supervisorctl start scum-shop:*
#
# 查看状态：
#    sudo supervisorctl status
#
# 查看日志：
#    sudo supervisorctl tail scum-shop-sync
#    sudo supervisorctl tail scum-shop-web 