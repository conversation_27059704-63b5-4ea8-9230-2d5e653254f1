{% extends "base.html" %}

{% block title %}游戏数据 - SCUM物品代码网站{% endblock %}

{% block content %}
<div class="data-container">
    <div class="data-header">
        <h1><i class="fas fa-chart-bar"></i> 游戏数据</h1>
        <p>查看游戏数据和排行榜信息</p>
    </div>

    <div class="data-content">
        <!-- 左侧目录导航 -->
        <div class="data-sidebar">
            <div class="sidebar-header">
                <h3><i class="fas fa-list"></i> 快速导航</h3>
            </div>
            <ul class="sidebar-menu">
                <li><a href="#realtime-data"><i class="fas fa-bolt"></i> 实时数据</a></li>
                <li><a href="#points-leaderboard"><i class="fas fa-trophy"></i> 积分排行榜</a></li>
                <li><a href="#recycle-leaderboard"><i class="fas fa-recycle"></i> 回收排行榜</a></li>
                <li><a href="#server-stats"><i class="fas fa-chart-line"></i> 服务器统计</a></li>
            </ul>
        </div>

        <!-- 右侧内容区域 -->
        <div class="data-main-content">

    <!-- 实时数据统计部分 -->
    <div id="realtime-data" class="realtime-section">
        <div class="section-header">
            <h2><i class="fas fa-bolt"></i> 实时数据</h2>
            <p>查看最新的游戏活动</p>
        </div>
        <div class="activity-list">
            {% for activity in recent_activities %}
                <div class="activity-item {% if activity.type == 'kill' %}activity-kill{% else %}activity-recycle{% endif %}">
                    <div class="activity-icon">
                        {% if activity.type == 'kill' %}
                            <i class="fas fa-skull"></i>
                        {% else %}
                            <i class="fas fa-recycle"></i>
                        {% endif %}
                    </div>
                    <div class="activity-content">
                        {% if activity.type == 'kill' %}
                            <div class="activity-title">
                                <span class="killer">{{ activity.killer_name }}</span> 击杀了 <span class="victim">{{ activity.victim_name }}</span>
                            </div>
                            <div class="activity-details">
                                使用 <span class="weapon">{{ activity.weapon }}</span> 距离: {{ activity.distance|round(1) }} 米
                            </div>
                        {% else %}
                            <div class="activity-title">
                                <span class="recycler">{{ activity.username }}</span> 回收了 <span class="item">{{ activity.item_name }}</span>
                            </div>
                            <div class="activity-details">
                                数量: {{ activity.quantity }} 个, 获得 {{ activity.points_earned }} 积分
                            </div>
                        {% endif %}
                        <div class="activity-time">
                            {{ activity.time.strftime('%Y-%m-%d %H:%M:%S') }}
                        </div>
                    </div>
                </div>
            {% endfor %}
        </div>
    </div>

    <!-- 排行榜部分 -->
    <div id="points-leaderboard" class="leaderboard-section">
        <div class="section-header">
            <h2><i class="fas fa-trophy"></i> 积分排行榜</h2>
            <p>查看积分最高的用户</p>
        </div>
        <div class="leaderboard-table">
            <table>
                <thead>
                    <tr>
                        <th>排名</th>
                        <th>用户</th>
                        <th>积分</th>
                        <th>击杀/死亡</th>
                        <th>状态</th>
                    </tr>
                </thead>
                <tbody>
                    {% for user in top_users %}
                    <tr {% if user.steamid == session.get('steamid') %}class="current-user"{% endif %}>
                        <td>{{ loop.index }}</td>
                        <td>{{ user.username }}</td>
                        <td>{{ user.points }}</td>
                        <td>{{ user.kills }}/{{ user.deaths }}</td>
                        <td>
                            <div class="user-badges">
                                {% if user.is_admin %}
                                <span class="admin-badge"><i class="fas fa-shield-alt"></i> 管理员</span>
                                {% endif %}

                                {% if user.is_vip %}
                                <span class="vip-badge"><i class="fas fa-crown"></i> VIP{{ user.vip_level }}</span>
                                {% endif %}

                                {% if not user.is_admin and not user.is_vip %}
                                <span class="user-badge"><i class="fas fa-user"></i> 用户</span>
                                {% endif %}
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>

    <!-- 回收排行榜部分 -->
    <div id="recycle-leaderboard" class="leaderboard-section">
        <div class="section-header">
            <h2><i class="fas fa-recycle"></i> 回收排行榜</h2>
            <p>查看回收物品最多的用户</p>
        </div>
        <div class="leaderboard-table">
            <table>
                <thead>
                    <tr>
                        <th>排名</th>
                        <th>用户</th>
                        <th>物品名称</th>
                        <th>回收数量</th>
                        <th>获得积分</th>
                    </tr>
                </thead>
                <tbody>
                    {% for recycler in top_recyclers %}
                    <tr {% if recycler.steamid == session.get('steamid') %}class="current-user"{% endif %}>
                        <td>{{ loop.index }}</td>
                        <td>{{ recycler.username }}</td>
                        <td>{{ recycler.item_name }}</td>
                        <td>{{ recycler.total_recycled }}</td>
                        <td>{{ recycler.total_points }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>

    <!-- 服务器统计部分 -->
    <div id="server-stats" class="stats-section">
        <div class="section-header">
            <h2><i class="fas fa-chart-line"></i> 服务器统计</h2>
            <p>查看服务器数据统计</p>
        </div>
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-users"></i>
                </div>
                <div class="stat-info">
                    <h3>注册用户</h3>
                    <p class="stat-value">{{ total_users }}</p>
                </div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-skull"></i>
                </div>
                <div class="stat-info">
                    <h3>总击杀数</h3>
                    <p class="stat-value">{{ total_kills }}</p>
                </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // 获取所有导航链接
        const navLinks = document.querySelectorAll('.sidebar-menu a');

        // 获取所有内容区域
        const sections = document.querySelectorAll('#realtime-data, #points-leaderboard, #recycle-leaderboard, #server-stats');

        // 添加滚动事件监听
        window.addEventListener('scroll', function() {
            let current = '';

            // 检测当前滚动位置，确定当前可见的区域
            sections.forEach(section => {
                const sectionTop = section.offsetTop;
                const sectionHeight = section.offsetHeight;

                if (window.pageYOffset >= sectionTop - 100) {
                    current = section.getAttribute('id');
                }
            });

            // 更新导航链接的活动状态
            navLinks.forEach(link => {
                link.classList.remove('active');
                if (link.getAttribute('href') === '#' + current) {
                    link.classList.add('active');
                }
            });
        });

        // 点击导航链接时平滑滚动
        navLinks.forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();

                const targetId = this.getAttribute('href');
                const targetElement = document.querySelector(targetId);

                if (targetElement) {
                    window.scrollTo({
                        top: targetElement.offsetTop - 20,
                        behavior: 'smooth'
                    });
                }
            });
        });
    });
</script>
{% endblock %}

{% block head_extra %}
<style>
    .data-container {
        margin-top: 30px;
        margin-bottom: 40px;
        max-width: 1400px;
        margin-left: auto;
        margin-right: auto;
        padding: 0 15px;
    }

    /* 布局样式 */
    .data-content {
        display: flex;
        margin-top: 30px;
        gap: 30px;
    }

    .data-sidebar {
        width: 250px;
        flex-shrink: 0;
    }

    .data-main-content {
        flex: 1;
    }

    /* 侧边栏样式 */
    .sidebar-header {
        margin-bottom: 15px;
        padding-bottom: 10px;
        border-bottom: 1px solid #444;
    }

    .sidebar-header h3 {
        font-size: 1.2rem;
        font-weight: 600;
        color: var(--text-primary);
        display: flex;
        align-items: center;
    }

    .sidebar-header h3 i {
        margin-right: 8px;
        color: #673ab7;
    }

    .sidebar-menu {
        list-style: none;
        padding: 0;
        margin: 0;
    }

    .sidebar-menu li {
        margin-bottom: 10px;
    }

    .sidebar-menu a {
        display: flex;
        align-items: center;
        padding: 10px 15px;
        border-radius: 8px;
        color: var(--text-secondary);
        text-decoration: none;
        transition: all 0.2s ease;
        background-color: rgba(0, 0, 0, 0.2);
        border: 1px solid #333;
    }

    .sidebar-menu a:hover, .sidebar-menu a.active {
        background-color: rgba(103, 58, 183, 0.1);
        color: var(--text-primary);
        border-color: #673ab7;
        transform: translateX(5px);
    }

    .sidebar-menu a.active {
        background-color: rgba(103, 58, 183, 0.2);
        border-left: 3px solid #673ab7;
        font-weight: bold;
    }

    .sidebar-menu a i {
        margin-right: 10px;
        width: 20px;
        text-align: center;
    }

    /* 响应式设计 */
    @media (max-width: 992px) {
        .data-content {
            flex-direction: column;
        }

        .data-sidebar {
            width: 100%;
            margin-bottom: 20px;
        }

        .sidebar-menu {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
        }

        .sidebar-menu li {
            margin-bottom: 0;
            flex-grow: 1;
        }
    }

    .data-header {
        text-align: center;
        margin-bottom: 40px;
    }

    .data-header h1 {
        font-size: 2.2rem;
        font-weight: 700;
        margin-bottom: 16px;
        color: var(--text-primary);
    }

    .data-header p {
        font-size: 1.1rem;
        color: var(--text-secondary);
        max-width: 700px;
        margin: 0 auto;
    }

    /* 服务器统计部分 */
    .stats-section {
        background-color: var(--card-bg);
        border-radius: var(--border-radius);
        padding: 30px;
        margin-top: 40px;
        border: 1px solid #333;
        box-shadow: var(--box-shadow);
    }

    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
        gap: 20px;
        margin-top: 20px;
    }

    .stat-card {
        background-color: rgba(0, 0, 0, 0.2);
        border-radius: 10px;
        padding: 20px;
        display: flex;
        align-items: center;
        border: 1px solid #444;
        transition: var(--transition);
    }

    .stat-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);
        border-color: var(--primary-color);
    }

    .stat-icon {
        width: 50px;
        height: 50px;
        background-color: #673ab7;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 15px;
        box-shadow: 0 5px 15px rgba(103, 58, 183, 0.3);
    }

    .stat-icon i {
        font-size: 20px;
        color: white;
    }

    .stat-info {
        flex: 1;
    }

    .stat-info h3 {
        font-size: 1rem;
        font-weight: 600;
        margin-bottom: 5px;
        color: var(--text-secondary);
    }

    .stat-value {
        font-size: 1.5rem;
        font-weight: 700;
        color: var(--text-primary);
    }

    @media (max-width: 768px) {
        .stats-grid {
            grid-template-columns: repeat(2, 1fr);
        }
    }

    @media (max-width: 576px) {
        .stats-grid {
            grid-template-columns: 1fr;
        }
    }

    /* 平滑滚动效果 */
    html {
        scroll-behavior: smooth;
    }

    /* 滚动偏移量，让锚点链接跳转时不被导航栏遮挡 */
    #realtime-data, #points-leaderboard, #recycle-leaderboard, #server-stats {
        scroll-margin-top: 20px;
    }

    /* 实时数据统计样式 */
    .realtime-section {
        background-color: var(--card-bg);
        border-radius: var(--border-radius);
        padding: 30px;
        margin-top: 40px;
        border: 1px solid #333;
        box-shadow: var(--box-shadow);
    }

    .activity-list {
        margin-top: 20px;
        display: flex;
        flex-direction: column;
        gap: 15px;
    }

    .activity-item {
        display: flex;
        background-color: rgba(0, 0, 0, 0.2);
        border-radius: 10px;
        padding: 15px;
        border: 1px solid #444;
        transition: var(--transition);
    }

    .activity-item:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 16px rgba(0, 0, 0, 0.3);
    }

    .activity-kill {
        border-left: 4px solid #ff5252;
    }

    .activity-recycle {
        border-left: 4px solid #4CAF50;
    }

    .activity-icon {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 15px;
        flex-shrink: 0;
    }

    .activity-kill .activity-icon {
        background-color: rgba(255, 82, 82, 0.2);
        color: #ff5252;
    }

    .activity-recycle .activity-icon {
        background-color: rgba(76, 175, 80, 0.2);
        color: #4CAF50;
    }

    .activity-icon i {
        font-size: 18px;
    }

    .activity-content {
        flex: 1;
    }

    .activity-title {
        font-size: 16px;
        font-weight: 600;
        margin-bottom: 5px;
        color: var(--text-primary);
    }

    .activity-details {
        font-size: 14px;
        color: var(--text-secondary);
        margin-bottom: 5px;
    }

    .activity-time {
        font-size: 12px;
        color: #777;
    }

    .killer, .recycler {
        color: #4CAF50;
        font-weight: bold;
    }

    .victim {
        color: #ff5252;
        font-weight: bold;
    }

    .weapon, .item {
        color: #ff9800;
        font-weight: bold;
    }
</style>
{% endblock %}
